# Copyright 2004-2025 <PERSON> <<EMAIL>>
#
# Permission is hereby granted, free of charge, to any person
# obtaining a copy of this software and associated documentation files
# (the "Software"), to deal in the Software without restriction,
# including without limitation the rights to use, copy, modify, merge,
# publish, distribute, sublicense, and/or sell copies of the Software,
# and to permit persons to whom the Software is furnished to do so,
# subject to the following conditions:
#
# The above copyright notice and this permission notice shall be
# included in all copies or substantial portions of the Software.
#
# THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
# EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
# MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
# NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE
# LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION
# OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION
# WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

# Checks for various abilities that might be taken away from us by
# redistributors.

init 1 python in ability:

    from store import config
    import store
    import store.updater as updater

    import os

    EXECUTABLES = [ "renpy.exe", "renpy.app", "renpy.sh" ]

    # can_distribute - True if we can distribute
    for i in EXECUTABLES:
        if not os.path.exists(os.path.join(config.renpy_base, i)):
            can_distribute = False
    else:
        can_distribute = True


    # can_update - True if we can update.
    can_update = updater.can_update() or (store.UPDATE_SIMULATE is not None)

