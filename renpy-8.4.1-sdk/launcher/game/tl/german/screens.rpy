
translate german strings:

    # screens.rpy:9
    old "## Styles"
    # Automatic translation.
    new "## Stile"

    # screens.rpy:87
    old "## In-game screens"
    new "## Spielinterne Bildschirme (Screens)"

    # screens.rpy:91
    old "## Say screen"
    new "## Dialog-Bildschirm"

    # screens.rpy:93
    old "## The say screen is used to display dialogue to the player. It takes two parameters, who and what, which are the name of the speaking character and the text to be displayed, respectively. (The who parameter can be None if no name is given.)"
    # Automatic translation.
    new "## Der say-Bildschirm wird verwendet, um dem Spieler einen Dialog anzuzeigen. Er benötigt zwei Parameter, who und what, die den Namen des sprechenden Charakters bzw. den anzuzeigenden Text darstellen. (Der who-Parameter kann None sein, wenn kein Name angegeben wird.)"

    # screens.rpy:98
    old "## This screen must create a text displayable with id \"what\", as <PERSON><PERSON><PERSON><PERSON> uses this to manage text display. It can also create displayables with id \"who\" and id \"window\" to apply style properties."
    # Automatic translation.
    new "## Dieser Bildschirm muss ein Text-Displayable mit der id \"what\" erstellen, da <PERSON>'<PERSON><PERSON> dieses zur Verwaltung der Textanzeige verwendet. Es kann auch Displayables mit der id \"who\" und id \"window\" erstellen, um Stileigenschaften anzuwenden."

    # screens.rpy:102
    old "## https://www.renpy.org/doc/html/screen_special.html#say"
    new "## https://www.renpy.org/doc/html/screen_special.html#say"

    # screens.rpy:169
    old "## Input screen"
    new "## Eingabe-Bildschirm"

    # screens.rpy:171
    old "## This screen is used to display renpy.input. The prompt parameter is used to pass a text prompt in."
    # Automatic translation.
    new "## Dieser Bildschirm wird zur Anzeige von renpy.input verwendet. Der Prompt-Parameter wird verwendet, um eine Text-Eingabeaufforderung zu übergeben."

    # screens.rpy:174
    old "## This screen must create an input displayable with id \"input\" to accept the various input parameters."
    # Automatic translation.
    new "## Dieser Bildschirm muss eine anzeigbare Eingabe mit der ID \"input\" erstellen, um die verschiedenen Eingabeparameter zu akzeptieren."

    # screens.rpy:177
    old "## http://www.renpy.org/doc/html/screen_special.html#input"
    new "## http://www.renpy.org/doc/html/screen_special.html#input"

    # screens.rpy:205
    old "## Choice screen"
    new "## Auswahl- oder Entscheidungs-Bildschirm"

    # screens.rpy:207
    old "## This screen is used to display the in-game choices presented by the menu statement. The one parameter, items, is a list of objects, each with caption and action fields."
    new "## Dieser Bildschirm wird verwendet, um die Auswahlmöglichkeiten im Spiel anzuzeigen, die von der Menüanweisung präsentiert werden. Der Parameter \"items\" ist eine Liste von Objekten, jedes mit Beschriftung und Aktionsfeldern."

    # screens.rpy:211
    old "## http://www.renpy.org/doc/html/screen_special.html#choice"
    new "## http://www.renpy.org/doc/html/screen_special.html#choice"

    # screens.rpy:221
    old "## When this is true, menu captions will be spoken by the narrator. When false, menu captions will be displayed as empty buttons."
    new "## Bei \"True\" werden die Menübeschriftungen vom Erzähler gesprochen. Bei \"False\" werden die Menübeschriftungen als leere Schaltflächen angezeigt."

    # screens.rpy:244
    old "## Quick Menu screen"
    new "## Schnellmenü-Bildschirm"

    # screens.rpy:246
    old "## The quick menu is displayed in-game to provide easy access to the out-of-game menus."
    # Automatic translation.
    new "## Das Schnellmenü wird im Spiel angezeigt, um einen einfachen Zugriff auf die Menüs außerhalb des Spiels zu ermöglichen."

    # screens.rpy:261
    old "Back"
    new "Zurück"

    # screens.rpy:262
    old "History"
    new "Dialogverlauf"

    # screens.rpy:263
    old "Skip"
    new "Vorspulen"

    # screens.rpy:264
    old "Auto"
    new "Auto"

    # screens.rpy:265
    old "Save"
    new "Speichern"

    # screens.rpy:266
    old "Q.Save"
    new "S.Speichern"

    # screens.rpy:267
    old "Q.Load"
    new "S.Laden"

    # screens.rpy:268
    old "Prefs"
    new "Optionen"

    # screens.rpy:271
    old "## This code ensures that the quick_menu screen is displayed in-game, whenever the player has not explicitly hidden the interface."
    # Automatic translation.
    new "## Dieser Code stellt sicher, dass der quick_menu-Bildschirm im Spiel angezeigt wird, wenn der Spieler die Benutzeroberfläche nicht explizit ausgeblendet hat."

    # screens.rpy:291
    old "## Navigation screen"
    # Automatic translation.
    new "## Navigationsbildschirm"

    # screens.rpy:293
    old "## This screen is included in the main and game menus, and provides navigation to other menus, and to start the game."
    # Automatic translation.
    new "## Dieser Bildschirm ist im Haupt- und Spielmenü enthalten und ermöglicht die Navigation zu anderen Menüs und den Start des Spiels."

    # screens.rpy:308
    old "Start"
    new "Start"

    # screens.rpy:316
    old "Load"
    # Automatic translation.
    new "Laden"

    # screens.rpy:318
    old "Preferences"
    new "Einstellungen"

    # screens.rpy:322
    old "End Replay"
    # Automatic translation.
    new "Wiederholung beenden"

    # screens.rpy:326
    old "Main Menu"
    new "Hauptmenü"

    # screens.rpy:328
    old "About"
    new "Über"

    # screens.rpy:332
    old "## Help isn't necessary or relevant to mobile devices."
    # Automatic translation.
    new "## Hilfe ist für mobile Geräte nicht notwendig oder relevant."

    # screens.rpy:333
    old "Help"
    new "Hilfe"

    # screens.rpy:335
    old "## The quit button is banned on iOS and unnecessary on Android."
    # Automatic translation.
    new "## Die Beenden-Schaltfläche ist auf iOS verboten und auf Android unnötig."

    # screens.rpy:336
    old "Quit"
    new "Beenden"

    # screens.rpy:350
    old "## Main Menu screen"
    # Automatic translation.
    new "## Hauptmenü-Bildschirm"

    # screens.rpy:352
    old "## Used to display the main menu when Ren'Py starts."
    # Automatic translation.
    new "## Wird verwendet, um das Hauptmenü beim Start von Ren'Py anzuzeigen."

    # screens.rpy:354
    old "## http://www.renpy.org/doc/html/screen_special.html#main-menu"
    new "## http://www.renpy.org/doc/html/screen_special.html#main-menu"

    # screens.rpy:369
    old "## The use statement includes another screen inside this one. The actual contents of the main menu are in the navigation screen."
    # Automatic translation.
    new "## Die Anweisung use enthält einen weiteren Bildschirm innerhalb dieses Bildschirms. Der eigentliche Inhalt des Hauptmenüs befindet sich auf dem Navigationsbildschirm."

    # screens.rpy:413
    old "## Game Menu screen"
    # Automatic translation.
    new "## Bildschirm Spielmenü"

    # screens.rpy:415
    old "## This lays out the basic common structure of a game menu screen. It's called with the screen title, and displays the background, title, and navigation."
    # Automatic translation.
    new "## Hier wird die allgemeine Grundstruktur eines Spielmenübildschirms festgelegt. Es wird mit dem Bildschirmtitel aufgerufen und zeigt den Hintergrund, den Titel und die Navigation an."

    # screens.rpy:476
    old "Return"
    new "Zurück"

    # screens.rpy:539
    old "## About screen"
    new "## Info-Bildschirm"

    # screens.rpy:541
    old "## This screen gives credit and copyright information about the game and Ren'Py."
    new "## Dieser Bildschirm enthält Würdigungen und Copyright-Informationen über das Spiel und Ren'Py."

    # screens.rpy:544
    old "## There's nothing special about this screen, and hence it also serves as an example of how to make a custom screen."
    # Automatic translation.
    new "## Dieser Bildschirm ist nichts Besonderes und dient daher auch als Beispiel für die Erstellung eines benutzerdefinierten Bildschirms."

    # screens.rpy:551
    old "## This use statement includes the game_menu screen inside this one. The vbox child is then included inside the viewport inside the game_menu screen."
    new "## Diese Anweisung schließt den Bildschirm game_menu in diesen Bildschirm ein. Das vbox-Kind wird dann in das Viewport innerhalb des game_menu-Bildschirms aufgenommen."

    # screens.rpy:561
    old "Version [config.version!t]\n"
    new "Version [config.version!t]\n"

    # screens.rpy:563
    old "## gui.about is usually set in options.rpy."
    # Automatic translation.
    new "## gui.about wird normalerweise in options.rpy eingestellt."

    # screens.rpy:567
    old "Made with {a=https://www.renpy.org/}Ren'Py{/a} [renpy.version_only].\n\n[renpy.license!t]"
    new "Erstellt mit {a=https://www.renpy.org/}Ren'Py{/a} [renpy.version_only].\n\n[renpy.license!t]"

    # screens.rpy:570
    old "## This is redefined in options.rpy to add text to the about screen."
    new "## Dies wird in options.rpy neu definiert, um dem Info-Bildschirm Text hinzuzufügen."

    # screens.rpy:582
    old "## Load and Save screens"
    new "## Spielstände-Bildschirm"

    # screens.rpy:584
    old "## These screens are responsible for letting the player save the game and load it again. Since they share nearly everything in common, both are implemented in terms of a third screen, file_slots."
    # Automatic translation.
    new "## Der load and save Bildschirm sind dafür verantwortlich, dass der Spieler das Spiel speichern und wieder laden kann. Da sie fast alles gemeinsam haben, sind beide in Form eines dritten Bildschirms, file_slots, implementiert."

    # screens.rpy:588
    old "## https://www.renpy.org/doc/html/screen_special.html#save https://www.renpy.org/doc/html/screen_special.html#load"
    new "## https://www.renpy.org/doc/html/screen_special.html#save https://www.renpy.org/doc/html/screen_special.html#load"

    # screens.rpy:607
    old "Page {}"
    # Automatic translation.
    new "Seite {}"

    # screens.rpy:607
    old "Automatic saves"
    new "Automatische Speicherstände"

    # screens.rpy:607
    old "Quick saves"
    new "Schnellspeicherstände"

    # screens.rpy:613
    old "## This ensures the input will get the enter event before any of the buttons do."
    # Automatic translation.
    new "## Dies stellt sicher, dass die Eingabe das Enter-Ereignis erhält, bevor es eine der Schaltflächen tut."

    # screens.rpy:629
    old "## The grid of file slots."
    # Automatic translation.
    new "## Das Raster der Dateislots."

    # screens.rpy:649
    old "{#file_time}%A, %B %d %Y, %H:%M"
    new "{#file_time}%A, %d. %B %Y, %H:%M"

    # screens.rpy:649
    old "empty slot"
    new "leerer Speicherplatz"

    # screens.rpy:657
    old "## Buttons to access other pages."
    # Automatic translation.
    new "## Schaltflächen für den Zugriff auf andere Seiten."

    # screens.rpy:666
    old "<"
    new "<"

    # screens.rpy:668
    old "{#auto_page}A"
    new "{#auto_page}A"

    # screens.rpy:670
    old "{#quick_page}Q"
    new "{#quick_page}S"

    # screens.rpy:676
    old ">"
    new ">"

    # screens.rpy:711
    old "## Preferences screen"
    # Automatic translation.
    new "## Einstellungs-Bildschirm"

    # screens.rpy:713
    old "## The preferences screen allows the player to configure the game to better suit themselves."
    # Automatic translation.
    new "## Der Einstellungs-Bildschirm ermöglicht es dem Spieler, das Spiel so zu konfigurieren, dass es besser zu ihm passt."

    # screens.rpy:716
    old "## https://www.renpy.org/doc/html/screen_special.html#preferences"
    new "## https://www.renpy.org/doc/html/screen_special.html#preferences"

    # screens.rpy:738
    old "Display"
    new "Anzeige"

    # screens.rpy:739
    old "Window"
    new "Fenster"

    # screens.rpy:740
    old "Fullscreen"
    new "Vollbild"

    # screens.rpy:744
    old "Rollback Side"
    # Automatic translation.
    new "Rollback Seite"

    # screens.rpy:745
    old "Disable"
    # Automatic translation.
    new "Deaktivieren"

    # screens.rpy:746
    old "Left"
    # Automatic translation.
    new "Links"

    # screens.rpy:747
    old "Right"
    # Automatic translation.
    new "Rechts"

    # screens.rpy:752
    old "Unseen Text"
    new "Ungesehener Text"

    # screens.rpy:753
    old "After Choices"
    new "Nach Entscheidungen"

    # screens.rpy:754
    old "Transitions"
    new "Übergänge"

    # screens.rpy:756
    old "## Additional vboxes of type \"radio_pref\" or \"check_pref\" can be added here, to add additional creator-defined preferences."
    # Automatic translation.
    new "## Hier können zusätzliche vboxes vom Typ \"radio_pref\" oder \"check_pref\" hinzugefügt werden, um weitere vom Ersteller definierte Einstellungen hinzuzufügen."

    # screens.rpy:767
    old "Text Speed"
    new "Textgeschwindigkeit"

    # screens.rpy:771
    old "Auto-Forward Time"
    new "Automatischer Vorlauf"

    # screens.rpy:778
    old "Music Volume"
    new "Musiklautstärke"

    # screens.rpy:785
    old "Sound Volume"
    new "Geräuschlautstärke"

    # screens.rpy:791
    old "Test"
    new "Test"

    # screens.rpy:795
    old "Voice Volume"
    new "Sprachlautstärke"

    # screens.rpy:806
    old "Mute All"
    # Automatic translation.
    new "Alle stummschalten"

    # screens.rpy:882
    old "## History screen"
    new "## Dialogverlauf-Bildschirm"

    # screens.rpy:884
    old "## This is a screen that displays the dialogue history to the player. While there isn't anything special about this screen, it does have to access the dialogue history stored in _history_list."
    # Automatic translation.
    new "## Dies ist ein Bildschirm, der dem Spieler den Dialogverlauf anzeigt. Es gibt zwar nichts Besonderes an diesem Bildschirm, aber er muss auf den Dialogverlauf zugreifen, der in _history_list gespeichert ist."

    # screens.rpy:888
    old "## https://www.renpy.org/doc/html/history.html"
    new "## https://www.renpy.org/doc/html/history.html"

    # screens.rpy:894
    old "## Avoid predicting this screen, as it can be very large."
    new "## Vermeide es, diesen Bildschirm via \"predict\" vorberechnen zu lassen, da er sehr groß sein kann."

    # screens.rpy:905
    old "## This lays things out properly if history_height is None."
    # Automatic translation.
    new "## Dies legt die Dinge richtig aus, wenn history_height None ist."

    # screens.rpy:914
    old "## Take the color of the who text from the Character, if set."
    new "## Übernimmt die Farbe des who-Textes des Charakters, falls vorhanden."

    # screens.rpy:921
    old "The dialogue history is empty."
    # Automatic translation.
    new "Der Dialogverlauf ist leer."

    # screens.rpy:965
    old "## Help screen"
    # Automatic translation.
    new "## Hilfe-Bildschirm"

    # screens.rpy:967
    old "## A screen that gives information about key and mouse bindings. It uses other screens (keyboard_help, mouse_help, and gamepad_help) to display the actual help."
    # Automatic translation.
    new "## Ein Bildschirm, der Informationen über Tasten- und Mausbelegungen liefert. Er verwendet andere Bildschirme (keyboard_help, mouse_help und gamepad_help), um die eigentliche Hilfe anzuzeigen."

    # screens.rpy:986
    old "Keyboard"
    # Automatic translation.
    new "Tastatur"

    # screens.rpy:987
    old "Mouse"
    # Automatic translation.
    new "Maus"

    # screens.rpy:990
    old "Gamepad"
    new "Gamepad"

    # screens.rpy:1003
    old "Enter"
    # Automatic translation.
    new "Eingabe"

    # screens.rpy:1004
    old "Advances dialogue and activates the interface."
    # Automatic translation.
    new "Bringt den Dialog voran und aktiviert die Schnittstelle."

    # screens.rpy:1007
    old "Space"
    # Automatic translation.
    new "Leertaste"

    # screens.rpy:1008
    old "Advances dialogue without selecting choices."
    # Automatic translation.
    new "Bringt den Dialog voran ohne eine Auswahl zu treffen."

    # screens.rpy:1011
    old "Arrow Keys"
    # Automatic translation.
    new "Pfeil-Tasten"

    # screens.rpy:1012
    old "Navigate the interface."
    # Automatic translation.
    new "Navigiert durch die Schnittstelle."

    # screens.rpy:1015
    old "Escape"
    # Automatic translation.
    new "ESC"

    # screens.rpy:1016
    old "Accesses the game menu."
    # Automatic translation.
    new "Ruft das Spielmenü auf."

    # screens.rpy:1019
    old "Ctrl"
    # Automatic translation.
    new "Strg"

    # screens.rpy:1020
    old "Skips dialogue while held down."
    new "Überspringt Dialoge, wenn Du die Taste gedrückt hältst."

    # screens.rpy:1023
    old "Tab"
    new "Tab"

    # screens.rpy:1024
    old "Toggles dialogue skipping."
    # Automatic translation.
    new "Schaltet das Überspringen von Dialogen ein/aus."

    # screens.rpy:1027
    old "Page Up"
    # Automatic translation.
    new "Seite hoch"

    # screens.rpy:1028
    old "Rolls back to earlier dialogue."
    # Automatic translation.
    new "Springt zu einem früheren Dialog zurück."

    # screens.rpy:1031
    old "Page Down"
    # Automatic translation.
    new "Seite runter"

    # screens.rpy:1032
    old "Rolls forward to later dialogue."
    # Automatic translation.
    new "Springt zu einem späteren Dialog vor."

    # screens.rpy:1036
    old "Hides the user interface."
    # Automatic translation.
    new "Blendet die Benutzeroberfläche aus."

    # screens.rpy:1040
    old "Takes a screenshot."
    # Automatic translation.
    new "Nimmt einen Screenshot auf."

    # screens.rpy:1044
    old "Toggles assistive {a=https://www.renpy.org/l/voicing}self-voicing{/a}."
    # Automatic translation.
    new "Schaltet unterstützende {a=https://www.renpy.org/l/voicing}Automatische Spracherzeugung{/a} ein/aus."

    # screens.rpy:1050
    old "Left Click"
    # Automatic translation.
    new "Linksklick"

    # screens.rpy:1054
    old "Middle Click"
    # Automatic translation.
    new "Mittlerer Klick"

    # screens.rpy:1058
    old "Right Click"
    # Automatic translation.
    new "Rechtsklick"

    # screens.rpy:1062
    old "Mouse Wheel Up"
    new "Mausrad nach oben\nKlicke auf Rollback Seite"

    # screens.rpy:1066
    old "Mouse Wheel Down"
    # Automatic translation.
    new "Mausrad nach unten"

    # screens.rpy:1073
    old "Right Trigger\nA/Bottom Button"
    # Automatic translation.
    new "Rechter Auslöser\nA/Unterer Knopf"

    # screens.rpy:1074
    old "Advance dialogue and activates the interface."
    # Automatic translation.
    new "Springt zu einem späteren Dialog vor und aktiviert die Schnittstelle."

    # screens.rpy:1078
    old "Roll back to earlier dialogue."
    # Automatic translation.
    new "Springt zu einem früheren Dialog zurück."

    # screens.rpy:1081
    old "Right Shoulder"
    # Automatic translation.
    new "Rechte Schulter"

    # screens.rpy:1082
    old "Roll forward to later dialogue."
    new "Spult zu einem späteren Dialog vor."

    # screens.rpy:1085
    old "D-Pad, Sticks"
    new "D-Pad, Sticks"

    # screens.rpy:1089
    old "Start, Guide"
    # Automatic translation.
    new "Start, Leitfaden"

    # screens.rpy:1090
    old "Access the game menu."
    new "Ruft das Spielmenü auf."

    # screens.rpy:1093
    old "Y/Top Button"
    # Automatic translation.
    new "Y/Top-Taste"

    # screens.rpy:1096
    old "Calibrate"
    # Automatic translation.
    new "Kalibrieren"

    # screens.rpy:1124
    old "## Additional screens"
    # Automatic translation.
    new "## Zusätzliche Bildschirme"

    # screens.rpy:1128
    old "## Confirm screen"
    new "## Bestätigungs-Bildschirm"

    # screens.rpy:1130
    old "## The confirm screen is called when Ren'Py wants to ask the player a yes or no question."
    # Automatic translation.
    new "## Der Bestätigungsbildschirm wird aufgerufen, wenn Ren'Py dem Spieler eine Ja- oder Nein-Frage stellen will."

    # screens.rpy:1133
    old "## http://www.renpy.org/doc/html/screen_special.html#confirm"
    new "## http://www.renpy.org/doc/html/screen_special.html#confirm"

    # screens.rpy:1137
    old "## Ensure other screens do not get input while this screen is displayed."
    # Automatic translation.
    new "## Stell sicher, dass andere Bildschirme keine Eingaben erhalten, während dieser Bildschirm angezeigt wird."

    # screens.rpy:1161
    old "Yes"
    new "Ja"

    # screens.rpy:1162
    old "No"
    new "Nein"

    # screens.rpy:1164
    old "## Right-click and escape answer \"no\"."
    new "## Ein Rechtsklick oder [ESC] beantworten jeweils mit \"Nein\"."

    # screens.rpy:1191
    old "## Skip indicator screen"
    # Automatic translation.
    new "## Vorspul-Symbol Bildschirm"

    # screens.rpy:1193
    old "## The skip_indicator screen is displayed to indicate that skipping is in progress."
    # Automatic translation.
    new "## Der Bildschirm skip_indicator wird angezeigt, um zu signalisieren, dass das Überspringen im Gange ist."

    # screens.rpy:1196
    old "## https://www.renpy.org/doc/html/screen_special.html#skip-indicator"
    new "## https://www.renpy.org/doc/html/screen_special.html#skip-indicator"

    # screens.rpy:1208
    old "Skipping"
    new "Vorspulen"

    # screens.rpy:1215
    old "## This transform is used to blink the arrows one after another."
    new "## Dieses Transform wird verwendet, um die Pfeile nacheinander blinken zu lassen."

    # screens.rpy:1247
    old "## Notify screen"
    # Automatic translation.
    new "## Benachrichtigungsbildschirm"

    # screens.rpy:1249
    old "## The notify screen is used to show the player a message. (For example, when the game is quicksaved or a screenshot has been taken.)"
    # Automatic translation.
    new "## Der Benachrichtigungsbildschirm wird verwendet, um dem Spieler eine Nachricht anzuzeigen. (Zum Beispiel, wenn das Spiel schnell gespeichert wird oder ein Screenshot gemacht wurde)."

    # screens.rpy:1252
    old "## https://www.renpy.org/doc/html/screen_special.html#notify-screen"
    new "## https://www.renpy.org/doc/html/screen_special.html#notify-screen"

    # screens.rpy:1286
    old "## NVL screen"
    # Automatic translation.
    new "## NVL-Bildschirm"

    # screens.rpy:1288
    old "## This screen is used for NVL-mode dialogue and menus."
    # Automatic translation.
    new "## Dieser Bildschirm wird für den Dialog und die Menüs im NVL-Modus verwendet."

    # screens.rpy:1290
    old "## http://www.renpy.org/doc/html/screen_special.html#nvl"
    new "## http://www.renpy.org/doc/html/screen_special.html#nvl"

    # screens.rpy:1301
    old "## Displays dialogue in either a vpgrid or the vbox."
    # Automatic translation.
    new "## Zeigt den Dialog entweder in einem vpgrid oder in der vbox an."

    # screens.rpy:1314
    old "## Displays the menu, if given. The menu may be displayed incorrectly if config.narrator_menu is set to True, as it is above."
    # Automatic translation.
    new "## Zeigt das Menü an, falls vorhanden. Das Menü kann falsch angezeigt werden, wenn config.narrator_menu auf True gesetzt ist, wie es oben der Fall ist."

    # screens.rpy:1344
    old "## This controls the maximum number of NVL-mode entries that can be displayed at once."
    # Automatic translation.
    new "## Dies steuert die maximale Anzahl der Einträge im NVL-Modus, die gleichzeitig angezeigt werden können."

    # screens.rpy:1406
    old "## Mobile Variants"
    new "## Varianten für Mobilgeräte"

    # screens.rpy:1413
    old "## Since a mouse may not be present, we replace the quick menu with a version that uses fewer and bigger buttons that are easier to touch."
    # Automatic translation.
    new "## Da eine Maus möglicherweise nicht vorhanden ist, ersetzen wir das Schnellmenü durch eine Version mit weniger und größeren Tasten, die leichter zu berühren sind."

    # screens.rpy:1429
    old "Menu"
    # Automatic translation.
    new "Menü"


translate german strings:

    # gui/game/screens.rpy:114
    old "## If there's a side image, display it above the text. Do not display on the phone variant - there's no room."
    # Automatic translation.
    new "## Wenn es ein Seitenbild gibt, wird es über dem Text angezeigt. Allerdings nicht auf der Smartphone-Variante - dort ist kein Platz."

    # gui/game/screens.rpy:120
    old "## Make the namebox available for styling through the Character object."
    new "## Macht das Namensfeld für die Gestaltung durch das Character-Objekt verfügbar."

    # gui/game/screens.rpy:173
    old "## https://www.renpy.org/doc/html/screen_special.html#input"
    new "## https://www.renpy.org/doc/html/screen_special.html#input"

    # gui/game/screens.rpy:206
    old "## https://www.renpy.org/doc/html/screen_special.html#choice"
    new "## https://www.renpy.org/doc/html/screen_special.html#choice"

    # gui/game/screens.rpy:241
    old "## Ensure this appears on top of other screens."
    new "## Stellt sicher, dass dies vor allen anderen Bildschirmen erscheint."

    # gui/game/screens.rpy:280
    old "## Main and Game Menu Screens"
    # Automatic translation.
    new "## Haupt- und Spielmenü-Bildschirme"

    # gui/game/screens.rpy:329
    old "## The quit button is banned on iOS and unnecessary on Android and Web."
    # Automatic translation.
    new "## Die Beenden-Schaltfläche ist auf iOS verboten und auf Android und Web unnötig."

    # gui/game/screens.rpy:348
    old "## https://www.renpy.org/doc/html/screen_special.html#main-menu"
    new "## https://www.renpy.org/doc/html/screen_special.html#main-menu"

    # gui/game/screens.rpy:352
    old "## This ensures that any other menu screen is replaced."
    # Automatic translation.
    new "## Dadurch wird sichergestellt, dass jeder andere Menübildschirm ersetzt wird."

    # gui/game/screens.rpy:357
    old "## This empty frame darkens the main menu."
    # Automatic translation.
    new "## Dieser leere Rahmen verdunkelt das Hauptmenü."

    # gui/game/screens.rpy:429
    old "## Reserve space for the navigation section."
    new "## Reserviert Platz für den Navigationsbereich."

    # gui/game/screens.rpy:608
    old "## The page name, which can be edited by clicking on a button."
    # Automatic translation.
    new "## Der Seitenname, der durch Klicken auf eine Schaltfläche bearbeitet werden kann."

    # gui/game/screens.rpy:668
    old "## range(1, 10) gives the numbers from 1 to 9."
    # Automatic translation.
    new "## range(1, 10) liefert die Zahlen von 1 bis 9."

    # gui/game/screens.rpy:676
    old "Upload Sync"
    # Automatic translation.
    new "Sync hochladen"

    # gui/game/screens.rpy:680
    old "Download Sync"
    # Automatic translation.
    new "Sync herunterladen"

    # gui/game/screens.rpy:921
    old "## This determines what tags are allowed to be displayed on the history screen."
    new "## Hier wird festgelegt, welche Tags auf dem Verlaufsbildschirm angezeigt werden dürfen."

    # gui/game/screens.rpy:1049
    old "Opens the accessibility menu."
    # Automatic translation.
    new "Öffnet das Menü Barrierefreiheit."

    # gui/game/screens.rpy:1082
    old "Left Trigger\nLeft Shoulder"
    # Automatic translation.
    new "Linker Auslöser\nLinke Schulter"

    # gui/game/screens.rpy:1139
    old "## https://www.renpy.org/doc/html/screen_special.html#confirm"
    new "## https://www.renpy.org/doc/html/screen_special.html#confirm"

    # gui/game/screens.rpy:1248
    old "## We have to use a font that has the BLACK RIGHT-POINTING SMALL TRIANGLE glyph in it."
    new "## Wir müssen eine Schriftart verwenden, die die Glyphe BLACK RIGHT-POINTING SMALL TRIANGLE enthält."

    # gui/game/screens.rpy:1296
    old "## https://www.renpy.org/doc/html/screen_special.html#nvl"
    new "## https://www.renpy.org/doc/html/screen_special.html#nvl"

    # gui/game/screens.rpy:1320
    old "## Displays the menu, if given. The menu may be displayed incorrectly if config.narrator_menu is set to True."
    # Automatic translation.
    new "## Zeigt das Menü an, falls vorhanden. Das Menü wird möglicherweise nicht korrekt angezeigt, wenn config.narrator_menu auf True gesetzt ist."

    # gui/game/screens.rpy:1410
    old "## Bubble screen"
    new "## Sprechblasen-Bildschirm"

    # gui/game/screens.rpy:1412
    old "## The bubble screen is used to display dialogue to the player when using speech bubbles. The bubble screen takes the same parameters as the say screen, must create a displayable with the id of \"what\", and can create displayables with the \"namebox\", \"who\", and \"window\" ids."
    # Automatic translation.
    new "## Der Sprechblasenbildschirm wird verwendet, um dem Spieler einen Dialog anzuzeigen, wenn Sprechblasen verwendet werden. Er benötigt die gleichen Parameter wie der Dialog-Bildschirm, muss ein Displayable mit der Id \"what\" erstellen und kann Displayables mit den Ids \"namebox\", \"who\" und \"window\" erstellen."

    # gui/game/screens.rpy:1417
    old "## https://www.renpy.org/doc/html/bubble.html#bubble-screen"
    new "## https://www.renpy.org/doc/html/bubble.html#bubble-screen"

translate german strings:

    # gui/game/screens.rpy:411
    old "## The scroll parameter can be None, or one of \"viewport\" or \"vpgrid\". This screen is intended to be used with one or more children, which are transcluded (placed) inside it."
    new "## Der Parameter scroll kann None oder einer der Parameter \"viewport\" oder \"vpgrid\" sein. Dieser Bildschirm sollte mit einem oder mehreren Unterobjekten verwendet werden, die in ihn eingeschlossen (platziert) werden."


