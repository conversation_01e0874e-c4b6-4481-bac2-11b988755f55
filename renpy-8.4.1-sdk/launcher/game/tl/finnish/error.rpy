
translate finnish strings:

    # 00gltest.rpy:64
    old "Graphics Acceleration"
    new "Grafiikkakiihdytys"

    # 00gltest.rpy:70
    old "Automatically Choose"
    new "Valitse automaattisesti"

    # 00gltest.rpy:75
    old "Force Angle/DirectX Renderer"
    new "Pakota Angle/DirectX-renderöinti"

    # 00gltest.rpy:79
    old "Force OpenGL Renderer"
    new "Pakota OpenGL-renderöinti"

    # 00gltest.rpy:83
    old "Force Software Renderer"
    new "Pakota Software-renderöinti"

    # 00gltest.rpy:93
    old "Enable"
    new "Ota käyttöön"

    # 00gltest.rpy:109
    old "Changes will take effect the next time this program is run."
    new "Muutokset tulevat voimaan, kun käynnistät ohjelman uudelleen."

    # 00gltest.rpy:141
    old "Performance Warning"
    new "Suorituskyvyn varoitus"

    # 00gltest.rpy:146
    old "This computer is using software rendering."
    new "Tietokone käyttää software-renderöintiä."

    # 00gltest.rpy:148
    old "This computer is not using shaders."
    new "Tietokone ei käytä varjostuksia."

    # 00gltest.rpy:150
    old "This computer is displaying graphics slowly."
    new "Tietokone näyttää grafiikkaa hitaasti."

    # 00gltest.rpy:152
    old "This computer has a problem displaying graphics: [problem]."
    new "Tietokoneella on ongelmia grafiikan näyttämisessä: [problem]."

    # 00gltest.rpy:157
    old "Its graphics drivers may be out of date or not operating correctly. This can lead to slow or incorrect graphics display. Updating DirectX could fix this problem."
    new "Sen grafiikka-ajurit voivat olla vanhentuneita tai ne saattavat toimia väärin. Tämä voi johtaa hitaaseen tai väärään grafiikan esittämiseen. DirectX:n päivittäminen saattaa korjata ongelman."

    # 00gltest.rpy:159
    old "Its graphics drivers may be out of date or not operating correctly. This can lead to slow or incorrect graphics display."
    new "Sen grafiikka-ajurit voivat olla vanhentuneita tai ne saattavat toimia väärin. Tämä voi johtaa hitaaseen tai väärään grafiikan esittämiseen."

    # 00gltest.rpy:164
    old "Update DirectX"
    new "Päivitä DirectX"

    # 00gltest.rpy:170
    old "Continue, Show this warning again"
    new "Jatka, näytä tämä varoitus uudelleen"

    # 00gltest.rpy:174
    old "Continue, Don't show warning again"
    new "Jatka, älä näytä tätä varoitusta enää uudelleen"

    # 00gltest.rpy:192
    old "Updating DirectX."
    new "Päivitetään DirectX:ää."

    # 00gltest.rpy:196
    old "DirectX web setup has been started. It may start minimized in the taskbar. Please follow the prompts to install DirectX."
    new "DirectX:n verkkoasennus on alkanut. Se voi olla aluksi minimisoituna työkalupalkilla. Seuraa ohjeita asentaaksesi DirectX:n."

    # 00gltest.rpy:200
    old "{b}Note:{/b} Microsoft's DirectX web setup program will, by default, install the Bing toolbar. If you do not want this toolbar, uncheck the appropriate box."
    new "{b}Huomaa:{/b} Microsoftin DirectX-verkkoasennusohjelma asentaa, oletusarvoisesti, Bing-työkalupalkin. Jos et halua tätä työkalupalkkia, poista valinta oikeasta laatikosta."

    # 00gltest.rpy:204
    old "When setup finishes, please click below to restart this program."
    new "Kun asennus on valmis, klikkaa alhaalta käynnistääksesi tämän ohjelman uudelleen."

    # 00gltest.rpy:206
    old "Restart"
    new "Käynnistä uudelleen"

    # 00gamepad.rpy:32
    old "Select Gamepad to Calibrate"
    new "Valitse kalibroitava peliohjain"

    # 00gamepad.rpy:35
    old "No Gamepads Available"
    new "Peliohjaimia ei havaittu"

    # 00gamepad.rpy:54
    old "Calibrating [name] ([i]/[total])"
    new "Kalibroidaan [name] ([i]/[total])"

    # 00gamepad.rpy:58
    old "Press or move the [control!s] [kind]."
    new "Paina tai liikuta [control!s] [kind]."

    # 00gamepad.rpy:66
    old "Skip (A)"
    new "Ohita (A)"

    # 00gamepad.rpy:69
    old "Back (B)"
    new "Takaisin (B)"

    # _errorhandling.rpym:495
    old "Open Traceback"
    new "Avaa virheenjäljitystoiminto"

    # _errorhandling.rpym:497
    old "Opens the traceback.txt file in a text editor."
    new "Avaa traceback.txt-tiedoston tekstieditorissa."

    # _errorhandling.rpym:499
    old "Copy to Clipboard"
    new "Kopioi leikepöydälle"

    # _errorhandling.rpym:501
    old "Copies the traceback.txt file to the clipboard."
    new "Kopioi traceback.txt-tiedoston leikepöydälle."

    # _errorhandling.rpym:519
    old "An exception has occurred."
    new "Odottamaton virhe on tapahtunut."

    # _errorhandling.rpym:538
    old "Rollback"
    new "Palaa edelliseen tilaan"

    # _errorhandling.rpym:540
    old "Attempts a roll back to a prior time, allowing you to save or choose a different choice."
    new "Yrittää palata aikaisempaan tilaan, antaen sinun tallentaa tai tehdä eri valinnan."

    # _errorhandling.rpym:543
    old "Ignore"
    new "Sivuuta"

    # _errorhandling.rpym:545
    old "Ignores the exception, allowing you to continue. This often leads to additional errors."
    new "Sivuuttaa poikkeuksen, antaen sinun jatkaa. Tämä johtaa usein uusiin virheisiin."

    # _errorhandling.rpym:548
    old "Reload"
    new "Lataa uudelleen"

    # _errorhandling.rpym:550
    old "Reloads the game from disk, saving and restoring game state if possible."
    new "Lataa pelin uudelleen muistista, tallentaen ja palauttaen pelitilan jos mahdollista."

    # _errorhandling.rpym:560
    old "Quits the game."
    new "Lopettaa pelin."

    # _errorhandling.rpym:582
    old "Parsing the script failed."
    new "Skriptin jäsennys epäonnistui."

    # _errorhandling.rpym:606
    old "Open Parse Errors"
    new "Avaa jäsennysvirheet"

    # _errorhandling.rpym:608
    old "Opens the errors.txt file in a text editor."
    new "Avaa errors.txt-tiedoston tekstieditorissa."

    # _errorhandling.rpym:612
    old "Copies the errors.txt file to the clipboard."
    new "Kopioi errors.txt-tiedoston leikepöydälle."


translate finnish strings:

    # renpy/common/00gltest.rpy:89
    old "Renderer"
    new "Renderer"

    # renpy/common/00gltest.rpy:100
    old "Force GL Renderer"
    new "Force GL Renderer"

    # renpy/common/00gltest.rpy:105
    old "Force ANGLE Renderer"
    new "Force ANGLE Renderer"

    # renpy/common/00gltest.rpy:110
    old "Force GLES Renderer"
    # Automatic translation.
    new "Pakota GLES Renderer"

    # renpy/common/00gltest.rpy:116
    old "Force GL2 Renderer"
    # Automatic translation.
    new "Pakota GL2 Renderer"

    # renpy/common/00gltest.rpy:121
    old "Force ANGLE2 Renderer"
    # Automatic translation.
    new "Voima ANGLE2 Renderer"

    # renpy/common/00gltest.rpy:126
    old "Force GLES2 Renderer"
    # Automatic translation.
    new "Pakota GLES2 Renderer"

    # renpy/common/00gltest.rpy:136
    old "Enable (No Blocklist)"
    # Automatic translation.
    new "Enable (Ei estolistaa)"

    # renpy/common/00gltest.rpy:159
    old "Powersave"
    new "Powersave"

    # renpy/common/00gltest.rpy:173
    old "Framerate"
    new "Framerate"

    # renpy/common/00gltest.rpy:177
    old "Screen"
    # Automatic translation.
    new "Näyttö"

    # renpy/common/00gltest.rpy:181
    old "60"
    new "60"

    # renpy/common/00gltest.rpy:185
    old "30"
    new "30"

    # renpy/common/00gltest.rpy:191
    old "Tearing"
    new "Tearing"

    # renpy/common/00gltest.rpy:249
    old "This game requires use of GL2 that can't be initialised."
    # Automatic translation.
    new "Tämä peli vaatii GL2:n käyttöä, jota ei voida alustaa."

    # renpy/common/00gltest.rpy:259
    old "The {a=edit:1:log.txt}log.txt{/a} file may contain information to help you determine what is wrong with your computer."
    # Automatic translation.
    new "{a=edit:1:log.txt}log.txt{/a} tiedosto voi sisältää tietoja, joiden avulla voit selvittää, mikä tietokoneessa on vikana."

    # renpy/common/00gltest.rpy:264
    old "More details on how to fix this can be found in the {a=[url]}documentation{/a}."
    # Automatic translation.
    new "Lisätietoja tämän korjaamisesta löytyy {a=[url]}-dokumentaatiosta{/a}."

    # renpy/common/00gltest.rpy:281
    old "Change render options"
    # Automatic translation.
    new "Muuta renderöintivaihtoehtoja"

    # renpy/common/00gamepad.rpy:58
    old "Press or move the '[control!s]' [kind]."
    # Automatic translation.
    new "Paina tai siirrä '[control!s]' [kind]."

    # renpy/common/_errorhandling.rpym:555
    old "Open"
    # Automatic translation.
    new "Avaa"

    # renpy/common/_errorhandling.rpym:559
    old "Copy BBCode"
    # Automatic translation.
    new "Kopioi BBCode"

    # renpy/common/_errorhandling.rpym:561
    old "Copies the traceback.txt file to the clipboard as BBcode for forums like https://lemmasoft.renai.us/."
    # Automatic translation.
    new "Kopioi traceback.txt-tiedoston leikepöydälle BBkoodina foorumeille, kuten https://lemmasoft.renai.us/."

    # renpy/common/_errorhandling.rpym:563
    old "Copy Markdown"
    # Automatic translation.
    new "Kopioi Markdown"

    # renpy/common/_errorhandling.rpym:565
    old "Copies the traceback.txt file to the clipboard as Markdown for Discord."
    # Automatic translation.
    new "Kopioi traceback.txt-tiedoston leikepöydälle Markdown-muodossa Discordia varten."

    # renpy/common/_errorhandling.rpym:626
    old "Ignores the exception, allowing you to continue."
    # Automatic translation.
    new "Jättää poikkeuksen huomiotta, jolloin voit jatkaa."

    # renpy/common/_errorhandling.rpym:637
    old "Console"
    # Automatic translation.
    new "Konsoli"

    # renpy/common/_errorhandling.rpym:639
    old "Opens a console to allow debugging the problem."
    # Automatic translation.
    new "Avaa konsolin ongelman vianmääritystä varten."

