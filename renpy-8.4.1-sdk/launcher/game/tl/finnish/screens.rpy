
translate finnish strings:

    # screens.rpy:9
    old "## Styles"
    new "## Tyylit"

    # screens.rpy:87
    old "## In-game screens"
    new "## Pelinsisäiset näytöt"

    # screens.rpy:91
    old "## Say screen"
    new "## Tulostenäyttö"

    # screens.rpy:93
    old "## The say screen is used to display dialogue to the player. It takes two parameters, who and what, which are the name of the speaking character and the text to be displayed, respectively. (The who parameter can be None if no name is given.)"
    new "## Tulostenäyttöä käytetään dialogin näyttämiseen pelaajalle. Se ottaa kaksi parametriä, who ja what, jotka ovat puhuvan hahmon nimi sekä näytettävä teksti. (who-parametri voi olla None, jos nimeä ei anneta.)"

    # screens.rpy:98
    old "## This screen must create a text displayable with id \"what\", as Ren'<PERSON><PERSON> uses this to manage text display. It can also create displayables with id \"who\" and id \"window\" to apply style properties."
    new "## Tämän näytön on luotava tekstiesittäjä käyttäen komentoa id \"what\", sillä Ren'Py käyttää tätä tekstin näytön käsittelyssä. Se voi myös luoda grafiikkaa käyttäen komentoja id \"who\" ja id \"window\" tyyliominaisuuksien lisäämiseksi."

    # screens.rpy:102
    old "## https://www.renpy.org/doc/html/screen_special.html#say"
    new "## https://www.renpy.org/doc/html/screen_special.html#say"

    # screens.rpy:169
    old "## Input screen"
    new "## Syötenäyttö"

    # screens.rpy:171
    old "## This screen is used to display renpy.input. The prompt parameter is used to pass a text prompt in."
    new "## Tätä näyttöä käytetään renpy.input:in renderöinnissä. prompt-parametri antaa kehotetekstin."

    # screens.rpy:174
    old "## This screen must create an input displayable with id \"input\" to accept the various input parameters."
    new "## Tämän näytön tulee luoda syöttöikkuna käyttäen komentoa id \"input\" voidakseen hyväksyä erilaisia syöteparametrejä."

    # screens.rpy:177
    old "## http://www.renpy.org/doc/html/screen_special.html#input"
    new "## http://www.renpy.org/doc/html/screen_special.html#input"

    # screens.rpy:205
    old "## Choice screen"
    new "## Valintanäyttö"

    # screens.rpy:207
    old "## This screen is used to display the in-game choices presented by the menu statement. The one parameter, items, is a list of objects, each with caption and action fields."
    new "## Tätä näyttöä käytetään esittämään pelinsisäiset menu-lauseen esittämät valinnat. Sen ainoa parametri, items, on lista olioista, joiista jokaisessa on kentät otsikolle ja toiminnolle."

    # screens.rpy:211
    old "## http://www.renpy.org/doc/html/screen_special.html#choice"
    new "## http://www.renpy.org/doc/html/screen_special.html#choice"

    # screens.rpy:221
    old "## When this is true, menu captions will be spoken by the narrator. When false, menu captions will be displayed as empty buttons."
    new "## Tämän ollessa tosi, kertoja puhuu valinnat ääneen. Ja kun epätosi, vaihtoehdot näytetään tyhjinä painikkeina."

    # screens.rpy:244
    old "## Quick Menu screen"
    new "## Pikavalikkonäyttö"

    # screens.rpy:246
    old "## The quick menu is displayed in-game to provide easy access to the out-of-game menus."
    new "## Pikavalikko näytetään pelin aikana, tarjoten helpon pääsyn pelin ulkoisiin valikkoihin."

    # screens.rpy:261
    old "Back"
    new "Takaisin"

    # screens.rpy:262
    old "History"
    new "Historia"

    # screens.rpy:263
    old "Skip"
    # Automatic translation.
    new "Ohita"

    # screens.rpy:264
    old "Auto"
    new "Auto"

    # screens.rpy:265
    old "Save"
    new "Tallenna"

    # screens.rpy:266
    old "Q.Save"
    new "P.Tallenna"

    # screens.rpy:267
    old "Q.Load"
    new "P.Lataa"

    # screens.rpy:268
    old "Prefs"
    new "Asetukset"

    # screens.rpy:271
    old "## This code ensures that the quick_menu screen is displayed in-game, whenever the player has not explicitly hidden the interface."
    new "## Tämä koodi varmistaa, että quick_menu-näyttö näytetään pelin aikana, aina kun pelaaja ei ole erikseen sitä piilottanut."

    # screens.rpy:291
    old "## Navigation screen"
    new "## Siirtymänäyttö"

    # screens.rpy:293
    old "## This screen is included in the main and game menus, and provides navigation to other menus, and to start the game."
    new "## Tämä näyttö sisältyy pää- ja pelivalikkoon, tarjoten pääsyn muihin valikoihin, sekä pelin aloitukseen."

    # screens.rpy:308
    old "Start"
    new "Aloita"

    # screens.rpy:316
    old "Load"
    new "Lataa"

    # screens.rpy:318
    old "Preferences"
    new "Asetukset"

    # screens.rpy:322
    old "End Replay"
    new "Päätä Toisto"

    # screens.rpy:326
    old "Main Menu"
    new "Päävalikko"

    # screens.rpy:328
    old "About"
    new "Tietoja"

    # screens.rpy:332
    old "## Help isn't necessary or relevant to mobile devices."
    new "## Ohje ei ole tarpeellinen tai oleellinen mobiililaitteilla."

    # screens.rpy:333
    old "Help"
    new "Ohje"

    # screens.rpy:335
    old "## The quit button is banned on iOS and unnecessary on Android."
    new "## Poistu-painike on kielletty iOS-laitteilla, ja tarpeeton Android-laitteilla."

    # screens.rpy:336
    old "Quit"
    new "Poistu"

    # screens.rpy:350
    old "## Main Menu screen"
    new "## Päävalikkonäkymä"

    # screens.rpy:352
    old "## Used to display the main menu when Ren'Py starts."
    new "## Käytetään päävalikon näyttämiseksi Ren'Py:n käynnistyessä."

    # screens.rpy:354
    old "## http://www.renpy.org/doc/html/screen_special.html#main-menu"
    new "## http://www.renpy.org/doc/html/screen_special.html#main-menu"

    # screens.rpy:369
    old "## The use statement includes another screen inside this one. The actual contents of the main menu are in the navigation screen."
    new "## use-komento sisältää toisen näytön. Päävalikon sisältö on todellisuudessa osa siirtymänäyttöä."

    # screens.rpy:413
    old "## Game Menu screen"
    new "## Pelivalikkonäkymä"

    # screens.rpy:415
    old "## This lays out the basic common structure of a game menu screen. It's called with the screen title, and displays the background, title, and navigation."
    new "## Tämä esittää pelivalikon perusrakenteen. Sitä kutsutaan näytön nimellä, ja se näyttää taustakuvan, nimen, sekä siirtymävalinnat."

    # screens.rpy:418
    old "## The scroll parameter can be None, or one of \"viewport\" or \"vpgrid\". When this screen is intended to be used with one or more children, which are transcluded (placed) inside it."
    new "## scroll-parametri voi olla None, tai vaihtoisesti \"viewport\" tai \"vpgrid\". Tämän näytön sisältäessä yhden tai useamman muun näytön, jotka sijoitetaan sen sisään."

    # screens.rpy:476
    old "Return"
    new "Palaa"

    # screens.rpy:539
    old "## About screen"
    new "## Tietoja-näyttö"

    # screens.rpy:541
    old "## This screen gives credit and copyright information about the game and Ren'Py."
    new "## Tämä näyttö antaa kehuja kehittäjille, sekä kopio-oikeusinformaatiota pelistä sekä Ren'Py:stä."

    # screens.rpy:544
    old "## There's nothing special about this screen, and hence it also serves as an example of how to make a custom screen."
    new "## Tässä näytössä ei ole mitään erityistä, minkä vuoksi se toimii hyvin esimerkkinä itse tehdystä näytöstä."

    # screens.rpy:551
    old "## This use statement includes the game_menu screen inside this one. The vbox child is then included inside the viewport inside the game_menu screen."
    new "## Tämä use-lause sisältää game_menu-näytön. Sen alainen vbox-ikkuna sisältyy täten näkymään game_menu-näytön kautta."

    # screens.rpy:561
    old "Version [config.version!t]\n"
    new "Versio [config.version!t]\n"

    # screens.rpy:563
    old "## gui.about is usually set in options.rpy."
    new "## gui.about asetetaan yleensä tiedostossa options.rpy."

    # screens.rpy:567
    old "Made with {a=https://www.renpy.org/}Ren'Py{/a} [renpy.version_only].\n\n[renpy.license!t]"
    new "Kehitetty käyttäen ohjelmaa {a=https://www.renpy.org/}Ren'Py{/a} [renpy.version_only].\n\n[renpy.license!t]"

    # screens.rpy:570
    old "## This is redefined in options.rpy to add text to the about screen."
    new "## Tämä on määritelty uudelleen options.rpy-tiedostossa tekstin lisäämiseksi tietoja-näyttöön."

    # screens.rpy:582
    old "## Load and Save screens"
    new "## Lataa ja Tallenna -näytöt"

    # screens.rpy:584
    old "## These screens are responsible for letting the player save the game and load it again. Since they share nearly everything in common, both are implemented in terms of a third screen, file_slots."
    new "## Nämä näytöt mahdollistavat pelaajan tallentaa pelin edistys ja ladata olemassaolevia tallennuksia. Koska näillä on niin paljon yhteistä, molemmat näytöt on määritelty kolmannen näytön, file_slots, mukaan."

    # screens.rpy:588
    old "## https://www.renpy.org/doc/html/screen_special.html#save https://www.renpy.org/doc/html/screen_special.html#load"
    new "## https://www.renpy.org/doc/html/screen_special.html#save https://www.renpy.org/doc/html/screen_special.html#load"

    # screens.rpy:607
    old "Page {}"
    new "Sivu {}"

    # screens.rpy:607
    old "Automatic saves"
    new "Automaattiset tallennukset"

    # screens.rpy:607
    old "Quick saves"
    new "Pikatallennukset"

    # screens.rpy:613
    old "## This ensures the input will get the enter event before any of the buttons do."
    new "## Tämä varmistaa, että input saa enter-tapahtuman ennen muita painikkeita."

    # screens.rpy:629
    old "## The grid of file slots."
    new "## Tallennuspaikkojen taulukko."

    # screens.rpy:649
    old "{#file_time}%A, %B %d %Y, %H:%M"
    new "{#file_time}%A, %B %d %Y, %H:%M"

    # screens.rpy:649
    old "empty slot"
    new "tyhjä paikka"

    # screens.rpy:657
    old "## Buttons to access other pages."
    new "## Painikkeita muille sivuille pääsyä varten."

    # screens.rpy:666
    old "<"
    new "<"

    # screens.rpy:668
    old "{#auto_page}A"
    new "{#auto_page}A"

    # screens.rpy:670
    old "{#quick_page}Q"
    new "{#quick_page}Q"

    # screens.rpy:676
    old ">"
    new ">"

    # screens.rpy:711
    old "## Preferences screen"
    new "## Asetukset-näyttö"

    # screens.rpy:713
    old "## The preferences screen allows the player to configure the game to better suit themselves."
    new "## Asetukset antavat pelaajan säätää pelin grafiikka, toiminnot sekä äänet mieleisekseen."

    # screens.rpy:716
    old "## https://www.renpy.org/doc/html/screen_special.html#preferences"
    new "## https://www.renpy.org/doc/html/screen_special.html#preferences"

    # screens.rpy:738
    old "Display"
    new "Näyttö"

    # screens.rpy:739
    old "Window"
    new "Ikkuna"

    # screens.rpy:740
    old "Fullscreen"
    new "Koko näyttö"

    # screens.rpy:744
    old "Rollback Side"
    new "Palautussivu"

    # screens.rpy:745
    old "Disable"
    new "Poista käytöstä"

    # screens.rpy:746
    old "Left"
    new "Vasen"

    # screens.rpy:747
    old "Right"
    new "Oikea"

    # screens.rpy:752
    old "Unseen Text"
    new "Lukematon Teksti"

    # screens.rpy:753
    old "After Choices"
    new "Päätösten Jälkeen"

    # screens.rpy:754
    old "Transitions"
    new "Siirtymät"

    # screens.rpy:756
    old "## Additional vboxes of type \"radio_pref\" or \"check_pref\" can be added here, to add additional creator-defined preferences."
    new "## Ylimääräisiä tyyppien \"radio_pref\" tai \"check_pref\" vboxeja voidaan lisätä tähän, antaen pelaajalle ylimääräisiä kehittäjän luomia asetuksia."

    # screens.rpy:767
    old "Text Speed"
    new "Tekstin Nopeus"

    # screens.rpy:771
    old "Auto-Forward Time"
    new "Automaattisen Jatkamisen Viive"

    # screens.rpy:778
    old "Music Volume"
    new "Musiikin Äänenvoimakkuus"

    # screens.rpy:785
    old "Sound Volume"
    new "Ääniefektien Äänenvoimakkuus"

    # screens.rpy:791
    old "Test"
    new "Kokeile"

    # screens.rpy:795
    old "Voice Volume"
    new "Puheen Äänenvoimakkuus"

    # screens.rpy:806
    old "Mute All"
    new "Mykistä Kaikki"

    # screens.rpy:882
    old "## History screen"
    new "## Historia-näyttö"

    # screens.rpy:884
    old "## This is a screen that displays the dialogue history to the player. While there isn't anything special about this screen, it does have to access the dialogue history stored in _history_list."
    new "## Tämä näyttö näyttää dialogihistorian pelaajalle. Vaikka kyseisessä näytössä ei ole mitään erityistä, sillä on pääsy _history_list:iin tallennettuun dialogihistoriaan."

    # screens.rpy:888
    old "## https://www.renpy.org/doc/html/history.html"
    new "## https://www.renpy.org/doc/html/history.html"

    # screens.rpy:894
    old "## Avoid predicting this screen, as it can be very large."
    new "## Vältä ennustamasta tätä näyttöä, sillä se voi olla hyvin suuri."

    # screens.rpy:905
    old "## This lays things out properly if history_height is None."
    new "## Tämä asettelee dialogin kunnnolla, jos history_height on None."

    # screens.rpy:914
    old "## Take the color of the who text from the Character, if set."
    new "## Ota väri hahmon who-tekstistä, jos asetettu."

    # screens.rpy:921
    old "The dialogue history is empty."
    new "Dialogihistoria on tyhjä."

    # screens.rpy:965
    old "## Help screen"
    new "## Ohje-näyttö"

    # screens.rpy:967
    old "## A screen that gives information about key and mouse bindings. It uses other screens (keyboard_help, mouse_help, and gamepad_help) to display the actual help."
    new "## Näyttö, joka tarjoaa tietoa pelin käyttämistä hiiren ja näppäimistön painikkeista sekä niiden toiminnoista. Se käyttää muita näyttöjä (keyboard_help, mouse_help, ja gamepad_help) näyttääkseen itse ohjeet."

    # screens.rpy:986
    old "Keyboard"
    new "Näppäimistö"

    # screens.rpy:987
    old "Mouse"
    new "Hiiri"

    # screens.rpy:990
    old "Gamepad"
    new "Peliohjain"

    # screens.rpy:1003
    old "Enter"
    # Automatic translation.
    new "Kirjoita"

    # screens.rpy:1004
    old "Advances dialogue and activates the interface."
    new "Jatkaa dialogia ja aktivoi valittuja toimintoja."

    # screens.rpy:1007
    old "Space"
    new "Välilyönti"

    # screens.rpy:1008
    old "Advances dialogue without selecting choices."
    new "Jatkaa dialogia tekemättä päätöksiä."

    # screens.rpy:1011
    old "Arrow Keys"
    new "Nuolinäppäimet"

    # screens.rpy:1012
    old "Navigate the interface."
    new "Liiku käyttöliittymässä."

    # screens.rpy:1015
    old "Escape"
    new "Esc"

    # screens.rpy:1016
    old "Accesses the game menu."
    new "Avaa ja sulkee pelivalikon."

    # screens.rpy:1019
    old "Ctrl"
    new "Ctrl"

    # screens.rpy:1020
    old "Skips dialogue while held down."
    new "Sivuuttaa dialogin pidettäessä painettuna."

    # screens.rpy:1023
    old "Tab"
    new "Sarkain"

    # screens.rpy:1024
    old "Toggles dialogue skipping."
    new "Asettaa dialogin sivuutuksen päälle ja pois."

    # screens.rpy:1027
    old "Page Up"
    # Automatic translation.
    new "Sivu ylös"

    # screens.rpy:1028
    old "Rolls back to earlier dialogue."
    new "Siirtyy takaisin aiempaan dialogiin."

    # screens.rpy:1031
    old "Page Down"
    new "Siirtyy uudempaan dialogiin."

    # screens.rpy:1032
    old "Rolls forward to later dialogue."
    new "Vierittää kohti uudempaa dialogia."

    # screens.rpy:1036
    old "Hides the user interface."
    new "Piilottaa käyttöliittymän."

    # screens.rpy:1040
    old "Takes a screenshot."
    new "Ottaa näytönkaappauksen."

    # screens.rpy:1044
    old "Toggles assistive {a=https://www.renpy.org/l/voicing}self-voicing{/a}."
    new "Asettaa auttavan {a=https://www.renpy.org/l/voicing}TTS-tilan{/a} päälle ja pois."

    # screens.rpy:1050
    old "Left Click"
    new "Hiiren Vasen Painike"

    # screens.rpy:1054
    old "Middle Click"
    new "Hiiren Keskipainike"

    # screens.rpy:1058
    old "Right Click"
    new "Hiiren Oikea Painike"

    # screens.rpy:1062
    old "Mouse Wheel Up"
    new "Hiiren Rulla Ylös"

    # screens.rpy:1066
    old "Mouse Wheel Down"
    new "Hiiren Rulla Alas"

    # screens.rpy:1073
    old "Right Trigger\nA/Bottom Button"
    new "Oikea Liipaisin\nA/Alapainike"

    # screens.rpy:1074
    old "Advance dialogue and activates the interface."
    new "Jatka dialogia ja aktivoi valittuja toimintoja."

    # screens.rpy:1078
    old "Roll back to earlier dialogue."
    new "Siirtyy takaisin aiempaan dialogiin."

    # screens.rpy:1081
    old "Right Shoulder"
    new "Oikea Olkapainike"

    # screens.rpy:1082
    old "Roll forward to later dialogue."
    new "Vieritä kohti uudempaa dialogia."

    # screens.rpy:1085
    old "D-Pad, Sticks"
    new "Ristiohjain, Tikut"

    # screens.rpy:1089
    old "Start, Guide"
    new "Start, Opas"

    # screens.rpy:1090
    old "Access the game menu."
    new "Avaa ja sulje pelivalikko."

    # screens.rpy:1093
    old "Y/Top Button"
    new "Y/Yläpainike"

    # screens.rpy:1096
    old "Calibrate"
    new "Kalibroi"

    # screens.rpy:1124
    old "## Additional screens"
    new "## Ylimääräisiä näyttöjä"

    # screens.rpy:1128
    old "## Confirm screen"
    new "## Vahvistusikkuna"

    # screens.rpy:1130
    old "## The confirm screen is called when Ren'Py wants to ask the player a yes or no question."
    new "## Vahvistusikkunaa käytetään, kun Ren'Py haluaa kysyä pelaajalta kyllä tai ei -kysymyksen."

    # screens.rpy:1133
    old "## http://www.renpy.org/doc/html/screen_special.html#confirm"
    new "## http://www.renpy.org/doc/html/screen_special.html#confirm"

    # screens.rpy:1137
    old "## Ensure other screens do not get input while this screen is displayed."
    new "## Varmista, etteivät muut näytöt odota syötettä tämän näytön ollessa näkyvillä."

    # screens.rpy:1161
    old "Yes"
    new "Kyllä"

    # screens.rpy:1162
    old "No"
    new "Ei"

    # screens.rpy:1164
    old "## Right-click and escape answer \"no\"."
    new "## Hiiren oikea painike ja Esc antavat vastauksen \"ei\"."

    # screens.rpy:1191
    old "## Skip indicator screen"
    new "## Sivuutusilmaisin-näyttö"

    # screens.rpy:1193
    old "## The skip_indicator screen is displayed to indicate that skipping is in progress."
    new "## skip_indicator-näyttö on näkyvillä, kun dialogin sivuutuksen tila on käytössä."

    # screens.rpy:1196
    old "## https://www.renpy.org/doc/html/screen_special.html#skip-indicator"
    new "## https://www.renpy.org/doc/html/screen_special.html#skip-indicator"

    # screens.rpy:1208
    old "Skipping"
    new "Sivuutetaan dialogia"

    # screens.rpy:1215
    old "## This transform is used to blink the arrows one after another."
    new "## Tätä muutosta käytetään nuolten vilkkumiseen toistensa perään."

    # screens.rpy:1247
    old "## Notify screen"
    new "## Ilmoitusnäyttö"

    # screens.rpy:1249
    old "## The notify screen is used to show the player a message. (For example, when the game is quicksaved or a screenshot has been taken.)"
    new "## Ilmoitusnäyttöä käytetään näyttämään pelaajalle viesti. (Esimerkiksi kun peli on pikatallennettu tai näytönkaappaus on otettu.)"

    # screens.rpy:1252
    old "## https://www.renpy.org/doc/html/screen_special.html#notify-screen"
    new "## https://www.renpy.org/doc/html/screen_special.html#notify-screen"

    # screens.rpy:1286
    old "## NVL screen"
    new "## NVL-näyttö"

    # screens.rpy:1288
    old "## This screen is used for NVL-mode dialogue and menus."
    new "## Tätä näyttöä käytetään NVL-tilan dialogiin ja valikoihin."

    # screens.rpy:1290
    old "## http://www.renpy.org/doc/html/screen_special.html#nvl"
    new "## http://www.renpy.org/doc/html/screen_special.html#nvl"

    # screens.rpy:1301
    old "## Displays dialogue in either a vpgrid or the vbox."
    new "## Näyttää dialogin joko vpgridissä tai vboxissa."

    # screens.rpy:1314
    old "## Displays the menu, if given. The menu may be displayed incorrectly if config.narrator_menu is set to True, as it is above."
    new "## Näyttää valikon, jos annettu. Valikko voi näyttää väärältä mikäli config.narrator_menu on asetettu arvoon True, kuten yllä."

    # screens.rpy:1344
    old "## This controls the maximum number of NVL-mode entries that can be displayed at once."
    new "## Tämä säätää, kuinka mointa NVL-tilan viestiä voidaan maksimissaan näyttää kerralla."

    # screens.rpy:1406
    old "## Mobile Variants"
    new "## Mobiiliversiot"

    # screens.rpy:1413
    old "## Since a mouse may not be present, we replace the quick menu with a version that uses fewer and bigger buttons that are easier to touch."
    new "## Koska hiirtä ei välttämättä ole, korvaamme pikavalikon versiolla jossa on vähemmän, ja suurempia painikkeita, joita on helpompi koskea."

    # screens.rpy:1429
    old "Menu"
    new "Valikko"


translate finnish strings:

    # gui/game/screens.rpy:114
    old "## If there's a side image, display it above the text. Do not display on the phone variant - there's no room."
    # Automatic translation.
    new "## Jos sivulla on kuva, näytä se tekstin yläpuolella. Älä näytä puhelinmuunnoksessa - siinä ei ole tilaa."

    # gui/game/screens.rpy:120
    old "## Make the namebox available for styling through the Character object."
    # Automatic translation.
    new "## Tee nimilaatikosta muotoiltavissa oleva Character-olio."

    # gui/game/screens.rpy:173
    old "## https://www.renpy.org/doc/html/screen_special.html#input"
    new "## https://www.renpy.org/doc/html/screen_special.html#input"

    # gui/game/screens.rpy:206
    old "## https://www.renpy.org/doc/html/screen_special.html#choice"
    new "## https://www.renpy.org/doc/html/screen_special.html#choice"

    # gui/game/screens.rpy:241
    old "## Ensure this appears on top of other screens."
    # Automatic translation.
    new "## Varmista, että tämä näkyy muiden näyttöjen yläpuolella."

    # gui/game/screens.rpy:280
    old "## Main and Game Menu Screens"
    # Automatic translation.
    new "## Pää- ja pelivalikon näytöt"

    # gui/game/screens.rpy:329
    old "## The quit button is banned on iOS and unnecessary on Android and Web."
    # Automatic translation.
    new "## Lopeta-painike on kielletty iOS:ssä ja tarpeeton Androidissa ja Webissä."

    # gui/game/screens.rpy:348
    old "## https://www.renpy.org/doc/html/screen_special.html#main-menu"
    new "## https://www.renpy.org/doc/html/screen_special.html#main-menu"

    # gui/game/screens.rpy:352
    old "## This ensures that any other menu screen is replaced."
    # Automatic translation.
    new "## Näin varmistetaan, että kaikki muut valikkonäytöt korvataan."

    # gui/game/screens.rpy:357
    old "## This empty frame darkens the main menu."
    # Automatic translation.
    new "## Tämä tyhjä kehys tummentaa päävalikon."

    # gui/game/screens.rpy:429
    old "## Reserve space for the navigation section."
    # Automatic translation.
    new "## Varaa tilaa navigointiosuudelle."

    # gui/game/screens.rpy:608
    old "## The page name, which can be edited by clicking on a button."
    # Automatic translation.
    new "## Sivun nimi, jota voidaan muokata napsauttamalla painiketta."

    # gui/game/screens.rpy:668
    old "## range(1, 10) gives the numbers from 1 to 9."
    # Automatic translation.
    new "## range(1, 10) antaa numerot 1-9."

    # gui/game/screens.rpy:676
    old "Upload Sync"
    # Automatic translation.
    new "Lataa synkronointi"

    # gui/game/screens.rpy:680
    old "Download Sync"
    # Automatic translation.
    new "Lataa Sync"

    # gui/game/screens.rpy:921
    old "## This determines what tags are allowed to be displayed on the history screen."
    # Automatic translation.
    new "## Tämä määrittää, mitä tunnisteita saa näyttää historiaruudulla."

    # gui/game/screens.rpy:1049
    old "Opens the accessibility menu."
    # Automatic translation.
    new "Avaa saavutettavuusvalikon."

    # gui/game/screens.rpy:1082
    old "Left Trigger\nLeft Shoulder"
    # Automatic translation.
    new "Vasen liipaisin\nVasen olkapää"

    # gui/game/screens.rpy:1139
    old "## https://www.renpy.org/doc/html/screen_special.html#confirm"
    new "## https://www.renpy.org/doc/html/screen_special.html#confirm"

    # gui/game/screens.rpy:1248
    old "## We have to use a font that has the BLACK RIGHT-POINTING SMALL TRIANGLE glyph in it."
    # Automatic translation.
    new "## Meidän on käytettävä fonttia, jossa on BLACK RIGHT-POINTING SMALL TRIANGLE -lyhenne."

    # gui/game/screens.rpy:1296
    old "## https://www.renpy.org/doc/html/screen_special.html#nvl"
    new "## https://www.renpy.org/doc/html/screen_special.html#nvl"

    # gui/game/screens.rpy:1320
    old "## Displays the menu, if given. The menu may be displayed incorrectly if config.narrator_menu is set to True."
    # Automatic translation.
    new "## Näyttää valikon, jos se on annettu. Valikko saatetaan näyttää virheellisesti, jos config.narrator_menu on asetettu arvoon True."

    # gui/game/screens.rpy:1410
    old "## Bubble screen"
    # Automatic translation.
    new "## Kupla näyttö"

    # gui/game/screens.rpy:1412
    old "## The bubble screen is used to display dialogue to the player when using speech bubbles. The bubble screen takes the same parameters as the say screen, must create a displayable with the id of \"what\", and can create displayables with the \"namebox\", \"who\", and \"window\" ids."
    # Automatic translation.
    new "## Puhekuplaikkunaa käytetään näyttämään dialogi pelaajalle, kun käytetään puhekuplia. Bubble screen ottaa samat parametrit kuin say screen, sen on luotava displayable, jonka id on \"what\", ja se voi luoda displayables, joiden id:t ovat \"namebox\", \"who\" ja \"window\"."

    # gui/game/screens.rpy:1417
    old "## https://www.renpy.org/doc/html/bubble.html#bubble-screen"
    new "## https://www.renpy.org/doc/html/bubble.html#bubble-screen"


translate finnish strings:

    # gui/game/screens.rpy:411
    old "## The scroll parameter can be None, or one of \"viewport\" or \"vpgrid\". This screen is intended to be used with one or more children, which are transcluded (placed) inside it."
    # Automatic translation.
    new "## Selausparametri voi olla None tai jokin seuraavista: \"viewport\" tai \"vpgrid\". Tämä ruutu on tarkoitettu käytettäväksi yhden tai useamman lapsen kanssa, jotka siirretään (sijoitetaan) sen sisään."

