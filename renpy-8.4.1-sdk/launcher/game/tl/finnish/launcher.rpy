translate finnish strings:
    # game/new_project.rpy:77
    old "{#language name and font}"
    new "Suomi"

    # about.rpy:39
    old "[version!q]"
    new "[version!q]"

    # about.rpy:43
    old "View license"
    new "Näytä lisenssi"

    # add_file.rpy:28
    old "FILENAME"
    new "TIEDOSTONIMI"

    # add_file.rpy:28
    old "Enter the name of the script file to create."
    new "<PERSON><PERSON><PERSON><PERSON> s<PERSON> nimi, jonka haluat luoda."

    # add_file.rpy:31
    old "The filename must have the .rpy extension."
    new "Tiedostolla on oltava .rpy-tunniste."

    # add_file.rpy:39
    old "The file already exists."
    new "Tämänniminen tiedosto on jo olemassa."

    # add_file.rpy:42
    old "# Ren'Py automatically loads all script files ending with .rpy. To use this\n# file, define a label and jump to it from another file.\n"
    new "# Ren'Py lataa automaattisesti kaikki scriptitiedostot, joilla on .rpy pääte. Käyttääksesi tätä\n# tiedostoa, luo label ja hyppää siihen toisesta tiedostosta.\n"

    # android.rpy:30
    old "To build Android packages, please download RAPT, unzip it, and place it into the Ren'Py directory. Then restart the Ren'Py launcher."
    new "Luodaksesi Android-sovelluksia, ole hyvä ja lataa RAPT, pura se, ja aseta se Ren'Py kansioon. Tämän jälkeen käynnistä Ren'Py uudelleen."

    # android.rpy:31
    old "An x86 Java Development Kit is required to build Android packages on Windows. The JDK is different from the JRE, so it's possible you have Java without having the JDK.\n\nPlease {a=http://www.oracle.com/technetwork/java/javase/downloads/jdk8-downloads-2133151.html}download and install the JDK{/a}, then restart the Ren'Py launcher."
    new "32-bittinen Javan kehitystyökalu vaaditaan Android-sovellusten luomiseen Windowsilla. JDK on erilainen kuin JRE, joten on mahdollista, että sinulla on Java ilman JDK:ta.\n\n{a=http://www.oracle.com/technetwork/java/javase/downloads/jdk8-downloads-2133151.html}Lataa ja asenna JDK{/a}, ja sen jälkeen käynnistä Ren'Py uudelleen."

    # android.rpy:32
    old "RAPT has been installed, but you'll need to install the Android SDK before you can build Android packages. Choose Install SDK to do this."
    new "RAPT on asennettu, mutta sinun on asennettava Android SDK ennen kuin voit luoda Android-sovelluksia. Valitse 'Asenna SDK' tehdäksesi tämän."

    # android.rpy:33
    old "RAPT has been installed, but a key hasn't been configured. Please create a new key, or restore android.keystore."
    new "RAPT on asennettu, mutta avainta ei ole konfiguroitu. Luo uusi avain, tai palauta android.keystore."

    # android.rpy:34
    old "The current project has not been configured. Use \"Configure\" to configure it before building."
    new "Valittua projektia ei ole konfiguroitu. Käytä \"Konfigurointi\" -toimintoa konfiguroidaksesi sen ennen sovelluksen kokoamista."

    # android.rpy:35
    old "Choose \"Build\" to build the current project, or attach an Android device and choose \"Build & Install\" to build and install it on the device."
    new "Valitse \"Kokoa\" kootaksesi projektin, tai kiinnitä Android-laite ja valitse \"Kokoa ja asenna\" kootaksesi ja asentaaksesi sen laitteeseen."

    # android.rpy:37
    old "Attempts to emulate an Android phone.\n\nTouch input is emulated through the mouse, but only when the button is held down. Escape is mapped to the menu button, and PageUp is mapped to the back button."
    new "Ren'Py pyrkii emuloimaan Android-puhelinta.\n\nKosketus valintaa emuloidaan hiirellä, mutta vain kun nappia pidetään pohjassa. Esc toimii Menu-näppäimenä ja Pg Up toimii peruutusnappina."

    # android.rpy:38
    old "Attempts to emulate an Android tablet.\n\nTouch input is emulated through the mouse, but only when the button is held down. Escape is mapped to the menu button, and PageUp is mapped to the back button."
    new "Ren'Py pyrkii emuloimaan Android-tablettia.\n\nKosketusta emuloidaan hiirellä, mutta vain kun nappia pidetään pohjassa. Esc toimii Menu-näppäimenä ja Pg Up toimii peruutusnappina."

    # android.rpy:39
    old "Attempts to emulate a televison-based Android console, like the OUYA or Fire TV.\n\nController input is mapped to the arrow keys, Enter is mapped to the select button, Escape is mapped to the menu button, and PageUp is mapped to the back button."
    new "Ren'Py pyrkii emuloimaan televisiopohjaista Android-konsolia, kuten OUYA tai Fire TV.\n\nOhjainnäppäimet ovat nuolinäppäimet, Enter toimii valitsijana, Esc toimii Menu-näppäimenä ja Pg Up toimii peruutusnappina."

    # android.rpy:41
    old "Downloads and installs the Android SDK and supporting packages. Optionally, generates the keys required to sign the package."
    new "Lataa ja asentaa Android SDK:n ja tarvittavat lisäpaketit. Lisäksi, voidaan käyttää avainten luomiseen."

    # android.rpy:42
    old "Configures the package name, version, and other information about this project."
    new "Konfiguroi paketin nimen, version sekä kaiken muun projektia koskevan tiedon."

    # android.rpy:43
    old "Opens the file containing the Google Play keys in the editor.\n\nThis is only needed if the application is using an expansion APK. Read the documentation for more details."
    new "Avaa tiedoston, joka sisältää Google Play -avaimet, editorissa.\n\nTätä tarvitaan vain, jos sovellus käyttää laajennus-APK:ta. Lue dokumentaatio saadaksesi lisätietoa."

    # android.rpy:44
    old "Builds the Android package."
    new "Kokoaa Android-sovelluksen."

    # android.rpy:45
    old "Builds the Android package, and installs it on an Android device connected to your computer."
    new "Kokoaa Android-sovelluksen, ja asentaa sen tietokoneeseen kytkettyyn Android-laitteeseen."

    # android.rpy:46
    old "Builds the Android package, installs it on an Android device connected to your computer, then launches the app on your device."
    new "Kokoaa Android-sovelluksen, asentaa sen tietokoneeseen kytkettyyn Android-laitteeseen ja käynnistää sovelluksen laitteessa."

    # android.rpy:48
    old "Connects to an Android device running ADB in TCP/IP mode."
    new "Yhdistää Android-laitteeseen, joka pyörittää ADB:tä TCP/IP -tilassa."

    # android.rpy:49
    old "Disconnects from an Android device running ADB in TCP/IP mode."
    new "Katkaisee yhteyden Android-laitteeseen, joka pyörittää ADB:tä TCP/IP -tilassa."

    # android.rpy:50
    old "Retrieves the log from the Android device and writes it to a file."
    new "Noutaa lokitiedot Android-laitteesta ja luo niistä tiedoston."

    # android.rpy:240
    old "Copying Android files to distributions directory."
    new "Kopioidaan Android-tiedostoja jakelukansioon."

    # android.rpy:304
    old "Android: [project.current.name!q]"
    new "Android: [project.current.name!q]"

    # android.rpy:324
    old "Emulation:"
    new "Emulointi:"

    # android.rpy:333
    old "Phone"
    new "Puhelin"

    # android.rpy:337
    old "Tablet"
    new "Tabletti"

    # android.rpy:341
    old "Television"
    new "Televisio"

    # android.rpy:353
    old "Build:"
    new "Kokoa:"

    # android.rpy:361
    old "Install SDK & Create Keys"
    new "Asenna SDK & Luo Avaimet"

    # android.rpy:365
    old "Configure"
    new "Muokkaa Asetuksia"

    # android.rpy:369
    old "Build Package"
    new "Kokoa Sovellus"

    # android.rpy:373
    old "Build & Install"
    new "Kokoa & Asenna"

    # android.rpy:377
    old "Build, Install & Launch"
    new "Kokoa, Asenna & Aja"

    # android.rpy:388
    old "Other:"
    new "Muita:"

    # android.rpy:396
    old "Remote ADB Connect"
    new "Langaton ADB-yhdistäminen"

    # android.rpy:400
    old "Remote ADB Disconnect"
    new "Langaton ADB-katkaisu"

    # android.rpy:404
    old "Logcat"
    new "Logcat"

    # android.rpy:437
    old "Before packaging Android apps, you'll need to download RAPT, the Ren'Py Android Packaging Tool. Would you like to download RAPT now?"
    new "Ennen Android-sovellusten kokoamista, sinun on ladattava RAPT (Ren'Py Android Packaging Tool). Haluatko ladata RAPT:in nyt?"

    # android.rpy:496
    old "Remote ADB Address"
    new "Langaton ADB-osoite"

    # android.rpy:496
    old "Please enter the IP address and port number to connect to, in the form \"*************:5555\". Consult your device's documentation to determine if it supports remote ADB, and if so, the address and port to use."
    new "Syötä yhdistettävän kohteen IP-osoite ja porttinumero, esimerkin \"*************:5555\" mukaisesti. Selvitä laitteesi tiedoista kykeneekö se langattomaan ADB:hen, ja jos näin on, käytettävä osoite ja portti."

    # android.rpy:508
    old "Invalid remote ADB address"
    new "Virheellinen langaton ADB-osoite"

    # android.rpy:508
    old "The address must contain one exactly one ':'."
    new "Osoitteeseen täytyy kuulua tasan yksi ':'."

    # android.rpy:512
    old "The host may not contain whitespace."
    new "IP-osoite ei saa sisältää välilyöntejä."

    # android.rpy:518
    old "The port must be a number."
    new "Portin on oltava numero."

    # android.rpy:544
    old "Retrieving logcat information from device."
    new "Noudetaan logcat-tietoja laitteesta."

    # choose_directory.rpy:73
    old "Ren'Py was unable to run python with tkinter to choose the directory. Please install the python-tk or tkinter package."
    new "Ren'Py ei kyennyt suorittamaan pythonia tkinterillä valitakseen kansion. Ole hyvä ja asenna python-tk tai tkinter-paketti."

    # choose_theme.rpy:303
    old "Could not change the theme. Perhaps options.rpy was changed too much."
    new "Teeman vaihto epäonnistui. Options.rpy-tiedostoa on ehkä muokattu liikaa."

    # choose_theme.rpy:370
    old "Planetarium"
    new "Planetaario"

    # choose_theme.rpy:425
    old "Choose Theme"
    new "Valitse teema"

    # choose_theme.rpy:438
    old "Theme"
    new "Teema"

    # choose_theme.rpy:463
    old "Color Scheme"
    new "Väriskaala"

    # choose_theme.rpy:495
    old "Continue"
    new "Jatka"

    # consolecommand.rpy:84
    old "INFORMATION"
    new "INFORMAATIO"

    # consolecommand.rpy:84
    old "The command is being run in a new operating system console window."
    new "Komentoa ajetaan uuudessa käyttöjärjestelmän konsoli-ikkunassa."

    # distribute.rpy:443
    old "Scanning project files..."
    new "Skannataan projektin tiedostoja..."

    # distribute.rpy:459
    old "Building distributions failed:\n\nThe build.directory_name variable may not include the space, colon, or semicolon characters."
    new "Ohjelman kokoaminen epäonnistui:\n\nbuild.directory_name -muuttuja ei saa sisältää välilyöntejä, pilkkuja tai puolipilkkuja."

    # distribute.rpy:504
    old "No packages are selected, so there's nothing to do."
    new "Yhtäkään pakettia ei valittu, joten mitään ei ole tehtävissä."

    # distribute.rpy:516
    old "Scanning Ren'Py files..."
    new "Skannataan Ren'Py-tiedostoja..."

    # distribute.rpy:569
    old "All packages have been built.\n\nDue to the presence of permission information, unpacking and repacking the Linux and Macintosh distributions on Windows is not supported."
    new "Kaikki paketit on koottu.\n\nLupainformaation läsnäolon vuoksi Linux- ja Macintosh-sovellusten pakkaamista Windows-järjestelmässä ei tueta."

    # distribute.rpy:752
    old "Archiving files..."
    new "Arkistoidaan tiedostoja..."

    # distribute.rpy:1050
    old "Unpacking the Macintosh application for signing..."
    new "Puretaan Macintosh-sovellusta allekirjoitusta varten..."

    # distribute.rpy:1060
    old "Signing the Macintosh application..."
    new "Allekirjoitetaan Macintosh-sovellusta..."

    # distribute.rpy:1082
    old "Creating the Macintosh DMG..."
    new "Luodaan Macintosh DMG-tiedostoa..."

    # distribute.rpy:1091
    old "Signing the Macintosh DMG..."
    new "Allekirjoitetaan Macintosh DMG-tiedostoa..."

    # distribute.rpy:1248
    old "Writing the [variant] [format] package."
    new "Kirjoitetaan [variant] [format]-pakettia."

    # distribute.rpy:1261
    old "Making the [variant] update zsync file."
    new "Luodaan [variant] zsync-päivitystiedostoa."

    # distribute.rpy:1404
    old "Processed {b}[complete]{/b} of {b}[total]{/b} files."
    new "Käsitelty {b}[complete]{/b}/{b}[total]{/b}:sta tiedostosta."

    # distribute_gui.rpy:157
    old "Build Distributions: [project.current.name!q]"
    new "Kokoa ohjelma levitykseen: [project.current.name!q]"

    # distribute_gui.rpy:171
    old "Directory Name:"
    new "Kansion nimi:"

    # distribute_gui.rpy:175
    old "Executable Name:"
    new "Suoritettavan tiedoston nimi:"

    # distribute_gui.rpy:185
    old "Actions:"
    new "Toimet:"

    # distribute_gui.rpy:193
    old "Edit options.rpy"
    new "Muokkaa options.rpy-tiedostoa"

    # distribute_gui.rpy:194
    old "Add from clauses to calls, once"
    new "Lisää from-ehdot kutsuihin, kerran"

    # distribute_gui.rpy:195
    old "Refresh"
    new "Päivitä"

    # distribute_gui.rpy:199
    old "Upload to itch.io"
    new "Lataa itch.io-palveluun"

    # distribute_gui.rpy:215
    old "Build Packages:"
    new "Kokoa sovelluksia:"

    # distribute_gui.rpy:234
    old "Options:"
    new "Valinnat:"

    # distribute_gui.rpy:239
    old "Build Updates"
    new "Kokoa päivityksiä"

    # distribute_gui.rpy:241
    old "Add from clauses to calls"
    new "Lisää from-ehdot kutsuihin"

    # distribute_gui.rpy:242
    old "Force Recompile"
    new "Pakota uudelleenkokoaminen"

    # distribute_gui.rpy:246
    old "Build"
    new "Kokoa sovellus"

    # distribute_gui.rpy:250
    old "Adding from clauses to call statements that do not have them."
    new "Lisätään from-ehtoja kutsuihin, joilla ei ole niitä."

    # distribute_gui.rpy:271
    old "Errors were detected when running the project. Please ensure the project runs without errors before building distributions."
    new "Virheitä havaittu projektia suoritettaessa. Varmista, että projekti toimii moitteetta ennen kuin kokoat ohjelman."

    # distribute_gui.rpy:288
    old "Your project does not contain build information. Would you like to add build information to the end of options.rpy?"
    new "Projektisi ei sisällä kokoamisinformaatiota. Haluatko lisätä kokoamisinormaation options.rpy-tiedoston loppuun?"

    # editor.rpy:150
    old "{b}Recommended.{/b} A beta editor with an easy to use interface and features that aid in development, such as spell-checking. Editra currently lacks the IME support required for Chinese, Japanese, and Korean text input."
    new "{b}Suositeltu.{/b} Helppokäyttöinen beta-asteella oleva tekstieditori, joka sisältää kehitystä helpottavia toimintoja. Toistaiseksi Editrasta puuttuu täysin kiinalaisen, japanilaisen ja korealaisen tekstin kannatus."

    # editor.rpy:151
    old "{b}Recommended.{/b} A beta editor with an easy to use interface and features that aid in development, such as spell-checking. Editra currently lacks the IME support required for Chinese, Japanese, and Korean text input. On Linux, Editra requires wxPython."
    new "{b}Suositeltu.{/b} Helppokäyttöinen beta-asteella oleva tekstieditori, joka sisältää kehitystä helpottavia toimintoja. Toistaiseksi Editrasta puuttuu täysin kiinalaisen, japanilaisen ja korealaisen tekstin kannatus."

    # editor.rpy:167
    old "This may have occurred because wxPython is not installed on this system."
    new "Tämä saattoi tapahtua, koska wxPythonia ei ole asennettu järjestelmään."

    # editor.rpy:169
    old "Up to 22 MB download required."
    new "Noin 22 MB lataus vaaditaan"

    # editor.rpy:182
    old "A mature editor that requires Java."
    new "Edistynyt editori, joka vaatii Javan."

    # editor.rpy:182
    old "1.8 MB download required."
    new "1.8 MB lataus vaaditaan."

    # editor.rpy:182
    old "This may have occurred because Java is not installed on this system."
    new "Tämä saattoi tapahtua, koska Javaa ei ole asennettu."

    # editor.rpy:191
    old "Invokes the editor your operating system has associated with .rpy files."
    new "Käyttää editoria, jota käyttöjärjestelmäsi käyttää .rpy-tiedostojen kanssa."

    # editor.rpy:207
    old "Prevents Ren'Py from opening a text editor."
    new "Estää Ren'Py-ohjelmaa avaamasta tekstieditoria."

    # editor.rpy:359
    old "An exception occurred while launching the text editor:\n[exception!q]"
    new "Virhe käynnistäessä tekstieditoria:\n[exception!q]"

    # editor.rpy:457
    old "Select Editor"
    new "Valitse editori"

    # editor.rpy:472
    old "A text editor is the program you'll use to edit Ren'Py script files. Here, you can select the editor Ren'Py will use. If not already present, the editor will be automatically downloaded and installed."
    new "Tekstieditori on ohjelma, jota käytät Ren'Py-skriptitiedostojen muokkaamiseen. Täällä voit valita, mitä editoria Ren'Py käyttää. Jos valitsemaasi editoria ei vielä ole asennettu, se ladataan ja asennetaan automaattisesti."

    # editor.rpy:494
    old "Cancel"
    new "Peruuta"

    # front_page.rpy:35
    old "Open [text] directory."
    new "Avaa [text] kansio."

    # front_page.rpy:93
    old "refresh"
    new "päivitä"

    # front_page.rpy:120
    old "+ Create New Project"
    new "+ Luo Uusi Projekti"

    # front_page.rpy:130
    old "Launch Project"
    new "Suorita Projekti"

    # front_page.rpy:147
    old "[p.name!q] (template)"
    # Automatic translation.
    new "[p.name!q] (malli)"

    # front_page.rpy:149
    old "Select project [text]."
    new "Valitse projekti [text]."

    # front_page.rpy:165
    old "Tutorial"
    new "Apukurssi"

    # front_page.rpy:166
    old "The Question"
    # Automatic translation.
    new "Kysymys"

    # front_page.rpy:182
    old "Active Project"
    new "Aktiivinen projekti"

    # front_page.rpy:190
    old "Open Directory"
    new "Avaa kansio"

    # front_page.rpy:195
    old "game"
    new "game"

    # front_page.rpy:196
    old "base"
    new "base"

    # front_page.rpy:197
    old "images"
    new "images"

    # front_page.rpy:198
    old "gui"
    new "gui"

    # front_page.rpy:204
    old "Edit File"
    new "Muokkaa tiedostoa"

    # front_page.rpy:214
    old "All script files"
    new "Kaikki skriptitiedostot"

    # front_page.rpy:223
    old "Navigate Script"
    new "Selaa skriptejä"

    # front_page.rpy:234
    old "Check Script (Lint)"
    new "Tarkista skripti (Lint)"

    # front_page.rpy:237
    old "Change/Update GUI"
    new "Muokkaa/Päivitä GUI"

    # front_page.rpy:239
    old "Change Theme"
    new "Vaihda teemaa"

    # front_page.rpy:242
    old "Delete Persistent"
    new "Poista pysyvästi tallennettu data"

    # front_page.rpy:251
    old "Build Distributions"
    new "Kokoa jakeluun"

    # front_page.rpy:253
    old "Android"
    new "Android"

    # front_page.rpy:254
    old "iOS"
    new "iOS"

    # front_page.rpy:255
    old "Generate Translations"
    new "Luo käännöksiä"

    # front_page.rpy:256
    old "Extract Dialogue"
    new "Kopioi dialogi"

    # front_page.rpy:272
    old "Checking script for potential problems..."
    new "Tarkistetaan skriptiä mahdollisten ongelmien varalta..."

    # front_page.rpy:287
    old "Deleting persistent data..."
    new "Poistetaan pysyvästi tallennettua dataa..."

    # front_page.rpy:295
    old "Recompiling all rpy files into rpyc files..."
    new "Muodostetaan kaikista rpy-tiedostoista rpyc-tiedostoja..."

    # gui7.rpy:236
    old "Select Accent and Background Colors"
    new "Valitse Korostus- ja Taustavärit"

    # gui7.rpy:250
    old "Please click on the color scheme you wish to use, then click Continue. These colors can be changed and customized later."
    new "Ole hyvä ja valitse haluamasi väriteema, ja klikkaa Seuraava. Näitä värejä voi vaihtaa ja muokata myöhemmin."

    # gui7.rpy:294
    old "{b}Warning{/b}\nContinuing will overwrite customized bar, button, save slot, scrollbar, and slider images.\n\nWhat would you like to do?"
    new "{b}varoitus{/b}\nJatkamalla yliajat muokatut palkki-, painike-, tallennus-, vierityspalkki-, ja liukuvalintakuvat.\n\nHaluatko jatkaa?"

    # gui7.rpy:294
    old "Choose new colors, then regenerate image files."
    new "Valitse uudet värit, ja luo kuvatiedostot uudelleen."

    # gui7.rpy:294
    old "Regenerate the image files using the colors in gui.rpy."
    new "Luo kuvatiedostot uudelleen käyttäen värejä gui.rpy-tiedostossa."

    # gui7.rpy:314
    old "PROJECT NAME"
    new "PROJEKTIN NIMI"

    # gui7.rpy:314
    old "Please enter the name of your project:"
    new "Ole hyvä ja nimeä projektisi:"

    # gui7.rpy:322
    old "The project name may not be empty."
    new "Projektin nimi ei voi olla tyhjä."

    # gui7.rpy:327
    old "[project_name!q] already exists. Please choose a different project name."
    new "[project_name!q] on jo olemassa. Ole hyvä ja valitse toinen nimi projektillesi."

    # gui7.rpy:330
    old "[project_dir!q] already exists. Please choose a different project name."
    new "[project_dir!q] on jo olemassa. Ole hyvä ja valitse toinen nimi projektillesi."

    # gui7.rpy:341
    old "What resolution should the project use? Although Ren'Py can scale the window up and down, this is the initial size of the window, the size at which assets should be drawn, and the size at which the assets will be at their sharpest.\n\nThe default of [default_size[0]]x[default_size[1]] is a reasonable compromise."
    new "Mitä resoluutiota projektin tulisi käyttää? Vaikka Ren'Py kykenee skaalaamaan ikkunaa molempiin suuntiin, tämä on ikkunan alustava koko, resoluutio jolla grafiikka piirretään, ja koko jossa grafiikka on terävimmillään.\n\nOletusasetus [default_size[0]]x[default_size[1]] on varteenotettava kompromissi."

    # gui7.rpy:389
    old "Creating the new project..."
    new "Luodaan uutta projektia..."

    # gui7.rpy:391
    old "Updating the project..."
    new "Päivitetään projektia..."

    # interface.rpy:107
    old "Documentation"
    new "Dokumentaatio"

    # interface.rpy:108
    old "Ren'Py Website"
    new "Ren'Py:n verkkosivut"

    # interface.rpy:109
    old "Ren'Py Games List"
    new "Lista Ren'Py-peleistä"

    # interface.rpy:117
    old "update"
    new "päivitä"

    # interface.rpy:119
    old "preferences"
    new "asetukset"

    # interface.rpy:120
    old "quit"
    new "poistu"

    # interface.rpy:232
    old "Due to package format limitations, non-ASCII file and directory names are not allowed."
    new "Pakkausformaatin rajoitusten vuoksi, ei-ASCII tiedostot ja kansion nimet eivät ole sallittuja."

    # interface.rpy:327
    old "ERROR"
    new "VIRHE"

    # interface.rpy:356
    old "While [what!qt], an error occurred:"
    new "Kun [what!qt], tapahtui virhe:"

    # interface.rpy:356
    old "[exception!q]"
    new "[exception!q]"

    # interface.rpy:375
    old "Text input may not contain the {{ or [[ characters."
    new "Teksti ei saa sisältää {{ tai [[-merkkejä."

    # interface.rpy:380
    old "File and directory names may not contain / or \\."
    new "Tiedostojen ja kansioiden nimet eivät saa sisältää / tai \\-merkkejä."

    # interface.rpy:386
    old "File and directory names must consist of ASCII characters."
    new "Tiedostojen ja kansioiden nimet saavat koostua vain ASCII-merkeistä."

    # interface.rpy:454
    old "PROCESSING"
    new "KÄSITELLÄÄN"

    # interface.rpy:471
    old "QUESTION"
    new "KYSYMYS"

    # interface.rpy:484
    old "CHOICE"
    new "VALINTA"

    # ios.rpy:28
    old "To build iOS packages, please download renios, unzip it, and place it into the Ren'Py directory. Then restart the Ren'Py launcher."
    new "Luodaksesi iOS-sovelluksia, ole hyvä ja lataa renios, pura se, ja aseta se Ren'Py kansioon. Tämän jälkeen käynnistä Ren'Py uudelleen."

    # ios.rpy:29
    old "The directory in where Xcode projects will be placed has not been selected. Choose 'Select Directory' to select it."
    new "Kansiota, johon Xcode-projektit asetetaan, ei ole valittu. Klikkaa 'Valitse kansio' valitaksesi sen."

    # ios.rpy:30
    old "There is no Xcode project corresponding to the current Ren'Py project. Choose 'Create Xcode Project' to create one."
    new "Nykyistä Ren'Py-projektia vastaavaa Xcode-projektia ei löydy. Klikkaa 'Luo Xcode-projekti' luodaksesi sellaisen."

    # ios.rpy:31
    old "An Xcode project exists. Choose 'Update Xcode Project' to update it with the latest game files, or use Xcode to build and install it."
    new "Xcode-projekti on jo olemassa. Klikkaa 'Päivitä Xcode-projekti' päivittääksesi sen uusimmilla pelin tiedostoilla, tai käytä Xcodea kootaksesi ja asentaaksesi sen."

    # ios.rpy:33
    old "Attempts to emulate an iPhone.\n\nTouch input is emulated through the mouse, but only when the button is held down."
    new "Ren'Py pyrkii emuloimaan iPhonea.\n\nKosketusta emuloidaan hiirellä, mutta vain kun nappia pidetään pohjassa."

    # ios.rpy:34
    old "Attempts to emulate an iPad.\n\nTouch input is emulated through the mouse, but only when the button is held down."
    new "Ren'Py pyrkii emuloimaan iPadia.\n\nKosketusta emuloidaan hiirellä, mutta vain kun nappia pidetään pohjassa."

    # ios.rpy:36
    old "Selects the directory where Xcode projects will be placed."
    new "Valitsee kansion, johon Xcode-projektit sijoitetaan."

    # ios.rpy:37
    old "Creates an Xcode project corresponding to the current Ren'Py project."
    new "Luo Xcode-projektin tällä hetkellä valittuna olevasta Ren'Py-projektista."

    # ios.rpy:38
    old "Updates the Xcode project with the latest game files. This must be done each time the Ren'Py project changes."
    new "Päivittää Xcode-projektin uusimmilla pelitiedostoilla. Tämä on tehtävä joka kerta Ren'Py-projektin muuttuessa."

    # ios.rpy:39
    old "Opens the Xcode project in Xcode."
    new "Avaa Xcode-projektin Xcodessa."

    # ios.rpy:41
    old "Opens the directory containing Xcode projects."
    new "Avaa Xcode-projektit sisältävän kansion."

    # ios.rpy:126
    old "The Xcode project already exists. Would you like to rename the old project, and replace it with a new one?"
    new "Tämänniminen Xcode-projekti on jo olemassa. Haluatko nimetä vanhan projektin uudelleen ja korvata sen uudella?"

    # ios.rpy:211
    old "iOS: [project.current.name!q]"
    new "iOS: [project.current.name!q]"

    # ios.rpy:240
    old "iPhone"
    new "iPhone"

    # ios.rpy:244
    old "iPad"
    new "iPad"

    # ios.rpy:264
    old "Select Xcode Projects Directory"
    new "Valitse Xcode-projektikansio"

    # ios.rpy:268
    old "Create Xcode Project"
    new "Luo Xcode-projekti"

    # ios.rpy:272
    old "Update Xcode Project"
    new "Päivitä Xcode-projekti"

    # ios.rpy:277
    old "Launch Xcode"
    new "Käynnistä Xcode"

    # ios.rpy:312
    old "Open Xcode Projects Directory"
    new "Avaa Xcode-projektikansio"

    # ios.rpy:345
    old "Before packaging iOS apps, you'll need to download renios, Ren'Py's iOS support. Would you like to download renios now?"
    new "Ennen iOS-sovellusten pakkaamista sinun on ladattava renios, Ren'Py:n iOS-tuki. Haluaisitko ladata reniosin nyt?"

    # ios.rpy:354
    old "XCODE PROJECTS DIRECTORY"
    new "XCODE-PROJEKTIKANSIO"

    # ios.rpy:354
    old "Please choose the Xcode Projects Directory using the directory chooser.\n{b}The directory chooser may have opened behind this window.{/b}"
    new "Ole hyvä ja valitse Xcode-projektikansio käyttämällä resurssienhallintaa.\n{b}Kansion valinta on saattanut aueta tämän ikkunan taakse.{/b}"

    # ios.rpy:359
    old "Ren'Py has set the Xcode Projects Directory to:"
    new "Ren'Py on asettanut Xcode-projektikansioksi:"

    # itch.rpy:60
    old "The built distributions could not be found. Please choose 'Build' and try again."
    new "Koottuja jakeluita ei löytynyt. Valitse 'Kokoa' ja yritä uudelleen."

    # itch.rpy:91
    old "No uploadable files were found. Please choose 'Build' and try again."
    new "Ladattavia tiedostoja ei löytynyt. Valitse 'Kokoa' ja yritä uudelleen."

    # itch.rpy:99
    old "The butler program was not found."
    new "Butler-ohjelmaa ei löytynyt."

    # itch.rpy:99
    old "Please install the itch.io app, which includes butler, and try again."
    new "Ole hyvä ja asenna itch.io-sovellus, joka sisältää butlerin, ja yritä uudelleen."

    # itch.rpy:108
    old "The name of the itch project has not been set."
    new "Itch-projektin nimeä ei ole asetettu."

    # itch.rpy:108
    old "Please {a=https://itch.io/game/new}create your project{/a}, then add a line like \n{vspace=5}define build.itch_project = \"user-name/game-name\"\n{vspace=5} to options.rpy."
    new "Ole hyvä ja {a=https://itch.io/game/new}luo projektisi{/a}, ja tämän jälkeen lisää rivi kuten \n{vspace=5}define build.itch_project = \"user-name/game-name\"\n{vspace=5} options.rpy-tiedostoon."

    # mobilebuild.rpy:109
    old "{a=%s}%s{/a}"
    new "{a=%s}%s{/a}"

    # navigation.rpy:168
    old "Navigate: [project.current.name]"
    new "Navigoi: [project.current.name]"

    # navigation.rpy:177
    old "Order: "
    new "Järjestys: "

    # navigation.rpy:178
    old "alphabetical"
    new "aakkosellinen"

    # navigation.rpy:180
    old "by-file"
    new "tiedoston mukaan"

    # navigation.rpy:182
    old "natural"
    new "luonnollinen"

    # navigation.rpy:194
    old "Category:"
    new "Kategoria:"

    # navigation.rpy:196
    old "files"
    new "tiedostot"

    # navigation.rpy:197
    old "labels"
    new "tunnukset"

    # navigation.rpy:198
    old "defines"
    new "määritelmät"

    # navigation.rpy:199
    old "transforms"
    new "muutokset"

    # navigation.rpy:200
    old "screens"
    new "ikkunat"

    # navigation.rpy:201
    old "callables"
    new "kutsuttavat"

    # navigation.rpy:202
    old "TODOs"
    new "TODO:t"

    # navigation.rpy:241
    old "+ Add script file"
    new "+ Lisää skriptitiedosto"

    # navigation.rpy:249
    old "No TODO comments found.\n\nTo create one, include \"# TODO\" in your script."
    new "TODO-kommentteja ei löytynyt.\n\nLuodaksesi sellaisen, lisää \"# TODO\" skriptiisi."

    # navigation.rpy:256
    old "The list of names is empty."
    new "Nimilista on tyhjä."

    # new_project.rpy:38
    old "New GUI Interface"
    new "Uusi GUI-käyttöliittymä"

    # new_project.rpy:48
    old "Both interfaces have been translated to your language."
    new "Molemmat käyttöliittymät on käännetty kielellesi."

    # new_project.rpy:50
    old "Only the new GUI has been translated to your language."
    new "Vain uusi GUI on käännetty kielellesi."

    # new_project.rpy:52
    old "Only the legacy theme interface has been translated to your language."
    new "Vain vanha teemakäyttöliittymä on käännetty kielellesi."

    # new_project.rpy:54
    old "Neither interface has been translated to your language."
    new "Kumpaakaan käyttöliittymää ei ole käännetty kielellesi."

    # new_project.rpy:63
    old "The projects directory could not be set. Giving up."
    new "Projektikansion valinta epäonnistui."

    # new_project.rpy:69
    old "Which interface would you like to use? The new GUI has a modern look, supports wide screens and mobile devices, and is easier to customize. Legacy themes might be necessary to work with older example code.\n\n[language_support!t]\n\nIf in doubt, choose the new GUI, then click Continue on the bottom-right."
    new "Kumpaa käyttöliittymää haluaisit käyttää? Uusi GUI on modernin näköinen, tukee leveitä näyttöjä sekä mobiililaitteita, ja sen kustomointi on helpompaa. Vanhat teemat voivat olla tarpeellisia vanhan esimerkkikoodin kanssa.\n\n[language_support!t]\n\nJos epäröit, valitse uusi käyttöliittymä, ja klikkaa Jatka oikeassa alakulmassa."

    # new_project.rpy:69
    old "Legacy Theme Interface"
    new "Vanha Teemakäyttöliittymä"

    # new_project.rpy:90
    old "Choose Project Template"
    new "Valitse projektipohja"

    # new_project.rpy:108
    old "Please select a template to use for your new project. The template sets the default font and the user interface language. If your language is not supported, choose 'english'."
    new "Valitse pohja käytettäväksi uudessa projektissasi. Se asettaa perusfontin ja kielen. Jos kieltäsi ei löydy, valitse englanti (English)."

    # preferences.rpy:64
    old "Launcher Preferences"
    new "Työkalun asetukset"

    # preferences.rpy:85
    old "Projects Directory:"
    new "Projektien kansio"

    # preferences.rpy:92
    old "[persistent.projects_directory!q]"
    new "[persistent.projects_directory!q]"

    # preferences.rpy:94
    old "Projects directory: [text]"
    new "Projektikansio: [text]"

    # preferences.rpy:96
    old "Not Set"
    new "Ei Valittu"

    # preferences.rpy:111
    old "Text Editor:"
    new "Tekstieditori:"

    # preferences.rpy:117
    old "Text editor: [text]"
    new "Tekstieditori: [text]"

    # preferences.rpy:133
    old "Update Channel:"
    new "Päivityskanava:"

    # preferences.rpy:153
    old "Navigation Options:"
    new "Hakuasetukset"

    # preferences.rpy:157
    old "Include private names"
    new "Sisällytä yksityiset nimet"

    # preferences.rpy:158
    old "Include library names"
    new "Sisällytä kirjastot"

    # preferences.rpy:168
    old "Launcher Options:"
    new "Työkalun asetukset"

    # preferences.rpy:172
    old "Hardware rendering"
    new "Laitteistorenderöinti"

    # preferences.rpy:173
    old "Show templates"
    new "Näytä esimerkkipohjat"

    # preferences.rpy:174
    old "Show edit file section"
    new "Näytä tiedostojen muokkausvalikko"

    # preferences.rpy:175
    old "Large fonts"
    new "Suuret fontit"

    # preferences.rpy:178
    old "Console output"
    new "Konsolisyöte"

    # preferences.rpy:199
    old "Open launcher project"
    new "Avaa työkaluprojekti"

    # preferences.rpy:213
    old "Language:"
    new "Kieli:"

    # project.rpy:47
    old "After making changes to the script, press shift+R to reload your game."
    new "Kun olet tehnyt muutoksia pelin tiedostoihin, paina shift+R käynnistääksesi pelisi automaattisesti uudelleen."

    # project.rpy:47
    old "Press shift+O (the letter) to access the console."
    new "Paina shift+O (kirjain) päästäksesi käsiksi konsoliin."

    # project.rpy:47
    old "Press shift+D to access the developer menu."
    new "Paina shift+D avataksesi kehittäjävalikon."

    # project.rpy:47
    old "Have you backed up your projects recently?"
    new "Oletko ottanut projekteistasi varmuuskopiot viime aikoina?"

    # project.rpy:229
    old "Launching the project failed."
    new "Projektin käynnistys epäonnistui."

    # project.rpy:229
    old "Please ensure that your project launches normally before running this command."
    new "Varmista, että projektisi käynnistyy normaalisti ennen kuin käytät tätä komentoa."

    # project.rpy:242
    old "Ren'Py is scanning the project..."
    new "Ren'Py skannaa projektia..."

    # project.rpy:568
    old "Launching"
    new "Käynnistetään..."

    # project.rpy:597
    old "PROJECTS DIRECTORY"
    new "PROJEKTIKANSIO"

    # project.rpy:597
    old "Please choose the projects directory using the directory chooser.\n{b}The directory chooser may have opened behind this window.{/b}"
    new "Ole hyvä ja valitse kansio, jota haluat käyttää projektiesi tallentamiseen.\n{b}Kansion valitsija on saattanut aueta tämän ikkunan taakse.{/b}"

    # project.rpy:597
    old "This launcher will scan for projects in this directory, will create new projects in this directory, and will place built projects into this directory."
    new "Tämä työkalu etsii, luo sekä muokkaa projekteja, jotka löytyvät tästä kansiosta."

    # project.rpy:602
    old "Ren'Py has set the projects directory to:"
    new "Ren'Py on valinnut projektikansioksi:"

    # translations.rpy:63
    old "Translations: [project.current.name!q]"
    new "Käännökset: [project.current.name!q]"

    # translations.rpy:104
    old "The language to work with. This should only contain lower-case ASCII characters and underscores."
    new "Työkieli. Tämän tulisi sisältää vain pieniä ASCII-merkkejä ja alaviivoja."

    # translations.rpy:130
    old "Generate empty strings for translations"
    new "Luo tyhjiä merkkijonoja käännöksiä varten"

    # translations.rpy:148
    old "Generates or updates translation files. The files will be placed in game/tl/[persistent.translate_language!q]."
    new "Luo tai päivittää käännöstiedostot. Tiedostot sijoitetaan kansioon game/tl/[persistent.translate_language!q]."

    # translations.rpy:168
    old "Extract String Translations"
    new "Poimi Merkkijonojen Käännökset"

    # translations.rpy:170
    old "Merge String Translations"
    new "Yhdistä Merkkijonojen Käännökset"

    # translations.rpy:175
    old "Replace existing translations"
    new "Korvaa olemassaolevat käännökset"

    # translations.rpy:176
    old "Reverse languages"
    new "Vaihda kielet päikseen"

    # translations.rpy:180
    old "Update Default Interface Translations"
    new "Päivitä Oletusarvoiset Käyttöliittymän Käännökset"

    # translations.rpy:200
    old "The extract command allows you to extract string translations from an existing project into a temporary file.\n\nThe merge command merges extracted translations into another project."
    new "Poimi-komento mahdollistaa merkkijonokäännösten poimimisen olemassaolevasta projektista väliaikaiseen tiedostoon.\n\nYhdistä-komento yhdistää poimitut käännökset toiseen projektiin."

    # translations.rpy:224
    old "Ren'Py is generating translations...."
    new "Ren'Py luo käännöksiä...."

    # translations.rpy:235
    old "Ren'Py has finished generating [language] translations."
    new "Ren'Py on onnistuneesti luonut [language] käännöstiedostot."

    # translations.rpy:248
    old "Ren'Py is extracting string translations..."
    new "Ren'Py poimii merkkijonojen käännöksiä..."

    # translations.rpy:251
    old "Ren'Py has finished extracting [language] string translations."
    new "Ren'Py on onnistuneesti poiminut [language] merkkijonojen käännökset."

    # translations.rpy:271
    old "Ren'Py is merging string translations..."
    new "Ren'Py yhdisttää merkkijonojen käännöksiä..."

    # translations.rpy:274
    old "Ren'Py has finished merging [language] string translations."
    new "Ren'Py on onnistuneesti yhdistänyt [language] merkkijonojen käännökset."

    # translations.rpy:282
    old "Updating default interface translations..."
    new "Päivitetään oletusarvoisen käyttöliittymän käännöksiä..."

    # translations.rpy:306
    old "Extract Dialogue: [project.current.name!q]"
    new "Kopioi dialogi: [project.current.name!q]"

    # translations.rpy:322
    old "Format:"
    new "Formaatti:"

    # translations.rpy:330
    old "Tab-delimited Spreadsheet (dialogue.tab)"
    new "Taulukkotiedosto (dialogi.tab)"

    # translations.rpy:331
    old "Dialogue Text Only (dialogue.txt)"
    new "Dialogi pelkkänä tekstinä (dialogi.txt)"

    # translations.rpy:344
    old "Strip text tags from the dialogue."
    new "Kopioi tekstitägit dialogista."

    # translations.rpy:345
    old "Escape quotes and other special characters."
    new "Kaikki erikoismerkit."

    # translations.rpy:346
    old "Extract all translatable strings, not just dialogue."
    new "Kopioi kaikki käännettävät sanat, ei pelkkää dialogia."

    # translations.rpy:374
    old "Ren'Py is extracting dialogue...."
    new "Ren'Py kopioi dialogia...."

    # translations.rpy:378
    old "Ren'Py has finished extracting dialogue. The extracted dialogue can be found in dialogue.[persistent.dialogue_format] in the base directory."
    new "Ren'Py on onnistuneesti kopioinut dialogin. Kopio löytyy tiedostosta dialogue.[persistent.dialogue_format] projektin kansiosta."

    # updater.rpy:75
    old "Select Update Channel"
    new "Valitse päivityskanava"

    # updater.rpy:86
    old "The update channel controls the version of Ren'Py the updater will download. Please select an update channel:"
    new "Päivityskanava vaikuttaa siihen, mihin versioon Ren'Py päivitetään. Valitse päivityskanava:"

    # updater.rpy:91
    old "Release"
    new "Vakaat versiot"

    # updater.rpy:97
    old "{b}Recommended.{/b} The version of Ren'Py that should be used in all newly-released games."
    new "{b}Suositeltu.{/b} Ren'Py:n uusin, vakaa versio, jota suositellaan käytettäväksi."

    # updater.rpy:102
    old "Prerelease"
    new "Esiversiot"

    # updater.rpy:108
    old "A preview of the next version of Ren'Py that can be used for testing and taking advantage of new features, but not for final releases of games."
    new "Ennakkoversio Ren'Py:n seuraavasta versiosta, jota voidaan käyttää uusien ominaisuuksien testaamiseen ja hyödyntämiseen. Ei suositella pelien julkaisemiseen."

    # updater.rpy:114
    old "Experimental"
    new "Kokeelliset versiot"

    # updater.rpy:120
    old "Experimental versions of Ren'Py. You shouldn't select this channel unless asked by a Ren'Py developer."
    new "Ren'Py:n kokeellisia versioita. Sinun ei kannata valita tätä, ellei joku Ren'Py:n kehittäjistä erikseen sitä pyydä."

    # updater.rpy:126
    old "Nightly"
    new "Jokaöiset julkaisut"

    # updater.rpy:132
    old "The bleeding edge of Ren'Py development. This may have the latest features, or might not run at all."
    new "Upouudet Ren'Py-kehitysjulkaisut. Voi sisältää uusimmat toiminnot, tai ei ehkä toimi lainkaan."

    # updater.rpy:152
    old "An error has occurred:"
    new "On tapahtunut virhe:"

    # updater.rpy:154
    old "Checking for updates."
    new "Haetaan päivityksiä."

    # updater.rpy:156
    old "Ren'Py is up to date."
    new "Käytät uusinta Ren'Py-versiota."

    # updater.rpy:158
    old "[u.version] is now available. Do you want to install it?"
    new "[u.version] on nyt saatavilla. Haluatko asentaa sen?"

    # updater.rpy:160
    old "Preparing to download the update."
    new "Valmistellaan päivityksen lataamista."

    # updater.rpy:162
    old "Downloading the update."
    new "Ladataan päivitystä."

    # updater.rpy:164
    old "Unpacking the update."
    new "Puretaan päivitystä."

    # updater.rpy:166
    old "Finishing up."
    new "Viimeistellään asennusta."

    # updater.rpy:168
    old "The update has been installed. Ren'Py will restart."
    new "Päivitys on nyt asennettu. Ren'Py käynnistyy nyt uudelleen."

    # updater.rpy:170
    old "The update has been installed."
    new "Päivitys on asennettu."

    # updater.rpy:172
    old "The update was cancelled."
    new "Päivitys peruttiin."

    # updater.rpy:189
    old "Ren'Py Update"
    new "Ren'Py-päivitys"

    # updater.rpy:195
    old "Proceed"
    new "Jatka"

translate finnish strings:

    # game/add_file.rpy:37
    old "The file name may not be empty."
    # Automatic translation.
    new "Tiedoston nimi ei saa olla tyhjä."

    # game/android.rpy:37
    old "A 64-bit/x64 Java [JDK_REQUIREMENT] Development Kit is required to build Android packages on Windows. The JDK is different from the JRE, so it's possible you have Java without having the JDK.\n\nPlease {a=https://www.renpy.org/jdk/[JDK_REQUIREMENT]}download and install the JDK{/a}, then restart the Ren'Py launcher."
    # Automatic translation.
    new "Android-pakettien rakentamiseen Windowsissa tarvitaan 64-bittinen/x64 Java [JDK_REQUIREMENT] Development Kit. JDK on eri asia kuin JRE, joten on mahdollista, että sinulla on Java ilman JDK:ta.\n\nLataa ja asenna JDK {a=https://www.renpy.org/jdk/[JDK_REQUIREMENT]}{/a} ja käynnistä sitten Ren'Py-launcher uudelleen."

    # game/android.rpy:39
    old "RAPT has been installed, but a key hasn't been configured. Please generate new keys, or copy android.keystore and bundle.keystore to the base directory."
    # Automatic translation.
    new "RAPT on asennettu, mutta avainta ei ole määritetty. Luo uudet avaimet tai kopioi android.keystore ja bundle.keystore perushakemistoon."

    # game/android.rpy:41
    old "Please select if you want a Play Bundle (for Google Play), or a Universal APK (for sideloading and other app stores)."
    # Automatic translation.
    new "Valitse, haluatko Play Bundlen (Google Playta varten) vai Universal APK:n (sivulatausta ja muita sovelluskauppoja varten)."

    # game/android.rpy:46
    old "Attempts to emulate a televison-based Android console.\n\nController input is mapped to the arrow keys, Enter is mapped to the select button, Escape is mapped to the menu button, and PageUp is mapped to the back button."
    # Automatic translation.
    new "Yritetään emuloida televisioon perustuvaa Android-konsolia.\n\nOhjaimen syöttö on yhdistetty nuolinäppäimiin, Enter on yhdistetty valintapainikkeeseen, Escape on yhdistetty valikkopainikkeeseen ja PageUp on yhdistetty takaisin-painikkeeseen."

    # game/android.rpy:48
    old "Downloads and installs the Android SDK and supporting packages."
    # Automatic translation.
    new "Lataa ja asentaa Android SDK:n ja tukipaketit."

    # game/android.rpy:49
    old "Generates the keys required to sign the package."
    # Automatic translation.
    new "Luo paketin allekirjoittamiseen tarvittavat avaimet."

    # game/android.rpy:56
    old "Lists the connected devices."
    # Automatic translation.
    new "Luettelee liitetyt laitteet."

    # game/android.rpy:57
    old "Pairs with a device over Wi-Fi, on Android 11+."
    # Automatic translation.
    new "Pariliitos laitteen kanssa Wi-Fi-yhteyden kautta Android 11+ -käyttöjärjestelmässä."

    # game/android.rpy:58
    old "Connects to a device over Wi-Fi, on Android 11+."
    # Automatic translation.
    new "Muodostaa yhteyden laitteeseen Wi-Fi-yhteyden kautta Android 11+ -käyttöjärjestelmässä."

    # game/android.rpy:59
    old "Disconnects a device connected over Wi-Fi."
    new "Katkaisee yhteyden Wi-Fi-yhteyden kautta yhdistetyn laitteeseen."

    # game/android.rpy:61
    old "Removes Android temporary files."
    # Automatic translation.
    new "Poistaa Androidin väliaikaiset tiedostot."

    # game/android.rpy:63
    old "Builds an Android App Bundle (ABB), intended to be uploaded to Google Play. This can include up to 2GB of data."
    # Automatic translation.
    new "Rakentaa Android-sovelluspaketin (ABB), joka on tarkoitus ladata Google Play -palveluun. Tämä voi sisältää enintään 2 Gt dataa."

    # game/android.rpy:64
    old "Builds a Universal APK package, intended for sideloading and stores other than Google Play. This can include up to 2GB of data."
    new "Rakentaa universaalin APK-paketin, joka on tarkoitettu sivulataukseen ja muihin kauppoihin kuin Google Play'hin. Tämä voi sisältää enintään 2 Gt dataa."

    # game/android.rpy:327
    old "Android: [project.current.display_name!q]"
    new "Android: [project.current.display_name!q]"

    # game/android.rpy:383
    old "Install SDK"
    # Automatic translation.
    new "Asenna SDK"

    # game/android.rpy:387
    old "Generate Keys"
    # Automatic translation.
    new "Luo avaimet"

    # game/android.rpy:397
    old "Play Bundle"
    new "Play-kaupan paketti"

    # game/android.rpy:402
    old "Universal APK"
    new "Universal APK"

    # game/android.rpy:452
    old "List Devices"
    new "Listaa laitteet"

    # game/android.rpy:456
    old "Wi-Fi Debugging Pair"
    # Automatic translation.
    new "Wi-Fi-vianmäärityspari"

    # game/android.rpy:460
    old "Wi-Fi Debugging Connect"
    new "Wi-Fi-vianmäärityksen yhdistäminen"

    # game/android.rpy:464
    old "Wi-Fi Debugging Disconnect"
    # Automatic translation.
    new "Wi-Fi-vianmäärityksen katkaisu"

    # game/android.rpy:468
    old "Clean"
    new "Puhdista"

    # game/android.rpy:573
    old "Wi-Fi Pairing Code"
    # Automatic translation.
    new "Wi-Fi-pariliitäntäkoodi"

    # game/android.rpy:573
    old "If supported, this can be found in 'Developer options', 'Wireless debugging', 'Pair device with pairing code'."
    # Automatic translation.
    new "Jos tämä on tuettu, se löytyy kohdasta \"Kehittäjän asetukset\", \"Langaton virheenkorjaus\", \"Laitteen pariliitos pariliitoskoodilla\"."

    # game/android.rpy:580
    old "Pairing Host & Port"
    # Automatic translation.
    new "Isännän ja sataman yhdistäminen"

    # game/android.rpy:596
    old "IP Address & Port"
    # Automatic translation.
    new "IP-osoite ja portti"

    # game/android.rpy:596
    old "If supported, this can be found in 'Developer options', 'Wireless debugging'."
    # Automatic translation.
    new "Jos tämä on tuettu, se löytyy kohdasta 'Kehitysasetukset', 'Langaton virheenkorjaus'."

    # game/android.rpy:612
    old "This can be found in 'List Devices'."
    new "Tämä löytyy kohdasta 'listaa laitteet'."

    # game/android.rpy:632
    old "Cleaning up Android project."
    # Automatic translation.
    new "Android-projektin siivoaminen."

    # game/androidstrings.rpy:7
    old "{} is not a directory."
    # Automatic translation.
    new "{} ei ole hakemisto."

    # game/androidstrings.rpy:8
    old "{} does not contain a Ren'Py game."
    # Automatic translation.
    new "{} ei sisällä Ren'Py-peliä."

    # game/androidstrings.rpy:10
    old "Run configure before attempting to build the app."
    # Automatic translation.
    new "Suorita configure ennen sovelluksen rakentamista."

    # game/androidstrings.rpy:11
    old "Updating project."
    # Automatic translation.
    new "Projektin päivittäminen."

    # game/androidstrings.rpy:12
    old "Creating assets directory."
    new "Materiaalihakemiston luominen."

    # game/androidstrings.rpy:13
    old "Packaging internal data."
    # Automatic translation.
    new "Pakkauksen sisäiset tiedot."

    # game/androidstrings.rpy:14
    old "I'm using Gradle to build the package."
    # Automatic translation.
    new "Käytän Gradlea paketin rakentamiseen."

    # game/androidstrings.rpy:15
    old "The build seems to have failed."
    # Automatic translation.
    new "Rakentaminen näyttää epäonnistuneen."

    # game/androidstrings.rpy:16
    old "I'm installing the bundle."
    # Automatic translation.
    new "Asennan paketin."

    # game/androidstrings.rpy:17
    old "Installing the bundle appears to have failed."
    new "Paketin asentaminen näyttää epäonnistuneen."

    # game/androidstrings.rpy:18
    old "Launching app."
    new "Sovellus käynnistyy."

    # game/androidstrings.rpy:19
    old "Launching the app appears to have failed."
    # Automatic translation.
    new "Sovelluksen käynnistäminen näyttää epäonnistuneen."

    # game/androidstrings.rpy:20
    old "The build seems to have succeeded."
    # Automatic translation.
    new "Rakentaminen näyttää onnistuneen."

    # game/androidstrings.rpy:21
    old "What is the full name of your application? This name will appear in the list of installed applications."
    new "Mikä on sovelluksesi koko nimi? Tämä nimi näkyy asennettujen sovellusten luettelossa."

    # game/androidstrings.rpy:22
    old "What is the short name of your application? This name will be used in the launcher, and for application shortcuts."
    # Automatic translation.
    new "Mikä on sovelluksesi lyhyt nimi? Tätä nimeä käytetään käynnistysohjelmassa ja sovelluksen pikakuvakkeissa."

    # game/androidstrings.rpy:23
    old "What is the name of the package?\n\nThis is usually of the form com.domain.program or com.domain.email.program. It may only contain ASCII letters and dots. It must contain at least one dot."
    # Automatic translation.
    new "Mikä on paketin nimi?\n\nTämä on yleensä muotoa com.domain.program tai com.domain.email.program. Se voi sisältää vain ASCII-kirjaimia ja pisteitä. Sen on sisällettävä vähintään yksi piste."

    # game/androidstrings.rpy:24
    old "The package name may not be empty."
    # Automatic translation.
    new "Paketin nimi ei saa olla tyhjä."

    # game/androidstrings.rpy:25
    old "The package name may not contain spaces."
    # Automatic translation.
    new "Paketin nimi ei saa sisältää välilyöntejä."

    # game/androidstrings.rpy:26
    old "The package name must contain at least one dot."
    # Automatic translation.
    new "Paketin nimessä on oltava vähintään yksi piste."

    # game/androidstrings.rpy:27
    old "The package name may not contain two dots in a row, or begin or end with a dot."
    # Automatic translation.
    new "Paketin nimi ei saa sisältää kahta pistettä peräkkäin eikä alkaa tai päättyä pisteeseen."

    # game/androidstrings.rpy:28
    old "Each part of the package name must start with a letter, and contain only letters, numbers, and underscores."
    # Automatic translation.
    new "Paketin nimen jokaisen osan on alettava kirjaimella, ja sen on sisällettävä vain kirjaimia, numeroita ja alleviivauksia."

    # game/androidstrings.rpy:29
    old "{} is a Java keyword, and can't be used as part of a package name."
    # Automatic translation.
    new "{} on Javan avainsana, eikä sitä voi käyttää osana paketin nimeä."

    # game/androidstrings.rpy:30
    old "What is the application's version?\n\nThis should be the human-readable version that you would present to a person. It must contain only numbers and dots."
    new "Mikä on sovelluksen versio?\n\nTämän pitäisi olla ihmisen luettava versio, jonka voisi näyttää henkilölle. Sen on sisällettävä vain numeroita ja pisteitä."

    # game/androidstrings.rpy:31
    old "The version number must contain only numbers and dots."
    # Automatic translation.
    new "Versionumero saa sisältää vain numeroita ja pisteitä."

    # game/androidstrings.rpy:32
    old "How much RAM (in GB) do you want to allocate to Gradle?\nThis must be a positive integer number."
    # Automatic translation.
    new "Kuinka paljon RAM-muistia (gigatavuina) haluat varata Gradlelle?\nTämän on oltava positiivinen kokonaisluku."

    # game/androidstrings.rpy:33
    old "The RAM size must contain only numbers and be positive."
    # Automatic translation.
    new "RAM-koon on sisällettävä vain numeroita ja oltava positiivinen."

    # game/androidstrings.rpy:34
    old "How would you like your application to be displayed?"
    # Automatic translation.
    new "Miten haluat, että sovelluksesi näytetään?"

    # game/androidstrings.rpy:35
    old "In landscape orientation."
    # Automatic translation.
    new "Maisemasuunnassa."

    # game/androidstrings.rpy:36
    old "In portrait orientation."
    # Automatic translation.
    new "Pystysuunnassa."

    # game/androidstrings.rpy:37
    old "In the user's preferred orientation."
    # Automatic translation.
    new "Käyttäjän haluamassa suunnassa."

    # game/androidstrings.rpy:38
    old "Which app store would you like to support in-app purchasing through?"
    # Automatic translation.
    new "Minkä sovelluskaupan kautta haluaisit tukea sovelluksen sisäisiä ostoja?"

    # game/androidstrings.rpy:39
    old "Google Play."
    new "Google Playn."

    # game/androidstrings.rpy:40
    old "Amazon App Store."
    new "Amazon App Storen."

    # game/androidstrings.rpy:41
    old "Both, in one app."
    new "Molempien yhdessä sovelluksessa."

    # game/androidstrings.rpy:42
    old "Neither."
    new "En kummankaan."

    # game/androidstrings.rpy:43
    old "Do you want to automatically update the Java source code?"
    # Automatic translation.
    new "Haluatko päivittää Javan lähdekoodin automaattisesti?"

    # game/androidstrings.rpy:44
    old "Yes. This is the best choice for most projects."
    # Automatic translation.
    new "Kyllä. Tämä on paras valinta useimpiin hankkeisiin."

    # game/androidstrings.rpy:45
    old "No. This may require manual updates when Ren'Py or the project configuration changes."
    # Automatic translation.
    new "Ei. Tämä saattaa vaatia manuaalisia päivityksiä, kun Ren'Py tai projektin kokoonpano muuttuu."

    # game/androidstrings.rpy:46
    old "Unknown configuration variable: {}"
    # Automatic translation.
    new "Tuntematon konfiguraatiomuuttuja: {}"

    # game/androidstrings.rpy:47
    old "I'm compiling a short test program, to see if you have a working JDK on your system."
    new "Rakennan lyhyen testiohjelman nähdäkseni, onko järjestelmässäsi toimiva JDK."

    # game/androidstrings.rpy:48
    old "I was unable to use javac to compile a test file. If you haven't installed the Java Development Kit yet, please download it from:\n\n{a=https://adoptium.net/?variant=openjdk8}https://adoptium.net/?variant=openjdk8{/a}\n\nThe JDK is different from the JRE, so it's possible you have Java without having the JDK. Please make sure you installed the 'JavaSoft (Oracle) registry keys'.\n\nWithout a working JDK, I can't continue."
    # Automatic translation.
    new "En pystynyt kääntämään testitiedostoa javacin avulla. Jos et ole vielä asentanut Java Development Kitiä, lataa se osoitteesta:\n\n{a=https://adoptium.net/?variant=openjdk8}https://adoptium.net/?variant=openjdk8{/a}\n\nJDK on eri asia kuin JRE, joten on mahdollista, että sinulla on Java ilman JDK:ta. Varmista, että olet asentanut 'JavaSoft (Oracle) registry keys'.\n\nIlman toimivaa JDK:ta en voi jatkaa."

    # game/androidstrings.rpy:49
    old "The version of Java on your computer does not appear to be JDK 8, which is the only version supported by the Android SDK. If you need to install JDK 8, you can download it from:\n\n{a=https://adoptium.net/?variant=openjdk8}https://adoptium.net/?variant=openjdk8{/a}\n\nYou can also set the JAVA_HOME environment variable to use a different version of Java."
    # Automatic translation.
    new "Tietokoneessasi oleva Java-versio ei näytä olevan JDK 8, joka on ainoa Android SDK:n tukema versio. Jos sinun on asennettava JDK 8, voit ladata sen osoitteesta:\n\n{a=https://adoptium.net/?variant=openjdk8}https://adoptium.net/?variant=openjdk8{/a}\n\nVoit myös asettaa JAVA_HOME-ympäristömuuttujan käyttämään eri Java-versiota."

    # game/androidstrings.rpy:50
    old "The JDK is present and working. Good!"
    # Automatic translation.
    new "JDK on läsnä ja toimii. Hyvä!"

    # game/androidstrings.rpy:51
    old "The Android SDK has already been unpacked."
    # Automatic translation.
    new "Android SDK on jo purettu."

    # game/androidstrings.rpy:52
    old "Do you accept the Android SDK Terms and Conditions?"
    # Automatic translation.
    new "Hyväksytkö Android SDK:n käyttöehdot?"

    # game/androidstrings.rpy:53
    old "I'm downloading the Android SDK. This might take a while."
    # Automatic translation.
    new "Lataan Android SDK:n. Tämä saattaa kestää jonkin aikaa."

    # game/androidstrings.rpy:54
    old "I'm extracting the Android SDK."
    # Automatic translation.
    new "Puran Android SDK:n."

    # game/androidstrings.rpy:55
    old "I've finished unpacking the Android SDK."
    # Automatic translation.
    new "Olen purkanut Android SDK:n pakkauksen."

    # game/androidstrings.rpy:56
    old "I'm about to download and install the required Android packages. This might take a while."
    # Automatic translation.
    new "Aion ladata ja asentaa tarvittavat Android-paketit. Tämä saattaa kestää jonkin aikaa."

    # game/androidstrings.rpy:57
    old "I was unable to accept the Android licenses."
    # Automatic translation.
    new "En pystynyt hyväksymään Android-lisenssejä."

    # game/androidstrings.rpy:59
    old "I was unable to install the required Android packages."
    # Automatic translation.
    new "En saanut asennettua tarvittavia Android-paketteja."

    # game/androidstrings.rpy:60
    old "I've finished installing the required Android packages."
    # Automatic translation.
    new "Olen saanut asennettua tarvittavat Android-paketit."

    # game/androidstrings.rpy:61
    old "It looks like you're ready to start packaging games."
    # Automatic translation.
    new "Näyttää siltä, että olet valmis aloittamaan pelien pakkaamisen."

    # game/androidstrings.rpy:62
    old "Please enter your name or the name of your organization."
    # Automatic translation.
    new "Kirjoita nimesi tai organisaatiosi nimi."

    # game/androidstrings.rpy:63
    old "I found an android.keystore file in the rapt directory. Do you want to use this file?"
    # Automatic translation.
    new "Löysin android.keystore-tiedoston rapt-hakemistosta. Haluatko käyttää tätä tiedostoa?"

    # game/androidstrings.rpy:64
    old "I can create an application signing key for you. This key is required to create Universal APK for sideloading and stores other than Google Play.\n\nDo you want to create a key?"
    # Automatic translation.
    new "Voin luoda sinulle sovelluksen allekirjoitusavaimen. Tätä avainta tarvitaan luodaksesi Universal APK:n sivulatausta ja muita kauppoja kuin Google Playta varten.\n\nHaluatko luoda avaimen?"

    # game/androidstrings.rpy:65
    old "I will create the key in the android.keystore file.\n\nYou need to back this file up. If you lose it, you will not be able to upgrade your application.\n\nYou also need to keep the key safe. If evil people get this file, they could make fake versions of your application, and potentially steal your users' data.\n\nWill you make a backup of android.keystore, and keep it in a safe place?"
    # Automatic translation.
    new "Luon avaimen android.keystore-tiedostoon.\n\nSinun on varmuuskopioitava tämä tiedosto. Jos kadotat sen, et voi päivittää sovellusta.\n\nSinun on myös pidettävä avain turvassa. Jos pahat ihmiset saavat tämän tiedoston haltuunsa, he voivat tehdä sovelluksestasi väärennettyjä versioita ja mahdollisesti varastaa käyttäjien tietoja.\n\nTeetkö varmuuskopion android.keystore-tiedostosta ja säilytät sen turvallisessa paikassa?"

    # game/androidstrings.rpy:66
    old "\n\nSaying 'No' will prevent key creation."
    # Automatic translation.
    new "\n\nJos sanot \"Ei\", avaimen luominen estyy."

    # game/androidstrings.rpy:67
    old "Could not create android.keystore. Is keytool in your path?"
    # Automatic translation.
    new "Ei voitu luoda android.keystore. Onko keytool polussasi?"

    # game/androidstrings.rpy:68
    old "I've finished creating android.keystore. Please back it up, and keep it in a safe place."
    # Automatic translation.
    new "Olen saanut valmiiksi android.keystoren luomisen. Varmuuskopioi se ja säilytä se turvallisessa paikassa."

    # game/androidstrings.rpy:69
    old "I found a bundle.keystore file in the rapt directory. Do you want to use this file?"
    # Automatic translation.
    new "Löysin bundle.keystore-tiedoston rapt-hakemistosta. Haluatko käyttää tätä tiedostoa?"

    # game/androidstrings.rpy:70
    old "I can create a bundle signing key for you. This key is required to build an Android App Bundle (AAB) for upload to Google Play.\n\nDo you want to create a key?"
    new "Voin luoda sinulle paketin allekirjoitusavaimen. Tätä avainta tarvitaan Android-sovelluspaketin (AAB) rakentamiseen Google Playyn lataamista varten.\n\nHaluatko luoda avaimen?"

    # game/androidstrings.rpy:71
    old "I will create the key in the bundle.keystore file.\n\nYou need to back this file up. If you lose it, you will not be able to upgrade your application.\n\nYou also need to keep the key safe. If evil people get this file, they could make fake versions of your application, and potentially steal your users' data.\n\nWill you make a backup of bundle.keystore, and keep it in a safe place?"
    # Automatic translation.
    new "Luon avaimen bundle.keystore-tiedostoon.\n\nSinun on varmuuskopioitava tämä tiedosto. Jos kadotat sen, et voi päivittää sovellusta.\n\nSinun on myös pidettävä avain turvassa. Jos pahat ihmiset saavat tämän tiedoston haltuunsa, he voivat tehdä sovelluksestasi väärennettyjä versioita ja mahdollisesti varastaa käyttäjien tietoja.\n\nTeetkö varmuuskopion bundle.keystore-tiedostosta ja säilytät sen turvallisessa paikassa?"

    # game/androidstrings.rpy:73
    old "Could not create bundle.keystore. Is keytool in your path?"
    # Automatic translation.
    new "Ei voitu luoda bundle.keystore. Onko keytool polussasi?"

    # game/androidstrings.rpy:74
    old "I've opened the directory containing android.keystore and bundle.keystore. Please back them up, and keep them in a safe place."
    # Automatic translation.
    new "Olen avannut hakemiston, joka sisältää android.keystore ja bundle.keystore. Varmuuskopioi ne ja säilytä ne turvallisessa paikassa."

    # game/choose_directory.rpy:67
    old "Select Projects Directory"
    # Automatic translation.
    new "Valitse projektit Hakemisto"

    # game/choose_directory.rpy:79
    old "The selected projects directory is not writable."
    # Automatic translation.
    new "Valittuun projektihakemistoon ei voi kirjoittaa."

    # game/choose_theme.rpy:508
    old "changing the theme"
    # Automatic translation.
    new "teeman muuttaminen"

    # game/distribute.rpy:1278
    old "Signing the Macintosh application...\n(This may take a long time.)"
    new "Macintosh-sovellusta allekirjoitetaan...\n(Tämä voi kestää kauan.)"

    # game/distribute.rpy:1745
    old "Copying files..."
    new "Tiedostoja kopioidaan..."

    # game/distribute_gui.rpy:157
    old "Build Distributions: [project.current.display_name!q]"
    # Automatic translation.
    new "Rakenna jakelut: [project.current.display_name!q]"

    # game/distribute_gui.rpy:195
    old "Update old-game"
    new "Päivitä old-game"

    # game/distribute_gui.rpy:231
    old "(DLC)"
    new "(DLC)"

    # game/dmgcheck.rpy:50
    old "Ren'Py is running from a read only folder. Some functionality will not work."
    # Automatic translation.
    new "Ren'Py toimii vain lukuoikeuksilla varustetusta kansiosta. Jotkin toiminnot eivät toimi."

    # game/dmgcheck.rpy:50
    old "This is probably because Ren'Py is running directly from a Macintosh drive image. To fix this, quit this launcher, copy the entire %s folder somewhere else on your computer, and run Ren'Py again."
    # Automatic translation.
    new "Tämä johtuu luultavasti siitä, että Ren'Py toimii suoraan Macintosh-aseman kuvasta. Voit korjata tämän lopettamalla tämän käynnistysohjelman, kopioimalla koko %s-kansion jonnekin muualle tietokoneellesi ja käynnistämällä Ren'Py:n uudelleen."

    # game/editor.rpy:152
    old "A modern editor with many extensions including advanced Ren'Py integration."
    # Automatic translation.
    new "Nykyaikainen editori, jossa on monia laajennuksia, mukaan lukien edistynyt Ren'Py-integraatio."

    # game/editor.rpy:153
    old "A modern editor with many extensions including advanced Ren'Py integration.\n{a=jump:reinstall_vscode}Upgrade Visual Studio Code to the latest version.{/a}"
    # Automatic translation.
    new "Nykyaikainen editori, jossa on monia laajennuksia, mukaan lukien edistynyt Ren'Py-integraatio.\n{a=jump:reinstall_vscode}Päivitä Visual Studio Code uusimpaan versioon.{/a}"

    # game/editor.rpy:169
    old "Visual Studio Code"
    new "Visual Studio Code"

    # game/editor.rpy:169
    old "Up to 110 MB download required."
    # Automatic translation.
    new "Jopa 110 Mt:n lataus vaaditaan."

    # game/editor.rpy:182
    old "A modern and approachable text editor."
    # Automatic translation.
    new "Moderni ja helposti lähestyttävä tekstieditori."

    # game/editor.rpy:196
    old "Atom"
    new "Atom"

    # game/editor.rpy:196
    old "Up to 150 MB download required."
    new "Jopa 150 Mt:n lataus vaaditaan."

    # game/editor.rpy:211
    old "jEdit"
    new "jEdit"

    # game/editor.rpy:220
    old "Visual Studio Code (System)"
    # Automatic translation.
    new "Visual Studio Code (järjestelmä)"

    # game/editor.rpy:220
    old "Uses a copy of Visual Studio Code that you have installed outside of Ren'Py. It's recommended you install the language-renpy extension to add support for Ren'Py files."
    new "Käyttää kopiota Visual Studio Code -ohjelmasta, jonka olet asentanut Ren'Py:n ulkopuolelle. On suositeltavaa asentaa language-renpy -laajennus lisätäksesi tuen Ren'Py-tiedostoille."

    # game/editor.rpy:226
    old "System Editor"
    new "Järjestelmän editori"

    # game/editor.rpy:245
    old "None"
    # Automatic translation.
    new "Ei ole"

    # game/editor.rpy:352
    old "Edit [text]."
    # Automatic translation.
    new "Muokkaa [text]."

    # game/front_page.rpy:58
    old "PROJECTS:"
    new "PROJEKTIT:"

    # game/front_page.rpy:165
    old "audio"
    new "audio"

    # game/front_page.rpy:182
    old "Open project"
    new "Avaa projekti"

    # game/front_page.rpy:188
    old "Actions"
    # Automatic translation.
    new "Toimet"

    # game/front_page.rpy:219
    old "Web"
    new "Web"

    # game/front_page.rpy:219
    old "(Beta)"
    new "(Beta)"

    # game/gui7.rpy:302
    old "{size=-4}\n\nThis will not overwrite gui/main_menu.png, gui/game_menu.png, and gui/window_icon.png, but will create files that do not exist.{/size}"
    # Automatic translation.
    new "{size=-4}\n\nTämä ei korvaa tiedostoja gui/main_menu.png, gui/game_menu.png ja gui/window_icon.png, vaan luo tiedostoja, joita ei ole olemassa.{/size}"

    # game/gui7.rpy:333
    old "Custom. The GUI is optimized for a 16:9 aspect ratio."
    # Automatic translation.
    new "Mukautettu. Käyttöliittymä on optimoitu 16:9-kuvasuhteelle."

    # game/gui7.rpy:350
    old "WIDTH"
    # Automatic translation.
    new "LEVEYS"

    # game/gui7.rpy:350
    old "Please enter the width of your game, in pixels."
    # Automatic translation.
    new "Kirjoita pelisi leveys pikseleinä."

    # game/gui7.rpy:360
    old "The width must be a number."
    # Automatic translation.
    new "Leveyden on oltava numero."

    # game/gui7.rpy:366
    old "HEIGHT"
    # Automatic translation.
    new "KORKEUS"

    # game/gui7.rpy:366
    old "Please enter the height of your game, in pixels."
    # Automatic translation.
    new "Anna pelisi korkeus pikseleinä."

    # game/gui7.rpy:376
    old "The height must be a number."
    # Automatic translation.
    new "Korkeuden on oltava numero."

    # game/gui7.rpy:424
    old "creating a new project"
    new "uutta projektia luodaan"

    # game/gui7.rpy:428
    old "activating the new project"
    new "uutta projektia aktivoidaan"

    # game/install.rpy:33
    old "Could not install [name!t], as a file matching [zipglob] was not found in the Ren'Py SDK directory."
    # Automatic translation.
    new "[name!t] ei voitu asentaa, koska Ren'Py SDK -hakemistosta ei löytynyt [zipglob] -nimistä tiedostoa."

    # game/install.rpy:79
    old "Successfully installed [name!t]."
    new "[name!t] asennettu onnistuneesti."

    # game/install.rpy:111
    old "This screen allows you to install libraries that can't be distributed with Ren'Py. Some of these libraries may require you to agree to a third-party license before being used or distributed."
    # Automatic translation.
    new "Tällä näytöllä voit asentaa kirjastoja joita ei voi jakaa Ren'Py:n mukana. Jotkin näistä kirjastoista saattavat vaatia, että hyväksyt kolmannen osapuolen lisenssin ennen käyttöä tai jakelua."

    # game/install.rpy:117
    old "Install Steam Support"
    # Automatic translation.
    new "Asenna Steam-tuki"

    # game/install.rpy:126
    old "Before installing Steam support, please make sure you are a {a=https://partner.steamgames.com/}Steam partner{/a}."
    # Automatic translation.
    new "Ennen kuin asennat Steam-tuen, varmista, että olet {a=https://partner.steamgames.com/}Steam-kumppani{/a}."

    # game/install.rpy:138
    old "Steam support has already been installed."
    # Automatic translation.
    new "Steam-tuki on jo asennettu."

    # game/install.rpy:142
    old "Install Live2D Cubism SDK for Native"
    new "Asenna Live2D Cubism SDK for Native"

    # game/install.rpy:156
    old "Install Libraries"
    # Automatic translation.
    new "Asenna kirjastot"

    # game/install.rpy:182
    old "The {a=https://www.live2d.com/en/download/cubism-sdk/download-native/}Cubism SDK for Native{/a} adds support for displaying Live2D models. Place CubismSdkForNative-4-{i}version{/i}.zip in the Ren'Py SDK directory, and then click Install. Distributing a game with Live2D requires you to accept a license from Live2D, Inc."
    # Automatic translation.
    new "{a=https://www.live2d.com/en/download/cubism-sdk/download-native/}Cubism SDK for Native{/a} lisää tuen Live2D-mallien näyttämiselle. Aseta CubismSdkForNative-4-{i}versio{/i}.zip-tiedosto Ren'Py SDK -hakemistoon ja napsauta sitten Install. Pelin levittäminen Live2D:n avulla edellyttää, että hyväksyt Live2D, Inc:n lisenssin."

    # game/install.rpy:186
    old "Live2D in Ren'Py doesn't support the Web, Android x86_64 (including emulators and Chrome OS), and must be added to iOS projects manually. Live2D must be reinstalled after upgrading Ren'Py or installing Android support."
    # Automatic translation.
    new "Ren'Py:n Live2D ei tue Webiä, Android x86_64:ää (mukaan lukien emulaattorit ja Chrome OS), ja se on lisättävä iOS-projekteihin manuaalisesti. Live2D on asennettava uudelleen Ren'Py:n päivittämisen tai Android-tuen asentamisen jälkeen."

    # game/install.rpy:191
    old "Open Ren'Py SDK Directory"
    # Automatic translation.
    new "Avaa Ren'Py SDK-hakemisto"

    # game/installer.rpy:10
    old "Downloading [extension.download_file]."
    new "Ladataan [extension.download_file]."

    # game/installer.rpy:11
    old "Could not download [extension.download_file] from [extension.download_url]:\n{b}[extension.download_error]"
    # Automatic translation.
    new "Ei voitu ladata [extension.download_file] osoitteesta [extension.download_url]:\n{b}[extension.download_error]"

    # game/installer.rpy:12
    old "The downloaded file [extension.download_file] from [extension.download_url] is not correct."
    # Automatic translation.
    new "Ladattu tiedosto [extension.download_file] osoitteesta [extension.download_url] ei ole oikein."

    # game/interface.rpy:124
    old "[interface.version]"
    new "[interface.version]"

    # game/interface.rpy:141
    old "Ren'Py Sponsor Information"
    new "Ren'Pyn sponsoritiedot"

    # game/interface.rpy:385
    old "opening the log file"
    # Automatic translation.
    new "lokitiedostoa avataan"

    # game/ios.rpy:269
    old "iOS: [project.current.display_name!q]"
    new "iOS: [project.current.display_name!q]"

    # game/ios.rpy:379
    old "There are known issues with the iOS simulator on Apple Silicon. Please test on x86_64 or iOS devices."
    # Automatic translation.
    new "Apple Siliconin iOS-simulaattorissa on tunnettuja ongelmia. Testaa x86_64- tai iOS-laitteilla."

    # game/itch.rpy:45
    old "Downloading the itch.io butler."
    new "Ladataan itch.io'n butler-ohjelmaa."

    # game/navigation.rpy:168
    old "Navigate: [project.current.display_name!q]"
    # Automatic translation.
    new "Navigoi: [project.current.display_name!q]"

    # game/new_project.rpy:81
    old "You will be creating an [new_project_language]{#this substitution may be localized} language project. Change the launcher language in preferences to create a project in another language."
    new "Luot [new_project_language]{#this substitution may be localized} -kielisen projektin. Vaihda käynnistyskieli asetuksissa, jos haluat luoda projektin toisella kielellä."

    # game/preferences.rpy:106
    old "General"
    # Automatic translation.
    new "Yleistä"

    # game/preferences.rpy:107
    old "Options"
    new "Asetukset"

    # game/preferences.rpy:224
    old "Sponsor message"
    new "Sponsoriviesti"

    # game/preferences.rpy:227
    old "Daily check for update"
    # Automatic translation.
    new "Päivittäinen päivityksen tarkistus"

    # game/preferences.rpy:246
    old "Launcher Theme:"
    new "Käynnistimen Teema:"

    # game/preferences.rpy:250
    old "Default theme"
    # Automatic translation.
    new "Oletusteema"

    # game/preferences.rpy:251
    old "Dark theme"
    # Automatic translation.
    new "Tumma teema"

    # game/preferences.rpy:252
    old "Custom theme"
    # Automatic translation.
    new "Mukautettu teema"

    # game/preferences.rpy:256
    old "Information about creating a custom theme can be found {a=[skins_url]}in the Ren'Py Documentation{/a}."
    new "Tietoa mukautetun teeman luomisesta löytyy {a=[skins_url]}Ren'Pyn dokumentaatiosta{/a}."

    # game/preferences.rpy:273
    old "Install Libraries:"
    # Automatic translation.
    new "Asenna kirjastot:"

    # game/preferences.rpy:300
    old "Reset window size"
    new "Palauta ikkunan oletuskoko"

    # game/preferences.rpy:301
    old "Clean temporary files"
    new "Poista väliaikaiset tiedostot"

    # game/preferences.rpy:308
    old "Cleaning temporary files..."
    new "Poistetaan väliaikaisia tiedostoja..."

    # game/preferences.rpy:338
    old "{#in language font}Welcome! Please choose a language"
    # Automatic translation.
    new "{#in language font}Tervetuloa! Valitse kieli"

    # game/preferences.rpy:373
    old "{#in language font}Start using Ren'Py in [lang_name]"
    new "{#in language font}Aloita Ren'Py:n käyttö kielellä [lang_name]"

    # game/project.rpy:46
    old "Lint checks your game for potential mistakes, and gives you statistics."
    # Automatic translation.
    new "Lint tarkistaa pelisi mahdollisten virheiden varalta ja antaa tilastoja."

    # game/project.rpy:283
    old "This may be because the project is not writeable."
    # Automatic translation.
    new "Tämä voi johtua siitä, että projekti ei ole kirjoitettavissa."

    # game/translations.rpy:91
    old "Translations: [project.current.display_name!q]"
    # Automatic translation.
    new "Käännökset: [project.current.display_name!q]"

    # game/translations.rpy:342
    old "Extract Dialogue: [project.current.display_name!q]"
    new "Kerää dialogi: [project.current.display_name!q]"

    # game/translations.rpy:391
    old "Language (or None for the default language):"
    # Automatic translation.
    new "Kieli (tai Ei mitään oletuskieleksi):"

    # game/updater.rpy:64
    old "Release (Ren'Py 8, Python 3)"
    # Automatic translation.
    new "Julkaisu (Ren'Py 8, Python 3)"

    # game/updater.rpy:65
    old "Release (Ren'Py 7, Python 2)"
    # Automatic translation.
    new "Julkaisu (Ren'Py 7, Python 2)"

    # game/updater.rpy:69
    old "Prerelease (Ren'Py 8, Python 3)"
    new "Esijulkaisu (Ren'Py 8, Python 3)"

    # game/updater.rpy:70
    old "Prerelease (Ren'Py 7, Python 2)"
    new "Esijulkaisu (Ren'Py 7, Python 2)"

    # game/updater.rpy:77
    old "Nightly (Ren'Py 8, Python 3)"
    new "Nightly (Ren'Py 8, Python 3)"

    # game/updater.rpy:78
    old "Nightly (Ren'Py 7, Python 2)"
    new "Nightly (Ren'Py 7, Python 2)"

    # game/updater.rpy:108
    old "The update channel controls the version of Ren'Py the updater will download."
    # Automatic translation.
    new "Päivityskanava ohjaa Ren'Py-version, jonka päivitysohjelma lataa."

    # game/updater.rpy:116
    old "• {a=https://www.renpy.org/doc/html/changelog.html}View change log{/a}"
    # Automatic translation.
    new "- {a=https://www.renpy.org/doc/html/changelog.html}Näytä muutosloki{/a}"

    # game/updater.rpy:118
    old "• {a=https://www.renpy.org/dev-doc/html/changelog.html}View change log{/a}"
    # Automatic translation.
    new "- {a=https://www.renpy.org/dev-doc/html/changelog.html}Näytä muutosloki{/a}"

    # game/updater.rpy:124
    old "• This version is installed and up-to-date."
    # Automatic translation.
    new "- Tämä versio on asennettu ja ajan tasalla."

    # game/updater.rpy:136
    old "%B %d, %Y"
    new "%B %d, %Y"

    # game/updater.rpy:215
    old "Fetching the list of update channels"
    new "Haetaan päivityskanavien luetteloa"

    # game/updater.rpy:220
    old "downloading the list of update channels"
    new "ladataan päivityskanavien luetteloa"

    # game/web.rpy:428
    old "Preparing progressive download"
    new "Valmistellaan progressiivista latausta"

    # game/web.rpy:485
    old "Creating package..."
    new "Luodaan pakettia..."

    # game/web.rpy:505
    old "Web: [project.current.display_name!q]"
    new "Web: [project.current.display_name!q]"

    # game/web.rpy:535
    old "Build Web Application"
    # Automatic translation.
    new "Rakenna web-sovellus"

    # game/web.rpy:536
    old "Build and Open in Browser"
    # Automatic translation.
    new "Rakenna ja avaa selaimessa"

    # game/web.rpy:537
    old "Open in Browser"
    # Automatic translation.
    new "Avaa selaimessa"

    # game/web.rpy:538
    old "Open build directory"
    # Automatic translation.
    new "Avaa rakennushakemisto"

    # game/web.rpy:560
    old "Images and music can be downloaded while playing. A 'progressive_download.txt' file will be created so you can configure this behavior."
    # Automatic translation.
    new "Kuvia ja musiikkia voi ladata toiston aikana. Progressive_download.txt-tiedosto luodaan, jotta voit määrittää tämän käyttäytymisen."

    # game/web.rpy:568
    old "Before packaging web apps, you'll need to download RenPyWeb, Ren'Py's web support. Would you like to download RenPyWeb now?"
    # Automatic translation.
    new "Ennen verkkosovellusten pakkaamista sinun on ladattava RenPyWeb, Ren'Pyn verkkotuki. Haluatko ladata RenPyWebin nyt?"


translate finnish strings:

    # game/updater.rpy:79
    old "A nightly build of fixes to the release version of Ren'Py."
    # Automatic translation.
    new "Yöllinen versio, joka sisältää korjauksia Ren'Pyn julkaisuversioon."
