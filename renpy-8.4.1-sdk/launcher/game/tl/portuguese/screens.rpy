
translate portuguese strings:

    # gui/game/screens.rpy:9
    old "## Styles"
    # Automatic translation.
    new "## Estilos"

    # gui/game/screens.rpy:81
    old "## In-game screens"
    # Automatic translation.
    new "## Telas no jogo"

    # gui/game/screens.rpy:85
    old "## Say screen"
    # Automatic translation.
    new "## Diga a tela"

    # gui/game/screens.rpy:87
    old "## The say screen is used to display dialogue to the player. It takes two parameters, who and what, which are the name of the speaking character and the text to be displayed, respectively. (The who parameter can be None if no name is given.)"
    # Automatic translation.
    new "## A tela say é usada para exibir o diálogo para o jogador. Ela recebe dois parâmetros, who e what, que são o nome do personagem que fala e o texto a ser exibido, respectivamente. (O parâmetro who pode ser None (Nenhum) se nenhum nome for fornecido)."

    # gui/game/screens.rpy:92
    old "## This screen must create a text displayable with id \"what\", as <PERSON><PERSON><PERSON><PERSON> uses this to manage text display. It can also create displayables with id \"who\" and id \"window\" to apply style properties."
    # Automatic translation.
    new "## Essa tela deve criar um texto exibível com o id \"what\", pois o Ren'Py o utiliza para gerenciar a exibição de texto. Ela também pode criar exibíveis com id \"who\" e id \"window\" para aplicar propriedades de estilo."

    # gui/game/screens.rpy:96
    old "## https://www.renpy.org/doc/html/screen_special.html#say"
    new "## https://www.renpy.org/doc/html/screen_special.html#say"

    # gui/game/screens.rpy:114
    old "## If there's a side image, display it above the text. Do not display on the phone variant - there's no room."
    # Automatic translation.
    new "## Se houver uma imagem lateral, exiba-a acima do texto. Não exiba na variante do telefone - não há espaço."

    # gui/game/screens.rpy:120
    old "## Make the namebox available for styling through the Character object."
    # Automatic translation.
    new "## Disponibilize a caixa de nome para estilização por meio do objeto Character."

    # gui/game/screens.rpy:165
    old "## Input screen"
    # Automatic translation.
    new "## Tela de entrada"

    # gui/game/screens.rpy:167
    old "## This screen is used to display renpy.input. The prompt parameter is used to pass a text prompt in."
    # Automatic translation.
    new "## Essa tela é usada para exibir renpy.input. O parâmetro prompt é usado para passar um prompt de texto."

    # gui/game/screens.rpy:170
    old "## This screen must create an input displayable with id \"input\" to accept the various input parameters."
    # Automatic translation.
    new "## Essa tela deve criar um displayable de entrada com id \"input\" para aceitar os vários parâmetros de entrada."

    # gui/game/screens.rpy:173
    old "## https://www.renpy.org/doc/html/screen_special.html#input"
    new "## https://www.renpy.org/doc/html/screen_special.html#input"

    # gui/game/screens.rpy:200
    old "## Choice screen"
    # Automatic translation.
    new "## Tela de escolha"

    # gui/game/screens.rpy:202
    old "## This screen is used to display the in-game choices presented by the menu statement. The one parameter, items, is a list of objects, each with caption and action fields."
    # Automatic translation.
    new "## Essa tela é usada para exibir as opções no jogo apresentadas pela instrução de menu. O único parâmetro, itens, é uma lista de objetos, cada um com campos de legenda e ação."

    # gui/game/screens.rpy:206
    old "## https://www.renpy.org/doc/html/screen_special.html#choice"
    new "## https://www.renpy.org/doc/html/screen_special.html#choice"

    # gui/game/screens.rpy:234
    old "## Quick Menu screen"
    # Automatic translation.
    new "## Tela do menu rápido"

    # gui/game/screens.rpy:236
    old "## The quick menu is displayed in-game to provide easy access to the out-of-game menus."
    # Automatic translation.
    new "## O menu rápido é exibido no jogo para fornecer acesso fácil aos menus fora do jogo."

    # gui/game/screens.rpy:241
    old "## Ensure this appears on top of other screens."
    # Automatic translation.
    new "## Certifique-se de que isso apareça na parte superior de outras telas."

    # gui/game/screens.rpy:253
    old "History"
    # Automatic translation.
    new "Histórico"

    # gui/game/screens.rpy:254
    old "Skip"
    # Automatic translation.
    new "Pular"

    # gui/game/screens.rpy:255
    old "Auto"
    # Automatic translation.
    new "Automotivo"

    # gui/game/screens.rpy:256
    old "Save"
    # Automatic translation.
    new "Salvar"

    # gui/game/screens.rpy:257
    old "Q.Save"
    # Automatic translation.
    new "Q.Salvar"

    # gui/game/screens.rpy:258
    old "Q.Load"
    # Automatic translation.
    new "Q. Carga"

    # gui/game/screens.rpy:259
    old "Prefs"
    new "Prefs"

    # gui/game/screens.rpy:262
    old "## This code ensures that the quick_menu screen is displayed in-game, whenever the player has not explicitly hidden the interface."
    # Automatic translation.
    new "## Esse código garante que a tela quick_menu seja exibida no jogo, sempre que o jogador não tiver ocultado explicitamente a interface."

    # gui/game/screens.rpy:280
    old "## Main and Game Menu Screens"
    # Automatic translation.
    new "## Telas do menu principal e do menu do jogo"

    # gui/game/screens.rpy:283
    old "## Navigation screen"
    # Automatic translation.
    new "## Tela de navegação"

    # gui/game/screens.rpy:285
    old "## This screen is included in the main and game menus, and provides navigation to other menus, and to start the game."
    # Automatic translation.
    new "## Essa tela está incluída nos menus principal e do jogo e fornece navegação para outros menus e para iniciar o jogo."

    # gui/game/screens.rpy:300
    old "Start"
    # Automatic translation.
    new "Início"

    # gui/game/screens.rpy:308
    old "Load"
    # Automatic translation.
    new "Carga"

    # gui/game/screens.rpy:310
    old "Preferences"
    # Automatic translation.
    new "Preferências"

    # gui/game/screens.rpy:314
    old "End Replay"
    # Automatic translation.
    new "Fim da reprodução"

    # gui/game/screens.rpy:318
    old "Main Menu"
    # Automatic translation.
    new "Menu principal"

    # gui/game/screens.rpy:324
    old "## Help isn't necessary or relevant to mobile devices."
    # Automatic translation.
    new "## A ajuda não é necessária ou relevante para dispositivos móveis."

    # gui/game/screens.rpy:325
    old "Help"
    # Automatic translation.
    new "Ajuda"

    # gui/game/screens.rpy:329
    old "## The quit button is banned on iOS and unnecessary on Android and Web."
    # Automatic translation.
    new "## O botão Sair é proibido no iOS e desnecessário no Android e na Web."

    # gui/game/screens.rpy:344
    old "## Main Menu screen"
    # Automatic translation.
    new "## Tela do menu principal"

    # gui/game/screens.rpy:346
    old "## Used to display the main menu when Ren'Py starts."
    # Automatic translation.
    new "## Usado para exibir o menu principal quando o Ren'Py é iniciado."

    # gui/game/screens.rpy:348
    old "## https://www.renpy.org/doc/html/screen_special.html#main-menu"
    new "## https://www.renpy.org/doc/html/screen_special.html#main-menu"

    # gui/game/screens.rpy:352
    old "## This ensures that any other menu screen is replaced."
    # Automatic translation.
    new "## Isso garante que qualquer outra tela de menu seja substituída."

    # gui/game/screens.rpy:357
    old "## This empty frame darkens the main menu."
    # Automatic translation.
    new "## Esse quadro vazio escurece o menu principal."

    # gui/game/screens.rpy:361
    old "## The use statement includes another screen inside this one. The actual contents of the main menu are in the navigation screen."
    # Automatic translation.
    new "## A instrução de uso inclui outra tela dentro desta. O conteúdo real do menu principal está na tela de navegação."

    # gui/game/screens.rpy:406
    old "## Game Menu screen"
    # Automatic translation.
    new "## Tela do menu do jogo"

    # gui/game/screens.rpy:408
    old "## This lays out the basic common structure of a game menu screen. It's called with the screen title, and displays the background, title, and navigation."
    # Automatic translation.
    new "## Isso estabelece a estrutura básica comum de uma tela de menu de jogo. Ela é chamada com o título da tela e exibe o plano de fundo, o título e a navegação."

    # gui/game/screens.rpy:429
    old "## Reserve space for the navigation section."
    # Automatic translation.
    new "## Reserve espaço para a seção de navegação."

    # gui/game/screens.rpy:534
    old "## About screen"
    # Automatic translation.
    new "## Sobre a tela"

    # gui/game/screens.rpy:536
    old "## This screen gives credit and copyright information about the game and Ren'Py."
    # Automatic translation.
    new "## Essa tela fornece informações de crédito e direitos autorais sobre o jogo e Ren'Py."

    # gui/game/screens.rpy:539
    old "## There's nothing special about this screen, and hence it also serves as an example of how to make a custom screen."
    # Automatic translation.
    new "## Não há nada de especial nessa tela e, portanto, ela também serve como exemplo de como criar uma tela personalizada."

    # gui/game/screens.rpy:546
    old "## This use statement includes the game_menu screen inside this one. The vbox child is then included inside the viewport inside the game_menu screen."
    # Automatic translation.
    new "## Essa instrução de uso inclui a tela game_menu dentro desta. O filho vbox é então incluído na janela de visualização dentro da tela game_menu."

    # gui/game/screens.rpy:556
    old "Version [config.version!t]\n"
    # Automatic translation.
    new "Versão [config.version!t]\n"

    # gui/game/screens.rpy:558
    old "## gui.about is usually set in options.rpy."
    # Automatic translation.
    new "## gui.about é normalmente definido em options.rpy."

    # gui/game/screens.rpy:562
    old "Made with {a=https://www.renpy.org/}Ren'Py{/a} [renpy.version_only].\n\n[renpy.license!t]"
    # Automatic translation.
    new "Feito com {a=https://www.renpy.org/}Ren'Py{/a} [renpy.version_only] .\n\n[renpy.license!t]"

    # gui/game/screens.rpy:573
    old "## Load and Save screens"
    # Automatic translation.
    new "## Carregar e salvar telas"

    # gui/game/screens.rpy:575
    old "## These screens are responsible for letting the player save the game and load it again. Since they share nearly everything in common, both are implemented in terms of a third screen, file_slots."
    # Automatic translation.
    new "## Essas telas são responsáveis por permitir que o jogador salve o jogo e o carregue novamente. Como elas têm quase tudo em comum, ambas são implementadas em termos de uma terceira tela, file_slots."

    # gui/game/screens.rpy:579
    old "## https://www.renpy.org/doc/html/screen_special.html#save https://www.renpy.org/doc/html/screen_special.html#load"
    new "## https://www.renpy.org/doc/html/screen_special.html#save https://www.renpy.org/doc/html/screen_special.html#load"

    # gui/game/screens.rpy:598
    old "Page {}"
    # Automatic translation.
    new "Página {}"

    # gui/game/screens.rpy:598
    old "Automatic saves"
    # Automatic translation.
    new "Salvamentos automáticos"

    # gui/game/screens.rpy:598
    old "Quick saves"
    # Automatic translation.
    new "Salvamentos rápidos"

    # gui/game/screens.rpy:604
    old "## This ensures the input will get the enter event before any of the buttons do."
    # Automatic translation.
    new "## Isso garante que a entrada receberá o evento enter antes de qualquer um dos botões."

    # gui/game/screens.rpy:608
    old "## The page name, which can be edited by clicking on a button."
    # Automatic translation.
    new "## O nome da página, que pode ser editado clicando em um botão."

    # gui/game/screens.rpy:620
    old "## The grid of file slots."
    # Automatic translation.
    new "## A grade de slots de arquivo."

    # gui/game/screens.rpy:640
    old "{#file_time}%A, %B %d %Y, %H:%M"
    new "{#file_time}%A, %B %d %Y, %H:%M"

    # gui/game/screens.rpy:640
    old "empty slot"
    # Automatic translation.
    new "slot vazio"

    # gui/game/screens.rpy:648
    old "## Buttons to access other pages."
    # Automatic translation.
    new "## Botões para acessar outras páginas."

    # gui/game/screens.rpy:660
    old "<"
    new "<"

    # gui/game/screens.rpy:663
    old "{#auto_page}A"
    new "{#auto_page}A"

    # gui/game/screens.rpy:666
    old "{#quick_page}Q"
    new "{#quick_page}Q"

    # gui/game/screens.rpy:668
    old "## range(1, 10) gives the numbers from 1 to 9."
    # Automatic translation.
    new "## range(1, 10) fornece os números de 1 a 9."

    # gui/game/screens.rpy:672
    old ">"
    new ">"

    # gui/game/screens.rpy:676
    old "Upload Sync"
    new "Upload Sync"

    # gui/game/screens.rpy:680
    old "Download Sync"
    # Automatic translation.
    new "Baixar o Sync"

    # gui/game/screens.rpy:717
    old "## Preferences screen"
    # Automatic translation.
    new "## Tela de preferências"

    # gui/game/screens.rpy:719
    old "## The preferences screen allows the player to configure the game to better suit themselves."
    # Automatic translation.
    new "## A tela de preferências permite que o jogador configure o jogo para se adequar melhor."

    # gui/game/screens.rpy:722
    old "## https://www.renpy.org/doc/html/screen_special.html#preferences"
    new "## https://www.renpy.org/doc/html/screen_special.html#preferences"

    # gui/game/screens.rpy:746
    old "Unseen Text"
    # Automatic translation.
    new "Texto invisível"

    # gui/game/screens.rpy:747
    old "After Choices"
    # Automatic translation.
    new "Após as escolhas"

    # gui/game/screens.rpy:748
    old "Transitions"
    # Automatic translation.
    new "Transições"

    # gui/game/screens.rpy:750
    old "## Additional vboxes of type \"radio_pref\" or \"check_pref\" can be added here, to add additional creator-defined preferences."
    # Automatic translation.
    new "## Vboxes adicionais do tipo \"radio_pref\" ou \"check_pref\" podem ser adicionadas aqui para acrescentar outras preferências definidas pelo criador."

    # gui/game/screens.rpy:761
    old "Text Speed"
    # Automatic translation.
    new "Velocidade do texto"

    # gui/game/screens.rpy:765
    old "Auto-Forward Time"
    # Automatic translation.
    new "Tempo de encaminhamento automático"

    # gui/game/screens.rpy:772
    old "Music Volume"
    # Automatic translation.
    new "Volume da música"

    # gui/game/screens.rpy:785
    old "Test"
    # Automatic translation.
    new "Teste"

    # gui/game/screens.rpy:789
    old "Voice Volume"
    # Automatic translation.
    new "Volume da voz"

    # gui/game/screens.rpy:800
    old "Mute All"
    # Automatic translation.
    new "Silenciar tudo"

    # gui/game/screens.rpy:876
    old "## History screen"
    # Automatic translation.
    new "## Tela de histórico"

    # gui/game/screens.rpy:878
    old "## This is a screen that displays the dialogue history to the player. While there isn't anything special about this screen, it does have to access the dialogue history stored in _history_list."
    # Automatic translation.
    new "## Essa é uma tela que exibe o histórico de diálogo para o jogador. Embora não haja nada de especial nessa tela, ela precisa acessar o histórico de diálogo armazenado em _history_list."

    # gui/game/screens.rpy:882
    old "## https://www.renpy.org/doc/html/history.html"
    new "## https://www.renpy.org/doc/html/history.html"

    # gui/game/screens.rpy:888
    old "## Avoid predicting this screen, as it can be very large."
    # Automatic translation.
    new "## Evite prever essa tela, pois ela pode ser muito grande."

    # gui/game/screens.rpy:899
    old "## This lays things out properly if history_height is None."
    # Automatic translation.
    new "## Isso organiza as coisas corretamente se history_height for None."

    # gui/game/screens.rpy:909
    old "## Take the color of the who text from the Character, if set."
    # Automatic translation.
    new "## Pegue a cor do texto who do caractere, se definido."

    # gui/game/screens.rpy:918
    old "The dialogue history is empty."
    # Automatic translation.
    new "O histórico de diálogo está vazio."

    # gui/game/screens.rpy:921
    old "## This determines what tags are allowed to be displayed on the history screen."
    # Automatic translation.
    new "## Isso determina quais tags podem ser exibidas na tela de histórico."

    # gui/game/screens.rpy:966
    old "## Help screen"
    # Automatic translation.
    new "## Tela de ajuda"

    # gui/game/screens.rpy:968
    old "## A screen that gives information about key and mouse bindings. It uses other screens (keyboard_help, mouse_help, and gamepad_help) to display the actual help."
    # Automatic translation.
    new "## Uma tela que fornece informações sobre as combinações de teclas e mouse. Ela usa outras telas (keyboard_help, mouse_help e gamepad_help) para exibir a ajuda real."

    # gui/game/screens.rpy:987
    old "Keyboard"
    # Automatic translation.
    new "Teclado"

    # gui/game/screens.rpy:988
    old "Mouse"
    new "Mouse"

    # gui/game/screens.rpy:991
    old "Gamepad"
    # Automatic translation.
    new "Controle de jogo"

    # gui/game/screens.rpy:1004
    old "Enter"
    # Automatic translation.
    new "Entrar"

    # gui/game/screens.rpy:1005
    old "Advances dialogue and activates the interface."
    # Automatic translation.
    new "Avança o diálogo e ativa a interface."

    # gui/game/screens.rpy:1008
    old "Space"
    # Automatic translation.
    new "Espaço"

    # gui/game/screens.rpy:1009
    old "Advances dialogue without selecting choices."
    # Automatic translation.
    new "Avança o diálogo sem selecionar opções."

    # gui/game/screens.rpy:1012
    old "Arrow Keys"
    # Automatic translation.
    new "Teclas de seta"

    # gui/game/screens.rpy:1013
    old "Navigate the interface."
    # Automatic translation.
    new "Navegue pela interface."

    # gui/game/screens.rpy:1016
    old "Escape"
    # Automatic translation.
    new "Fuga"

    # gui/game/screens.rpy:1017
    old "Accesses the game menu."
    # Automatic translation.
    new "Acessa o menu do jogo."

    # gui/game/screens.rpy:1020
    old "Ctrl"
    new "Ctrl"

    # gui/game/screens.rpy:1021
    old "Skips dialogue while held down."
    # Automatic translation.
    new "Pula o diálogo quando pressionado."

    # gui/game/screens.rpy:1024
    old "Tab"
    new "Tab"

    # gui/game/screens.rpy:1025
    old "Toggles dialogue skipping."
    # Automatic translation.
    new "Alterna o salto de diálogo."

    # gui/game/screens.rpy:1028
    old "Page Up"
    # Automatic translation.
    new "Página para cima"

    # gui/game/screens.rpy:1029
    old "Rolls back to earlier dialogue."
    # Automatic translation.
    new "Volta ao diálogo anterior."

    # gui/game/screens.rpy:1032
    old "Page Down"
    # Automatic translation.
    new "Página para baixo"

    # gui/game/screens.rpy:1033
    old "Rolls forward to later dialogue."
    # Automatic translation.
    new "Rola para frente o diálogo posterior."

    # gui/game/screens.rpy:1037
    old "Hides the user interface."
    # Automatic translation.
    new "Oculta a interface do usuário."

    # gui/game/screens.rpy:1041
    old "Takes a screenshot."
    # Automatic translation.
    new "Faz uma captura de tela."

    # gui/game/screens.rpy:1045
    old "Toggles assistive {a=https://www.renpy.org/l/voicing}self-voicing{/a}."
    # Automatic translation.
    new "Alterna a assistência {a=https://www.renpy.org/l/voicing}auto-voz{/a}."

    # gui/game/screens.rpy:1049
    old "Opens the accessibility menu."
    # Automatic translation.
    new "Abre o menu de acessibilidade."

    # gui/game/screens.rpy:1055
    old "Left Click"
    # Automatic translation.
    new "Clique com o botão esquerdo do mouse"

    # gui/game/screens.rpy:1059
    old "Middle Click"
    # Automatic translation.
    new "Clique no meio"

    # gui/game/screens.rpy:1063
    old "Right Click"
    # Automatic translation.
    new "Clique com o botão direito do mouse"

    # gui/game/screens.rpy:1067
    old "Mouse Wheel Up"
    # Automatic translation.
    new "Roda do mouse para cima\nClique em Rollback Side"

    # gui/game/screens.rpy:1071
    old "Mouse Wheel Down"
    # Automatic translation.
    new "Roda do mouse para baixo"

    # gui/game/screens.rpy:1078
    old "Right Trigger\nA/Bottom Button"
    # Automatic translation.
    new "Gatilho direito\nBotão A/inferior"

    # gui/game/screens.rpy:1082
    old "Left Trigger\nLeft Shoulder"
    # Automatic translation.
    new "Gatilho esquerdo\nOmbro esquerdo"

    # gui/game/screens.rpy:1086
    old "Right Shoulder"
    # Automatic translation.
    new "Ombro direito"

    # gui/game/screens.rpy:1091
    old "D-Pad, Sticks"
    # Automatic translation.
    new "D-Pad, bastões"

    # gui/game/screens.rpy:1095
    old "Start, Guide"
    # Automatic translation.
    new "Início, Guia"

    # gui/game/screens.rpy:1099
    old "Y/Top Button"
    # Automatic translation.
    new "Botão Y/Top"

    # gui/game/screens.rpy:1102
    old "Calibrate"
    # Automatic translation.
    new "Calibrar"

    # gui/game/screens.rpy:1130
    old "## Additional screens"
    # Automatic translation.
    new "## Telas adicionais"

    # gui/game/screens.rpy:1134
    old "## Confirm screen"
    # Automatic translation.
    new "## Confirmar tela"

    # gui/game/screens.rpy:1136
    old "## The confirm screen is called when Ren'Py wants to ask the player a yes or no question."
    # Automatic translation.
    new "## A tela de confirmação é chamada quando Ren'Py quer fazer uma pergunta de sim ou não ao jogador."

    # gui/game/screens.rpy:1139
    old "## https://www.renpy.org/doc/html/screen_special.html#confirm"
    new "## https://www.renpy.org/doc/html/screen_special.html#confirm"

    # gui/game/screens.rpy:1143
    old "## Ensure other screens do not get input while this screen is displayed."
    # Automatic translation.
    new "## Certifique-se de que outras telas não recebam entrada enquanto essa tela estiver sendo exibida."

    # gui/game/screens.rpy:1170
    old "## Right-click and escape answer \"no\"."
    # Automatic translation.
    new "## Clique com o botão direito do mouse e escape a resposta \"não\"."

    # gui/game/screens.rpy:1197
    old "## Skip indicator screen"
    # Automatic translation.
    new "## Pular a tela do indicador"

    # gui/game/screens.rpy:1199
    old "## The skip_indicator screen is displayed to indicate that skipping is in progress."
    # Automatic translation.
    new "## A tela skip_indicator é exibida para indicar que o salto está em andamento."

    # gui/game/screens.rpy:1202
    old "## https://www.renpy.org/doc/html/screen_special.html#skip-indicator"
    new "## https://www.renpy.org/doc/html/screen_special.html#skip-indicator"

    # gui/game/screens.rpy:1214
    old "Skipping"
    # Automatic translation.
    new "Pular"

    # gui/game/screens.rpy:1221
    old "## This transform is used to blink the arrows one after another."
    # Automatic translation.
    new "## Essa transformação é usada para piscar as setas uma após a outra."

    # gui/game/screens.rpy:1248
    old "## We have to use a font that has the BLACK RIGHT-POINTING SMALL TRIANGLE glyph in it."
    # Automatic translation.
    new "## Temos que usar uma fonte que tenha o glifo BLACK RIGHT-POINTING SMALL TRIANGLE."

    # gui/game/screens.rpy:1253
    old "## Notify screen"
    # Automatic translation.
    new "## Tela de notificação"

    # gui/game/screens.rpy:1255
    old "## The notify screen is used to show the player a message. (For example, when the game is quicksaved or a screenshot has been taken.)"
    # Automatic translation.
    new "## A tela de notificação é usada para mostrar uma mensagem ao jogador. (Por exemplo, quando o jogo é salvo rapidamente ou quando uma captura de tela é feita)."

    # gui/game/screens.rpy:1258
    old "## https://www.renpy.org/doc/html/screen_special.html#notify-screen"
    new "## https://www.renpy.org/doc/html/screen_special.html#notify-screen"

    # gui/game/screens.rpy:1292
    old "## NVL screen"
    # Automatic translation.
    new "## Tela NVL"

    # gui/game/screens.rpy:1294
    old "## This screen is used for NVL-mode dialogue and menus."
    # Automatic translation.
    new "## Essa tela é usada para o diálogo e os menus do modo NVL."

    # gui/game/screens.rpy:1296
    old "## https://www.renpy.org/doc/html/screen_special.html#nvl"
    new "## https://www.renpy.org/doc/html/screen_special.html#nvl"

    # gui/game/screens.rpy:1307
    old "## Displays dialogue in either a vpgrid or the vbox."
    # Automatic translation.
    new "## Exibe o diálogo em uma vpgrid ou na vbox."

    # gui/game/screens.rpy:1320
    old "## Displays the menu, if given. The menu may be displayed incorrectly if config.narrator_menu is set to True."
    # Automatic translation.
    new "## Exibe o menu, se fornecido. O menu poderá ser exibido incorretamente se config.narrator_menu estiver definido como True."

    # gui/game/screens.rpy:1350
    old "## This controls the maximum number of NVL-mode entries that can be displayed at once."
    # Automatic translation.
    new "## Isso controla o número máximo de entradas do modo NVL que podem ser exibidas de uma vez."

    # gui/game/screens.rpy:1410
    old "## Bubble screen"
    # Automatic translation.
    new "## Tela de bolhas"

    # gui/game/screens.rpy:1412
    old "## The bubble screen is used to display dialogue to the player when using speech bubbles. The bubble screen takes the same parameters as the say screen, must create a displayable with the id of \"what\", and can create displayables with the \"namebox\", \"who\", and \"window\" ids."
    # Automatic translation.
    new "## A tela de balão é usada para exibir o diálogo para o jogador ao usar balões de fala. A tela de bolhas recebe os mesmos parâmetros que a tela de dizer, deve criar um exibível com o ID de \"what\" e pode criar exibíveis com os IDs \"namebox\", \"who\" e \"window\"."

    # gui/game/screens.rpy:1417
    old "## https://www.renpy.org/doc/html/bubble.html#bubble-screen"
    new "## https://www.renpy.org/doc/html/bubble.html#bubble-screen"

    # gui/game/screens.rpy:1501
    old "## Mobile Variants"
    # Automatic translation.
    new "## Variantes do celular"

    # gui/game/screens.rpy:1508
    old "## Since a mouse may not be present, we replace the quick menu with a version that uses fewer and bigger buttons that are easier to touch."
    # Automatic translation.
    new "## Como o mouse pode não estar presente, substituímos o menu rápido por uma versão que usa menos botões e maiores, que são mais fáceis de tocar."

    # gui/game/screens.rpy:1526
    old "Menu"
    new "Menu"

translate portuguese strings:

    # gui/game/screens.rpy:411
    old "## The scroll parameter can be None, or one of \"viewport\" or \"vpgrid\". This screen is intended to be used with one or more children, which are transcluded (placed) inside it."
    # Automatic translation.
    new "## O parâmetro de rolagem pode ser Nenhum ou um dos parâmetros \"viewport\" ou \"vpgrid\". Essa tela deve ser usada com um ou mais filhos, que são transcluídos (colocados) dentro dela."

