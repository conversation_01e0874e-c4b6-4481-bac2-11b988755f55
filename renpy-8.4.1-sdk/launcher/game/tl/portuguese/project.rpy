translate portuguese strings:

    # game/project.rpy:201
    old "Launching the project failed."
    new "A execução do projeto falhou."

    # game/project.rpy:201
    old "Please ensure that your project launches normally before running this command."
    new "Por favor, garanta que seu projeto executa normalmente antes de tentar este comando."

    # game/project.rpy:204
    old "Ren'P<PERSON> is scanning the project..."
    new "Ren'Py está escaneando o projeto..."

    # game/project.rpy:503
    old "PROJECTS DIRECTORY"
    new "DIRETÓRIO DOS PROJETOS"

    # game/project.rpy:503
    old "Please choose the projects directory using the directory chooser.\n{b}The directory chooser may have opened behind this window.{/b}"
    new "Por favor escolha o diretório dos projetos usando o selecionador de diretorios \n{b}O selecionador de diretórios talvez possa estar aberto atrás dessa janela."

    # game/project.rpy:503
    old "This launcher will scan for projects in this directory, will create new projects in this directory, and will place built projects into this directory."
    new "O launcher buscará por projetos nesse diretório, criará novos projetos nesse diretório, e colocará os projetos nesse diretório."

    # game/project.rpy:543
    old "Ren'Py was unable to run python with tkinter to choose the projects directory."
    new "Ren'Py foi impedido de executar python com tkinter para escolher o diretório de projetos ."

    # game/project.rpy:547
    old "Ren'Py has set the projects directory to:"
    new "Ren'Py configurou o diretório de projetos para:"

    # game/project.rpy:30
    old "After making changes to the script, press shift+R to reload your game."
    new "Depois de realizar mudanças em um script, pressione shift+R para recarregar seu jogo."

    # game/project.rpy:31
    old "Press shift+O (the letter) to access the console."
    new "Pressione shift+O (a letra) para acessar o console."

    # game/project.rpy:32
    old "Press shift+D to access the developer menu."
    new "Pressione shift+D para acessar o menu de desenvolvedor."

    # game/project.rpy:496
    old "Launching"
    new "Executando"

    # game/project.rpy:584
    old "Ren'Py was unable to run python with tkinter to choose the projects directory. Please install the python-tk or tkinter package."
    new "Ren'Py não pode executar python com tkinter para escolher o diretório de projetos. Por favor, instale o pacote python-tk ou tkinter."

    # game/project.rpy:47
    old "Have you backed up your projects recently?"
    new "Realizou backup dos seus projetos recentemente?"

