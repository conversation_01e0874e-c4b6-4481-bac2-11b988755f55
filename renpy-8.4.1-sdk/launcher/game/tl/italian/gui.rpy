
translate italian strings:

    # gui.rpy:2
    old "## Initialization"
    new "## Inizializzazione"

    # gui.rpy:5
    old "## The init offset statement causes the init code in this file to run before init code in any other file."
    new "## Il comando 'init offset' determina che il codice init in questo file sia eseguito prima del codice init di ogni altro file."

    # gui.rpy:9
    old "## Calling gui.init resets the styles to sensible default values, and sets the width and height of the game."
    new "## Dichiarare gui.init resetta gli stili ai valori predefiniti, e imposta l'altezza e larghezza del gioco."

    # gui.rpy:21
    old "## Colors"
    new "## Colori"

    # gui.rpy:23
    old "## The colors of text in the interface."
    new "## I colori del testo nell'interfaccia."

    # gui.rpy:25
    old "## An accent color used throughout the interface to label and highlight text."
    new "## Un colore di risalto usato nell'interfaccia per le etichette e il testo in evidenza."

    # gui.rpy:29
    old "## The color used for a text button when it is neither selected nor hovered."
    new "## Il colore usato per il testo di un pulsante quando non è nè selezionato nè sotto il puntatore."

    # gui.rpy:32
    old "## The small color is used for small text, which needs to be brighter/darker to achieve the same effect."
    new "## Il colore small è usato per testo piccolo, che richiede di essere più chiaro o più scuro per ottenere lo stesso effetto."

    # gui.rpy:36
    old "## The color that is used for buttons and bars that are hovered."
    new "## Il colore usato per pulsanti e barre che si trovano sotto il puntatore."

    # gui.rpy:39
    old "## The color used for a text button when it is selected but not focused. A button is selected if it is the current screen or preference value."
    new "## Il colore usato per il testo di un pulsante che è selezionato ma non evidenziato. Un pulsante è selezionato se indica l'attuale schermata o valore di preferenza."

    # gui.rpy:43
    old "## The color used for a text button when it cannot be selected."
    new "## Il colore del testo per un pulsante che non può venire selezionato."

    # gui.rpy:46
    old "## Colors used for the portions of bars that are not filled in. These are not used directly, but are used when re-generating bar image files."
    new "## Colori usati per le frazioni di barre che non sono riempite. Non vengono usati direttamente, ma lo sono quando si ri-generano i file immagine della barra."

    # gui.rpy:51
    old "## The colors used for dialogue and menu choice text."
    new "## I colori usati per il dialogo e le scelte."

    # gui.rpy:56
    old "## Fonts and Font Sizes"
    new "## Font e Dimensioni"

    # gui.rpy:58
    old "## The font used for in-game text."
    new "## Il font usato per il testo interno al gioco."

    # gui.rpy:61
    old "## The font used for character names."
    new "## Il font usato per i nomi dei personaggi."

    # gui.rpy:64
    old "## The font used for out-of-game text."
    new "## Il font usato per il testo esterno al gioco."

    # gui.rpy:67
    old "## The size of normal dialogue text."
    new "## La dimensione del normale testo di dialogo."

    # gui.rpy:70
    old "## The size of character names."
    new "## La dimensione dei nomi dei personaggi."

    # gui.rpy:73
    old "## The size of text in the game's user interface."
    new "## Le dimensioni del testo nell'interfaccia di gioco."

    # gui.rpy:76
    old "## The size of labels in the game's user interface."
    new "## Le dimensioni delle etichette nell'interfaccia di gioco."

    # gui.rpy:79
    old "## The size of text on the notify screen."
    new "## La dimensione del testo delle notifiche (notify screen)."

    # gui.rpy:82
    old "## The size of the game's title."
    new "## Le dimensioni del titolo del gioco."

    # gui.rpy:86
    old "## Main and Game Menus"
    new "## Menu - Main e Game"

    # gui.rpy:88
    old "## The images used for the main and game menus."
    new "## Le immagini usate per i menu Main e Game."

    # gui.rpy:92
    old "## Should we show the name and version of the game?"
    new "## Si vuole mostrare il nome e la versione del gioco?"

    # gui.rpy:96
    old "## Dialogue"
    new "## Dialogo"

    # gui.rpy:98
    old "## These variables control how dialogue is displayed on the screen one line at a time."
    new "## Queste variabili controllano come viene mostrato il dialogo a schermo una linea alla volta."

    # gui.rpy:101
    old "## The height of the textbox containing dialogue."
    new "## L'altezza del textbox contenente il dialogo."

    # gui.rpy:104
    old "## The placement of the textbox vertically on the screen. 0.0 is the top, 0.5 is center, and 1.0 is the bottom."
    new "## La posizione verticale del textbox sullo schermo. 0.0 è in alto, 0.5 è al centro, e 1.0 è in basso."

    # gui.rpy:109
    old "## The placement of the speaking character's name, relative to the textbox. These can be a whole number of pixels from the left or top, or 0.5 to center."
    new "## La posizione del nome del personaggio, relativa al textbox. Può essere un numero esatto di pixel da sinistra o da sopra, oppure 0.5 per centrare."

    # gui.rpy:114
    old "## The horizontal alignment of the character's name. This can be 0.0 for left-aligned, 0.5 for centered, and 1.0 for right-aligned."
    new "## L'allineamento orizzontale del nome del personaggio. Può essere 0.0 per allinearlo a sinistra, 0.5 al centro e 1.0 a destra."

    # gui.rpy:118
    old "## The width, height, and borders of the box containing the character's name, or None to automatically size it."
    new "## Larghezza, altezza e bordi del riquadro che contiene il nome del personaggio, oppure None per dimensionarlo automaticamente."

    # gui.rpy:123
    old "## The borders of the box containing the character's name, in left, top, right, bottom order."
    new "## I bordi del riquadro che contiene il nome del personaggio, in questo ordine: sinistro, superiore, destro, inferiore."

    # gui.rpy:127
    old "## If True, the background of the namebox will be tiled, if False, the background of the namebox will be scaled."
    new "## Se 'True', lo sfondo di namebox sarà tassellato (ripetuto come una piastrella). Se 'False' sarà invece scalato."

    # gui.rpy:132
    old "## The placement of dialogue relative to the textbox. These can be a whole number of pixels relative to the left or top side of the textbox, or 0.5 to center."
    new "## La posizione dei dialoghi, relativa al textbox. Può essere un numero esatto di pixel relativo ai bordi sinistro o superiore del textbox, oppure 0.5 per centrare."

    # gui.rpy:138
    old "## The maximum width of dialogue text, in pixels."
    new "## La larghezza massima del testo di dialogo, in pixel."

    # gui.rpy:141
    old "## The horizontal alignment of the dialogue text. This can be 0.0 for left-aligned, 0.5 for centered, and 1.0 for right-aligned."
    new "## L'allineamento orizzontale del testo di dialogo. Può essere 0.0 per allinearlo a sinistra, 0.5 al centro e 1.0 a destra."

    # gui.rpy:146
    old "## Buttons"
    new "## Pulsanti"

    # gui.rpy:148
    old "## These variables, along with the image files in gui/button, control aspects of how buttons are displayed."
    new "## Queste variabili, assieme alle immagini contenute in gui/button, definiscono l'aspetto dei pulsanti."

    # gui.rpy:151
    old "## The width and height of a button, in pixels. If None, Ren'Py computes a size."
    new "## Larghezza e altezza di un pulsante, in pixel. Se 'None', Ren'Py calcolerà una dimensione."

    # gui.rpy:155
    old "## The borders on each side of the button, in left, top, right, bottom order."
    new "## I bordi su ogni lato del pulsante, nell'ordine: sinistro, superiore, destro, inferiore."

    # gui.rpy:158
    old "## If True, the background image will be tiled. If False, the background image will be linearly scaled."
    new "## Se 'True', l'immagine di sfondo sarà tassellata. Se 'False', l'immagine di sfondo verrà scalata."

    # gui.rpy:162
    old "## The font used by the button."
    new "## Il font usato dal pulsante."

    # gui.rpy:165
    old "## The size of the text used by the button."
    new "## La dimensione del testo usato dal pulsante."

    # gui.rpy:179
    old "## These variables override settings for different kinds of buttons. Please see the gui documentation for the kinds of buttons available, and what each is used for."
    new "## Queste variabili sovrascrivono le impostazioni per differenti tipi di pulsante. Leggere nella documentazione i tipi di pulsante disponibili, e per cosa viene usato ciascuno."

    # gui.rpy:183
    old "## These customizations are used by the default interface:"
    new "## Queste personalizzazioni sono usate dall'interfaccia predefinita:"

    # gui.rpy:198
    old "## You can also add your own customizations, by adding properly-named variables. For example, you can uncomment the following line to set the width of a navigation button."
    new "## Puoi aggiungere le tue personalizzazioni, aggiungendo variabili con la giusta nomenclatura. Per esempio, puoi togliere il segno # dalla linea seguente per impostare una larghezza fissa dei pulsanti di navigazione."

    # gui.rpy:205
    old "## Choice Buttons"
    new "## Pulsanti Scelta"

    # gui.rpy:207
    old "## Choice buttons are used in the in-game menus."
    new "## I pulsanti di scelta sono usati per i menu interni al gioco."

    # gui.rpy:220
    old "## File Slot Buttons"
    new "## Pulsanti Slot File"

    # gui.rpy:222
    old "## A file slot button is a special kind of button. It contains a thumbnail image, and text describing the contents of the save slot. A save slot uses image files in gui/button, like the other kinds of buttons."
    new "## Un pulsante slot è un tipo di pulsante speciale. Contiene un'immagine miniatura, e testo che riporta i contenuti dello slot. Uno slot di salvataggio usa immagini presenti in gui/button, come tutti gli altri tipi di pulsante."

    # gui.rpy:226
    old "## The save slot button."
    new "## Il pulsante slot salvataggio."

    # gui.rpy:234
    old "## The width and height of thumbnails used by the save slots."
    new "## Larghezza e altezza delle miniature usate dallo slot di salvataggio."

    # gui.rpy:238
    old "## The number of columns and rows in the grid of save slots."
    new "## Numero di colonne e righe della griglia degli slot di salvataggio."

    # gui.rpy:243
    old "## Positioning and Spacing"
    new "## Posizioni e Spaziature"

    # gui.rpy:245
    old "## These variables control the positioning and spacing of various user interface elements."
    new "## Queste variabili controllano posizione e spaziatura di vari elementi dell'interfaccia."

    # gui.rpy:248
    old "## The position of the left side of the navigation buttons, relative to the left side of the screen."
    new "## Posizione del lato sinistro dei pulsanti di navigazione, relativa al lato sinistro dello schermo."

    # gui.rpy:252
    old "## The vertical position of the skip indicator."
    new "## Posizione verticale dell'indicatore 'SALTO'."

    # gui.rpy:255
    old "## The vertical position of the notify screen."
    new "## Posizione verticale delle notifiche."

    # gui.rpy:258
    old "## The spacing between menu choices."
    new "## Spaziatura fra le scelte."

    # gui.rpy:261
    old "## Buttons in the navigation section of the main and game menus."
    new "## Pulsanti nella sezione di navigazione dei menu Main e Game."

    # gui.rpy:264
    old "## Controls the amount of spacing between preferences."
    new "## Controlla l'ammontare di spazio fra le opzioni (preferences)."

    # gui.rpy:267
    old "## Controls the amount of spacing between preference buttons."
    new "## Controlla l'ammontare di spazio fra i pulsanti delle opzioni (preferences)."

    # gui.rpy:270
    old "## The spacing between file page buttons."
    new "## La spaziatura fra i pulsanti delle pagine file."

    # gui.rpy:273
    old "## The spacing between file slots."
    new "## Spaziatura fra gli slot."

    # gui.rpy:277
    old "## Frames"
    new "## Frame"

    # gui.rpy:279
    old "## These variables control the look of frames that can contain user interface components when an overlay or window is not present."
    new "## Queste variabili controllano l'aspetto dei frame che possono contenere elementi d'interfaccia quando un livello sostrato (overlay) o una finestra sono assenti."

    # gui.rpy:282
    old "## Generic frames that are introduced by player code."
    new "## Frame generici introdotti dal codice dell'utente."

    # gui.rpy:285
    old "## The frame that is used as part of the confirm screen."
    new "## Frame usato come parte del confirm screen."

    # gui.rpy:288
    old "## The frame that is used as part of the skip screen."
    new "## Frame usato come parte dello skip screen."

    # gui.rpy:291
    old "## The frame that is used as part of the notify screen."
    new "## Frame usato come parte delle notifiche."

    # gui.rpy:294
    old "## Should frame backgrounds be tiled?"
    new "## Gli sfondi del frame devono essere tassellati?"

    # gui.rpy:298
    old "## Bars, Scrollbars, and Sliders"
    new "## Barre, Barre Scorrimento e Selettori"

    # gui.rpy:300
    old "## These control the look and size of bars, scrollbars, and sliders."
    new "## Controllano aspetto e dimensioni di barre e selettori"

    # gui.rpy:302
    old "## The default GUI only uses sliders and vertical scrollbars. All of the other bars are only used in creator-written code."
    new "## La GUI predefinita impiega solo selettori, e barre di scorrimento verticali. Tutte le altre barre sono usate solo in codice personalizzato."

    # gui.rpy:305
    old "## The height of horizontal bars, scrollbars, and sliders. The width of vertical bars, scrollbars, and sliders."
    new "## Altezza delle barre orizzontali. Larghezza delle barre verticali."

    # gui.rpy:311
    old "## True if bar images should be tiled. False if they should be linearly scaled."
    new "## 'True' se le immagini devono venire tassellate. 'False' se devono venire scalate."

    # gui.rpy:316
    old "## Horizontal borders."
    new "## Bordi orizzontali."

    # gui.rpy:321
    old "## Vertical borders."
    new "## Bordi verticali."

    # gui.rpy:326
    old "## What to do with unscrollable scrollbars in the gui. \"hide\" hides them, while None shows them."
    new "## Cosa fare con barre di scorrimento che non possono scorrere. \"hide\" le nasconde, mentre None le mostra comunque."

    # gui.rpy:331
    old "## History"
    new "## Cronologia"

    # gui.rpy:333
    old "## The history screen displays dialogue that the player has already dismissed."
    new "## Lo screen 'History' mostra una cronologia dei dialoghi già letti dal giocatore."

    # gui.rpy:335
    old "## The number of blocks of dialogue history Ren'Py will keep."
    new "## Il numero di blocchi di dialogo conservati da Ren'Py nella cronologia."

    # gui.rpy:338
    old "## The height of a history screen entry, or None to make the height variable at the cost of performance."
    new "## L'altezza di un elemento nella schermata di cronologia, oppure None per avere altezze variabili al costo delle prestazioni."

    # gui.rpy:342
    old "## The position, width, and alignment of the label giving the name of the speaking character."
    new "## Posizione, larghezza e allineamento dell'etichetta che equivale al nome del personaggio in causa."

    # gui.rpy:349
    old "## The position, width, and alignment of the dialogue text."
    new "## Posizione, larghezza e allineamento del testo di dialogo."

    # gui.rpy:356
    old "## NVL-Mode"
    new "## Modalità NVL"

    # gui.rpy:358
    old "## The NVL-mode screen displays the dialogue spoken by NVL-mode characters."
    new "## Lo screen NVL mostra il dialogo dei personaggi NVL."

    # gui.rpy:360
    old "## The borders of the background of the NVL-mode background window."
    new "## I bordi della finestra in Modalità NVL."

    # gui.rpy:363
    old "## The height of an NVL-mode entry. Set this to None to have the entries dynamically adjust height."
    new "## L'altezza di un elemento NVL. Impostalo a 'None' e gli elementi stabiliranno un'altezza automatica."

    # gui.rpy:367
    old "## The spacing between NVL-mode entries when gui.nvl_height is None, and between NVL-mode entries and an NVL-mode menu."
    new "## La spaziatura fra gli elementi in Modalità NVL, quando gui.nvl_height è None, e fra questi e un menu NVL."

    # gui.rpy:384
    old "## The position, width, and alignment of nvl_thought text (the text said by the nvl_narrator character.)"
    new "## La posizione, larghezza e allineamento del testo nvl_thought (il testo del personaggio nvl_narrator.)"

    # gui.rpy:391
    old "## The position of nvl menu_buttons."
    new "## La posizione dei menu_buttons in modalità NVL."

    # gui.rpy:403
    old "## This increases the size of the quick buttons to make them easier to touch on tablets and phones."
    new "## Questo aumenta la dimensione dei pulsanti rapidi per renderli più facili da toccare su tablet e telefoni."

    # gui.rpy:409
    old "## This changes the size and spacing of various GUI elements to ensure they are easily visible on phones."
    new "## Questo cambia la dimensione e spaziatura di vari elementi della GUI per assicurarsi che siano facilmente visibili su telefono."

    # gui.rpy:413
    old "## Font sizes."
    new "## Dimensioni font."

    # gui.rpy:421
    old "## Adjust the location of the textbox."
    new "## Cambia la posizione del textbox."

    # gui.rpy:427
    old "## Change the size and spacing of items in the game menu."
    new "## Cambia la dimensione e la spaziatura degli elementi nel menu game."

    # gui.rpy:436
    old "## File button layout."
    new "## Schema pulsanti file."

    # gui.rpy:440
    old "## NVL-mode."
    new "## Modalità NVL."

    # gui.rpy:456
    old "## Quick buttons."
    new "## Pulsanti rapidi."

    # gui.rpy:17
    old "## GUI Configuration Variables"
    new "## Variabili di Configurazione GUI"

    # gui.rpy:168
    old "## The color of button text in various states."
    new "## Colore testo nel pulsante, secondo i vari stati."

    # gui.rpy:174
    old "## The horizontal alignment of the button text. (0.0 is left, 0.5 is center, 1.0 is right)."
    new "## L'allineamento orizzontale del testo nel pulsante. (0.1 a sinistra, 0.5 al centro, 1.0 a destra)."

    # gui.rpy:395
    old "## Localization"
    new "## Localizzazione"

    # gui.rpy:397
    old "## This controls where a line break is permitted. The default is suitable for most languages. A list of available values can be found at https://www.renpy.org/doc/html/style_properties.html#style-property-language"
    new "## Questo controlla dove avviene un'interruzione di riga. Il valore predefinito è valido per la maggior parte dei linguaggi. Una lista di valori disponibili si può trovare su:https://www.renpy.org/doc/html/style_properties.html#style-property-language"

    # gui.rpy:405
    old "## Mobile devices"
    new "## Dispositivi mobili"

    # gui/game/gui.rpy:5
    old "## The init offset statement causes the initialization statements in this file to run before init statements in any other file."
    new "## L'istruzione init offset fa sì che le istruzioni di inizializzazione in questo file vengano eseguite prima di quelle di inizializzazione in qualsiasi altro file."

    # gui/game/gui.rpy:14
    old "## Enable checks for invalid or unstable properties in screens or transforms"
    new "## Abilita i controlli per proprietà non valide o instabili nelle schermate o nelle trasformazioni"

    # gui/game/gui.rpy:278
    old "## The position of the main menu text."
    new "## La posizione del testo del menu principale."

    # gui/game/gui.rpy:287
    old "## Generic frames."
    new "## Cornici generiche."

    # gui/game/gui.rpy:307
    old "## The default GUI only uses sliders and vertical scrollbars. All of the other bars are only used in creator-written screens."
    new "## L'interfaccia grafica predefinita usa solo cursori e barre di scorrimento verticali. Tutte le altre barre sono utilizzate solo in schermate scritte dai creatori di giochi."

    # gui/game/gui.rpy:368
    old "## The maximum number of NVL-mode entries Ren'Py will display. When more entries than this are to be show, the oldest entry will be removed."
    new "## Il numero massimo di voci in modalità NVL che Ren'Py visualizzerà. Se ne devono essere visualizzate di più, la voce più vecchia verrà rimossa."

    # gui/game/gui.rpy:446
    old "## Change the size and spacing of various things."
    new "## Cambia dimensione e spaziatura di vari elementi."

