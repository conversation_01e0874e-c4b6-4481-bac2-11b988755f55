
translate italian strings:

    # _developer/developer.rpym:38
    old "Developer Menu"
    new "Menu Sviluppatore"

    # _developer/developer.rpym:43
    old "Reload Game (Shift+R)"
    new "Ricarica Gioco (Shift+R)"

    # _developer/developer.rpym:45
    old "<PERSON><PERSON><PERSON> (Shift+O)"
    new "<PERSON><PERSON><PERSON> (Shift+O)"

    # _developer/developer.rpym:47
    old "Variable Viewer"
    new "Visualizzatore Variabili"

    # _developer/developer.rpym:49
    old "Theme Test"
    new "Prova Tema"

    # _developer/developer.rpym:51
    old "Image Location Picker"
    new "Selettore Posizione Immagini"

    # _developer/developer.rpym:53
    old "Filename List"
    new "Lista File"

    # _developer/developer.rpym:57
    old "Show Image Load Log"
    new "Mostra Log di Caricamento Immagini"

    # _developer/developer.rpym:60
    old "Hide Image Load Log"
    new "Nascondi Log di Caricamento Immagini"

    # _developer/developer.rpym:95
    old "Nothing to inspect."
    new "Niente da ispezionare."

    # _developer/developer.rpym:217
    old "Return to the developer menu"
    new "Ritorna al menu sviluppatore"

    # _developer/developer.rpym:373
    old "Rectangle: %r"
    new "Rettangolo: %r"

    # _developer/developer.rpym:378
    old "Mouse position: %r"
    new "Posizione del mouse: %r"

    # _developer/developer.rpym:383
    old "Right-click or escape to quit."
    new "Click destro o Esc per uscire."

    # _developer/developer.rpym:412
    old "Rectangle copied to clipboard."
    new "Rettangolo copiato negli appunti."

    # _developer/developer.rpym:415
    old "Position copied to clipboard."
    new "Posizione copiata negli appunti."

    # _developer/developer.rpym:524
    old "✔ "
    new "✔ "

    # _developer/developer.rpym:527
    old "✘ "
    new "✘ "

    # _developer/developer.rpym:532
    old "\n{color=#cfc}✔ predicted image (good){/color}\n{color=#fcc}✘ unpredicted image (bad){/color}\n{color=#fff}Drag to move.{/color}"
    new "\n{color=#cfc}✔ immagine predetta (bene){/color}\n{color=#fcc}✘ immagine non predetta (male){/color}\n{color=#fff}Trascina per muovere.{/color}"

    # _developer/inspector.rpym:38
    old "Displayable Inspector"
    new "Ispezionatore dei Displayable"

    # _developer/inspector.rpym:61
    old "Size"
    new "Dimensione"

    # _developer/inspector.rpym:65
    old "Style"
    new "Stile"

    # _developer/inspector.rpym:71
    old "Location"
    new "Posizione"

    # _developer/inspector.rpym:122
    old "Inspecting Styles of [displayable_name!q]"
    new "Ispezione Stili di [displayable_name!q]"

    # _developer/inspector.rpym:139
    old "displayable:"
    new "visualizzabile:"

    # _developer/inspector.rpym:145
    old "        (no properties affect the displayable)"
    new "        (nessuna proprietà ha effetto sul displayable)"

    # _developer/inspector.rpym:147
    old "        (default properties omitted)"
    new "        (proprietà predefinite omesse)"

    # _developer/inspector.rpym:185
    old "<repr() failed>"
    new "<repr() fallito>"

    # 00console.rpy:182
    old "Press <esc> to exit console. Type help for help.\n"
    new "Premi <esc> per uscire dalla console. Scrivi \"help\" per avere aiuto.\n"

    # 00console.rpy:186
    old "Ren'Py script enabled."
    new "Script di Ren'Py abilitato."

    # 00console.rpy:188
    old "Ren'Py script disabled."
    new "Script di Ren'Py disabilitato."

    # 00console.rpy:398
    old "help: show this help"
    new "help: mostra questo aiuto"

    # 00console.rpy:403
    old "commands:\n"
    new "comandi:\n"

    # 00console.rpy:413
    old " <renpy script statement>: run the statement\n"
    new " <comando renpy script>: esegui il comando\n"

    # 00console.rpy:415
    old " <python expression or statement>: run the expression or statement"
    new " <espressione o comando python>: esegui l'espressione o il comando"

    # 00console.rpy:423
    old "clear: clear the console history"
    new "clear: pulisci la cronologia della console"

    # 00console.rpy:427
    old "exit: exit the console"
    new "exit: esci dalla console"

    # 00console.rpy:435
    old "load <slot>: loads the game from slot"
    new "load <slot>: carica il gioco dallo slot"

    # 00console.rpy:448
    old "save <slot>: saves the game in slot"
    new "save <slot>: salva il gioco nello slot"

    # 00console.rpy:459
    old "reload: reloads the game, refreshing the scripts"
    new "reload: ricarica il gioco, aggiornando gli script"

    # 00console.rpy:467
    old "watch <expression>: watch a python expression"
    new "watch <espressione>: osserva un'espressione Python"

    # 00console.rpy:493
    old "unwatch <expression>: stop watching an expression"
    new "unwatch <espressione>: smetti di osservare un'espressione"

    # 00console.rpy:519
    old "unwatchall: stop watching all expressions"
    new "unwatchall: smetti di osservare tutte le espressioni"

    # 00console.rpy:536
    old "jump <label>: jumps to label"
    new "jump <label>: salta alla label"

    # renpy/common/_developer/developer.rpym:43
    old "Interactive Director (D)"
    new "Interactive Director (D)"

    # renpy/common/_developer/developer.rpym:51
    old "Persistent Viewer"
    new "Visualizzatore dei dati Persistenti"

    # renpy/common/_developer/developer.rpym:59
    old "Show Image Load Log (F4)"
    new "Mostra il Registro di Caricamento delle Immagini (F4)"

    # renpy/common/_developer/developer.rpym:62
    old "Hide Image Load Log (F4)"
    new "Nascondi il Registro di Caricamento delle Immagini (F4)"

    # renpy/common/_developer/developer.rpym:65
    old "Image Attributes"
    new "Attributi dell'Immagine"

    # renpy/common/_developer/developer.rpym:70
    old "Speech Bubble Editor (Shift+B)"
    new "Editor di Bolle di Dialogo (Shift+B)"

    # renpy/common/_developer/developer.rpym:97
    old "[name] [attributes] (hidden)"
    new "[name] [attributes] (nascosto)"

    # renpy/common/_developer/developer.rpym:101
    old "[name] [attributes]"
    new "[name] [attributes]"

    # renpy/common/_developer/developer.rpym:162
    old "Hide deleted"
    new "Nascondi cancellati"

    # renpy/common/_developer/developer.rpym:162
    old "Show deleted"
    new "Mostra cancellati"

    # renpy/common/_developer/developer.rpym:389
    old "Type to filter: "
    new "Tipi da filtrare: "

    # renpy/common/_developer/developer.rpym:507
    old "Textures: [tex_count] ([tex_size_mb:.1f] MB)"
    new "Texture: [tex_count] ([tex_size_mb:.1f] MB)"

    # renpy/common/_developer/developer.rpym:511
    old "Image cache: [cache_pct:.1f]% ([cache_size_mb:.1f] MB)"
    new "Cache immagini: [cache_pct:.1f]% ([cache_size_mb:.1f] MB)"

    # renpy/common/00console.rpy:789
    old "help: show this help\n help <expr>: show signature and documentation of <expr>"
    new "help: mostra questo aiuto\n help <expr>: mostra la firma e la documentazione di <expr>"

    # renpy/common/00console.rpy:813
    old "Help may display undocumented functions. Please check that the function or\nclass you want to use is documented.\n\n"
    new "La guida potrebbe visualizzare funzioni non documentate. Verifica che la funzione o\n classe che vuoi usare utilizzare sia documentata.\n\n"

    # renpy/common/00console.rpy:854
    old "stack: print the return stack"
    new "stack: stampa a schermo lo stack di ritorno"

    # renpy/common/00console.rpy:908
    old "watch <expression>: watch a python expression\n watch short: makes the representation of traced expressions short (default)\n watch long: makes the representation of traced expressions as is"
    new "watch <expression>: traccia un'espressione python\n watch short: rende breve la rappresentazione delle espressioni tracciate (default)\n watch long: rende la rappresentazione delle espressioni tracciate così come è"

    # renpy/common/00console.rpy:1028
    old "short: Shorten the representation of objects on the console (default)."
    new "short: Accorcia la rappresentazione degli oggetti sulla console (impostazione predefinita)."

    # renpy/common/00console.rpy:1032
    old "long: Print the full representation of objects on the console."
    new "long: Stampa sulla console la rappresentazione completa degli oggetti."

    # renpy/common/00console.rpy:1036
    old "escape: Enables escaping of unicode symbols in unicode strings."
    new "escape: Abilita l'escape dei simboli unicode nelle stringhe unicode."

    # renpy/common/00console.rpy:1040
    old "unescape: Disables escaping of unicode symbols in unicode strings and print it as is (default)."
    new "unescape: Disabilita l'escape dei simboli unicode nelle stringhe unicode e le stampa a schermo così come sono (impostazione predefinita)."

