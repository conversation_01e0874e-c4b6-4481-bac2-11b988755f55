translate italian strings:

    # game/new_project.rpy:77
    old "{#language name and font}"
    new "Italiano"

    # about.rpy:39
    old "[version!q]"
    new "[version!q]"

    # about.rpy:43
    old "View license"
    new "Leggi licenza"

    # add_file.rpy:28
    old "FILENAME"
    new "NOME FILE"

    # add_file.rpy:28
    old "Enter the name of the script file to create."
    new "Inserisci il nome del file di script da creare."

    # add_file.rpy:31
    old "The filename must have the .rpy extension."
    new "Il file deve avere estensione .rpy."

    # add_file.rpy:39
    old "The file already exists."
    new "Il file esiste già."

    # add_file.rpy:42
    old "# Ren'Py automatically loads all script files ending with .rpy. To use this\n# file, define a label and jump to it from another file.\n"
    new "# Ren'Py carica automaticamente tutti i file di script che finiscono con .rpy.\n# Per usare questo file, definisci una label e saltaci (jump) da un altro file."

    # android.rpy:30
    old "To build Android packages, please download RAPT, unzip it, and place it into the Ren'Py directory. Then restart the Ren'Py launcher."
    new "Per compilare un pacchetto di Android si prega di scaricare RAPT, estrarlo, posizionarlo nella cartella di Ren'Py e riavviare il launcher di Ren'Py."

    # android.rpy:31
    old "An x86 Java Development Kit is required to build Android packages on Windows. The JDK is different from the JRE, so it's possible you have Java without having the JDK.\n\nPlease {a=http://www.oracle.com/technetwork/java/javase/downloads/jdk8-downloads-2133151.html}download and install the JDK{/a}, then restart the Ren'Py launcher."
    new "La versione a 32-bit del Java Development Kit è necessaria per compilare pacchetti Android su Windows. Il JDK è diverso dal JRE ed è quindi possibile che tu abbia installato Java senza però aver installato il JDK.\n\nSi prega di {a=http://www.oracle.com/technetwork/java/javase/downloads/jdk8-downloads-2133151.html}scaricare ed installare il JDK{/a} e, successivamente, riavviare il launcher di Ren'Py."

    # android.rpy:32
    old "RAPT has been installed, but you'll need to install the Android SDK before you can build Android packages. Choose Install SDK to do this."
    new "RAPT è stato installato ma devi ancora installare l'Android SDK prima di poter compilare pacchetti per Android; scegli \"Installa SDK\" per farlo."

    # android.rpy:33
    old "RAPT has been installed, but a key hasn't been configured. Please create a new key, or restore android.keystore."
    new "RAPT è stato installato, ma la chiave non è stata configurata. Si prega di creare una chiave o di ripristinare android.keystore."

    # android.rpy:34
    old "The current project has not been configured. Use \"Configure\" to configure it before building."
    new "Il progetto corrente non è stato configurato. Usa \"Configura\" per configurarlo prima della compilazione."

    # android.rpy:35
    old "Choose \"Build\" to build the current project, or attach an Android device and choose \"Build & Install\" to build and install it on the device."
    new "Scegli \"Compila\" per compilare il progetto corrente o collega un dispositivo Android e scegli \"Compila ed Installa\" per compilarlo ed installarlo sul dispositivo."

    # android.rpy:37
    old "Attempts to emulate an Android phone.\n\nTouch input is emulated through the mouse, but only when the button is held down. Escape is mapped to the menu button, and PageUp is mapped to the back button."
    new "Tenta di emulare un dispositivo Android.\n\nL'input tattile viene emulato attraverso il mouse, ma soltanto quando viene eseguito un click. Esc è assegnato al pulsante \"menu\" e PaginaSu è assegnato al pulsante \"indietro\"."

    # android.rpy:38
    old "Attempts to emulate an Android tablet.\n\nTouch input is emulated through the mouse, but only when the button is held down. Escape is mapped to the menu button, and PageUp is mapped to the back button."
    new "Tenta di emulare un tablet Android.\n\nL'input tattile viene emulato attraverso il mouse, ma solo quando viene eseguito un click. Esc è assegnato al pulsante \"menu\" e PaginaSu è assegnato al pulsante \"indietro\"."

    # android.rpy:39
    old "Attempts to emulate a televison-based Android console, like the OUYA or Fire TV.\n\nController input is mapped to the arrow keys, Enter is mapped to the select button, Escape is mapped to the menu button, and PageUp is mapped to the back button."
    new "Prova ed emulare una console connessa al televisore come OUYA o Fire TV.\n\nL'input del controller è assegnato alle freccie direzionali, Invio è assegnato al tasto \"select\", \"Esc\" è assegnato al tasto \"menu\" e PageUp è assegnato al tasto \"back\". "

    # android.rpy:41
    old "Downloads and installs the Android SDK and supporting packages. Optionally, generates the keys required to sign the package."
    new "Scarica ed installa l'Android SDK ed i pacchetti di supporto. Opzionalmente, genera le chiavi richieste per firmare il pacchetto."

    # android.rpy:42
    old "Configures the package name, version, and other information about this project."
    new "Configura il nome del pacchetto, la versione ed altre informazioni su questo progetto."

    # android.rpy:43
    old "Opens the file containing the Google Play keys in the editor.\n\nThis is only needed if the application is using an expansion APK. Read the documentation for more details."
    new "Apre il file contenente le chiavi di Google Play nell'editor.\n\nQuesto serve solamente se l'applicazione sta usando un APK di espansione. Leggi la documentazione per maggiori dettagli."

    # android.rpy:44
    old "Builds the Android package."
    new "Compila il pacchetto per Android."

    # android.rpy:45
    old "Builds the Android package, and installs it on an Android device connected to your computer."
    new "Compila il pacchetto per Android e lo installa su un dispositivo connesso al tuo computer."

    # android.rpy:46
    old "Builds the Android package, installs it on an Android device connected to your computer, then launches the app on your device."
    new "Compila il pacchetto per Android, lo installa su un dispositivo Android connesso al tuo computer, quindi lancia l'applicazione sul dispositivo."

    # android.rpy:48
    old "Connects to an Android device running ADB in TCP/IP mode."
    new "Connette ad un dispositivo Android che esegue ADB in modalità TCP/IP."

    # android.rpy:49
    old "Disconnects from an Android device running ADB in TCP/IP mode."
    new "Disconnette da un dispositivo Android che esegue ADB in modalità TCP/IP."

    # android.rpy:50
    old "Retrieves the log from the Android device and writes it to a file."
    new "Recupera il log dal dispositivo Android e lo copia in un file."

    # android.rpy:240
    old "Copying Android files to distributions directory."
    new "Copia dei file Android nella cartella delle distribuzioni."

    # android.rpy:304
    old "Android: [project.current.name!q]"
    new "Android: [project.current.name!q]"

    # android.rpy:324
    old "Emulation:"
    new "Emulazione:"

    # android.rpy:333
    old "Phone"
    new "Telefono"

    # android.rpy:337
    old "Tablet"
    new "Tablet"

    # android.rpy:341
    old "Television"
    new "Televisione"

    # android.rpy:353
    old "Build:"
    new "Compila:"

    # android.rpy:361
    old "Install SDK & Create Keys"
    new "Installa SDK e Crea Chiavi"

    # android.rpy:365
    old "Configure"
    new "Configura"

    # android.rpy:369
    old "Build Package"
    new "Compila pacchetto"

    # android.rpy:373
    old "Build & Install"
    new "Compila ed Installa"

    # android.rpy:377
    old "Build, Install & Launch"
    new "Compila, Installa & Lancia"

    # android.rpy:388
    old "Other:"
    new "Altro:"

    # android.rpy:396
    old "Remote ADB Connect"
    new "Connessione remota ad ADB"

    # android.rpy:400
    old "Remote ADB Disconnect"
    new "Disconessione remota da ADB"

    # android.rpy:404
    old "Logcat"
    new "Logcat"

    # android.rpy:437
    old "Before packaging Android apps, you'll need to download RAPT, the Ren'Py Android Packaging Tool. Would you like to download RAPT now?"
    new "Prima di assemblare le applicazioni di Android hai bisogno di scaricare RAPT (Ren'Py Android Packaging Tool). Vuoi scaricarlo adesso?"

    # android.rpy:496
    old "Remote ADB Address"
    new "Indirizzo remoto di ADB"

    # android.rpy:496
    old "Please enter the IP address and port number to connect to, in the form \"*************:5555\". Consult your device's documentation to determine if it supports remote ADB, and if so, the address and port to use."
    new "Si prega di inserire l'indirizzo IP ed il numero della porta a cui connettersi nel formato \"*************:5555\". Consulta le istruzioni del tuo dispositivo per determinare se questo supporta ADB e con quali numeri di indirizzo e porta."

    # android.rpy:508
    old "Invalid remote ADB address"
    new "Indirizzo remoto di ADB invalido"

    # android.rpy:508
    old "The address must contain one exactly one ':'."
    new "L'indirizzo deve contenere esattamente uno e un solo ':'."

    # android.rpy:512
    old "The host may not contain whitespace."
    new "L'host non può contenere spazi vuoti."

    # android.rpy:518
    old "The port must be a number."
    new "La porta deve essere un numero."

    # android.rpy:544
    old "Retrieving logcat information from device."
    new "Recupero informazioni logcat dal dispositivo."

    # choose_directory.rpy:73
    old "Ren'Py was unable to run python with tkinter to choose the directory. Please install the python-tk or tkinter package."
    new "Ren'Py non è stato in grado di eseguire python con tkinter per scegliere la cartella. Installa python-tk o il pacchetto tkinter."

    # choose_theme.rpy:303
    old "Could not change the theme. Perhaps options.rpy was changed too much."
    new "Non è stato possibile cambiare il tema. Forse options.rpy è stato modificato troppo radicalmente."

    # choose_theme.rpy:370
    old "Planetarium"
    new "Planetario"

    # choose_theme.rpy:425
    old "Choose Theme"
    new "Scegli Tema"

    # choose_theme.rpy:438
    old "Theme"
    new "Tema"

    # choose_theme.rpy:463
    old "Color Scheme"
    new "Schema Colori"

    # choose_theme.rpy:495
    old "Continue"
    new "Continua"

    # consolecommand.rpy:84
    old "INFORMATION"
    new "INFORMAZIONI"

    # consolecommand.rpy:84
    old "The command is being run in a new operating system console window."
    new "Il comando è in esecuzione in una nuova console del sistema operativo."

    # distribute.rpy:443
    old "Scanning project files..."
    new "Analisi dei file del progetto..."

    # distribute.rpy:459
    old "Building distributions failed:\n\nThe build.directory_name variable may not include the space, colon, or semicolon characters."
    new "Compilazione delle distribuzioni fallita:\n\nLa variable build.directory_name non può includere spazi, due punti o punti e virgole."

    # distribute.rpy:504
    old "No packages are selected, so there's nothing to do."
    new "Nessun pacchetto selezionato, nessun compito da eseguire."

    # distribute.rpy:516
    old "Scanning Ren'Py files..."
    new "Analisi dei file di Ren'Py..."

    # distribute.rpy:569
    old "All packages have been built.\n\nDue to the presence of permission information, unpacking and repacking the Linux and Macintosh distributions on Windows is not supported."
    new "Tutti i pacchetti sono stati compilati.\n\nData la presenza di limitazioni sui permessi, estrarre e ri-archiviare le distribuzioni Linux e Macintosh su Windows non è supportato."

    # distribute.rpy:752
    old "Archiving files..."
    new "Archiviazione dei file..."

    # distribute.rpy:1050
    old "Unpacking the Macintosh application for signing..."
    new "Spacchettamento dell'applicazione Macintosh per la firma..."

    # distribute.rpy:1060
    old "Signing the Macintosh application..."
    new "Firma dell'applicazione Macintosh..."

    # distribute.rpy:1082
    old "Creating the Macintosh DMG..."
    new "Creazione del Macintosh DMG..."

    # distribute.rpy:1091
    old "Signing the Macintosh DMG..."
    new "Firma del Macintosh DMG..."

    # distribute.rpy:1248
    old "Writing the [variant] [format] package."
    new "Scrittura del pacchetto [variant] [format]."

    # distribute.rpy:1261
    old "Making the [variant] update zsync file."
    new "Creazione del file di aggiornamento di zync per [variant]"

    # distribute.rpy:1404
    old "Processed {b}[complete]{/b} of {b}[total]{/b} files."
    new "Processati {b}[complete]{/b} file su {b}[total]{/b}."

    # distribute_gui.rpy:157
    old "Build Distributions: [project.current.name!q]"
    new "Compila distribuzioni: [project.current.name!q]"

    # distribute_gui.rpy:171
    old "Directory Name:"
    new "Nome Cartella:"

    # distribute_gui.rpy:175
    old "Executable Name:"
    new "Nome Eseguibile:"

    # distribute_gui.rpy:185
    old "Actions:"
    new "Azioni:"

    # distribute_gui.rpy:193
    old "Edit options.rpy"
    new "Modifica options.rpy"

    # distribute_gui.rpy:194
    old "Add from clauses to calls, once"
    new "Aggiungi argomenti 'from' ai comandi 'call'"

    # distribute_gui.rpy:195
    old "Refresh"
    new "Aggiorna"

    # distribute_gui.rpy:199
    old "Upload to itch.io"
    new "Carica su itch.io"

    # distribute_gui.rpy:215
    old "Build Packages:"
    new "Compila pacchetti:"

    # distribute_gui.rpy:234
    old "Options:"
    new "Opzioni:"

    # distribute_gui.rpy:239
    old "Build Updates"
    new "Compila Aggiornamenti"

    # distribute_gui.rpy:241
    old "Add from clauses to calls"
    new "Aggiungi argomento 'from' ai 'call'."

    # distribute_gui.rpy:242
    old "Force Recompile"
    new "Forza Ricompilazione"

    # distribute_gui.rpy:246
    old "Build"
    new "Compila"

    # distribute_gui.rpy:250
    old "Adding from clauses to call statements that do not have them."
    new "Aggiunta degli argomenti 'from' ai comandi 'call' che non ne hanno."

    # distribute_gui.rpy:271
    old "Errors were detected when running the project. Please ensure the project runs without errors before building distributions."
    new "Errori riscontrati durante l'esecuzione del progetto. Prego assicurarsi che il progetto possa essere eseguito senza errori prima di compilare distribuzioni."

    # distribute_gui.rpy:288
    old "Your project does not contain build information. Would you like to add build information to the end of options.rpy?"
    new "Il tuo progetto non contiene informazioni di compilazione. Vuoi aggiungere informazioni di compilazione alla fine di options.rpy?"

    # editor.rpy:150
    old "{b}Recommended.{/b} A beta editor with an easy to use interface and features that aid in development, such as spell-checking. Editra currently lacks the IME support required for Chinese, Japanese, and Korean text input."
    new "{b}Raccomandato.{/b} Un editor in fase Beta con un'interfaccia facile da usare e funzionalità adatte allo sviluppo come il controllo ortografico. Editra al momento non offre il supporto IMO richiesto per Cinese, Giapponese e Coreano."

    # editor.rpy:151
    old "{b}Recommended.{/b} A beta editor with an easy to use interface and features that aid in development, such as spell-checking. Editra currently lacks the IME support required for Chinese, Japanese, and Korean text input. On Linux, Editra requires wxPython."
    new "{b}Raccomandato.{/b} Un editor in fase Beta con un'interfaccia facile da usare e funzionalità adatte allo sviluppo come il controllo ortografico. Editra al momento non offre il supporto IMO richiesto per Cinese, Giapponese e Coreano e richiede wxPython su Linux."

    # editor.rpy:167
    old "This may have occurred because wxPython is not installed on this system."
    new "Potrebbe essersi verificato per via dell'assenza di wxPython su questo sistema."

    # editor.rpy:169
    old "Up to 22 MB download required."
    new "Verranno scaricati fino a 22 MB."

    # editor.rpy:182
    old "A mature editor that requires Java."
    new "Un editor maturo che richiede Java."

    # editor.rpy:182
    old "1.8 MB download required."
    new "Verranno scaricati 1.8 MB."

    # editor.rpy:182
    old "This may have occurred because Java is not installed on this system."
    new "Potrebbe essersi verificato per l'assenza di Java su questo sistema."

    # editor.rpy:191
    old "Invokes the editor your operating system has associated with .rpy files."
    new "Esegue l'editor che il tuo sistema operativo ha associato con i file .rpy."

    # editor.rpy:207
    old "Prevents Ren'Py from opening a text editor."
    new "Impedisce a Ren'Py di aprire un editor di testo."

    # editor.rpy:359
    old "An exception occurred while launching the text editor:\n[exception!q]"
    new "Si è verificata un'eccezione durante il lancio dell'editor:\n[exception!q]"

    # editor.rpy:457
    old "Select Editor"
    new "Seleziona Editor"

    # editor.rpy:472
    old "A text editor is the program you'll use to edit Ren'Py script files. Here, you can select the editor Ren'Py will use. If not already present, the editor will be automatically downloaded and installed."
    new "L'editor di testo è il programma che viene usato per modificare i file di script di Ren'Py. Qui puoi selezionare l'editor da usare con Ren'Py. Se assente nel sistema, l'editor verrà automaticamente scaricato ed installato."

    # editor.rpy:494
    old "Cancel"
    new "Cancella"

    # front_page.rpy:35
    old "Open [text] directory."
    new "Apri la cartella [text]."

    # front_page.rpy:93
    old "refresh"
    new "aggiorna"

    # front_page.rpy:120
    old "+ Create New Project"
    new "+ Crea Nuovo Progetto"

    # front_page.rpy:130
    old "Launch Project"
    new "Avvia Progetto"

    # front_page.rpy:147
    old "[p.name!q] (template)"
    new "[p.name!q] (modello)"

    # front_page.rpy:149
    old "Select project [text]."
    new "Seleziona progetto [text]."

    # front_page.rpy:165
    old "Tutorial"
    new "Tutorial"

    # front_page.rpy:166
    old "The Question"
    new "La Domanda"

    # front_page.rpy:182
    old "Active Project"
    new "Progetto Attivo"

    # front_page.rpy:190
    old "Open Directory"
    new "Apri Cartella"

    # front_page.rpy:195
    old "game"
    new "game"

    # front_page.rpy:196
    old "base"
    new "base"

    # front_page.rpy:197
    old "images"
    new "images"

    # front_page.rpy:198
    old "gui"
    new "gui"

    # front_page.rpy:204
    old "Edit File"
    new "Modifica File"

    # front_page.rpy:214
    old "All script files"
    new "Tutti i file di script"

    # front_page.rpy:223
    old "Navigate Script"
    new "Esplora Script"

    # front_page.rpy:234
    old "Check Script (Lint)"
    new "Controlla Script (Lint)"

    # front_page.rpy:237
    old "Change/Update GUI"
    new "Cambia/Aggiorna Interfaccia Grafica"

    # front_page.rpy:239
    old "Change Theme"
    new "Cambia Tema"

    # front_page.rpy:242
    old "Delete Persistent"
    new "Cancella Dati Persistenti"

    # front_page.rpy:251
    old "Build Distributions"
    new "Compila Distribuzioni"

    # front_page.rpy:253
    old "Android"
    new "Android"

    # front_page.rpy:254
    old "iOS"
    new "iOS"

    # front_page.rpy:255
    old "Generate Translations"
    new "Genera Traduzioni"

    # front_page.rpy:256
    old "Extract Dialogue"
    new "Estrai Dialoghi"

    # front_page.rpy:272
    old "Checking script for potential problems..."
    new "Controllo dello script per potenziali problemi..."

    # front_page.rpy:287
    old "Deleting persistent data..."
    new "Eliminazione dati persistenti..."

    # front_page.rpy:295
    old "Recompiling all rpy files into rpyc files..."
    new "Ricompilazione di tutti i file rpy in rpyc..."

    # gui7.rpy:236
    old "Select Accent and Background Colors"
    new "Seleziona Colori Evidenza e Sfondo"

    # gui7.rpy:250
    old "Please click on the color scheme you wish to use, then click Continue. These colors can be changed and customized later."
    new "Clicca sullo schema di colori che vuoi usare, quindi clicca Continua. Questi colori possono venire cambiati e personalizzati in seguito."

    # gui7.rpy:294
    old "{b}Warning{/b}\nContinuing will overwrite customized bar, button, save slot, scrollbar, and slider images.\n\nWhat would you like to do?"
    new "{b}Attenzione{/b}\nContinuare sovrascriverà barre, pulsanti, slot, barre scorrimento e selettori.\n\nCosa intendi fare?"

    # gui7.rpy:294
    old "Choose new colors, then regenerate image files."
    new "Scegli nuovi colori, quindi rigenera i file immagine."

    # gui7.rpy:294
    old "Regenerate the image files using the colors in gui.rpy."
    new "Rigenera i file immagine usando i colori in gui.rpy."

    # gui7.rpy:314
    old "PROJECT NAME"
    new "NOME DEL PROGETTO"

    # gui7.rpy:314
    old "Please enter the name of your project:"
    new "Inserisci il nome del tuo progetto:"

    # gui7.rpy:322
    old "The project name may not be empty."
    new "Il nome del progetto non può essere nullo."

    # gui7.rpy:327
    old "[project_name!q] already exists. Please choose a different project name."
    new "[project_name!q] esiste già. Si prega di scegliere un altro nome."

    # gui7.rpy:330
    old "[project_dir!q] already exists. Please choose a different project name."
    new "[project_dir!q] esiste già. Si prega di scegliere un altro nome."

    # gui7.rpy:341
    old "What resolution should the project use? Although Ren'Py can scale the window up and down, this is the initial size of the window, the size at which assets should be drawn, and the size at which the assets will be at their sharpest.\n\nThe default of [default_size[0]]x[default_size[1]] is a reasonable compromise."
    new "Quale risoluzione dovrebbe usare il progetto? Sebbene Ren'Py possa scalare la finestra, questa sarà la dimensione iniziale, la dimensione alla quale i materiali dovrebbero venire disegnati, e la dimensione alla quale avranno la migliore definizione.\n\nLo standard di [default_size[0]]x[default_size[1]] è un compromesso ragionevole."

    # gui7.rpy:389
    old "Creating the new project..."
    new "Creazione del nuovo progetto..."

    # gui7.rpy:391
    old "Updating the project..."
    new "Aggiornamento del progetto..."

    # interface.rpy:107
    old "Documentation"
    new "Documentazione"

    # interface.rpy:108
    old "Ren'Py Website"
    new "Sito Ufficiale"

    # interface.rpy:109
    old "Ren'Py Games List"
    new "Lista Giochi Ren'Py"

    # interface.rpy:117
    old "update"
    new "aggiorna"

    # interface.rpy:119
    old "preferences"
    new "preferenze"

    # interface.rpy:120
    old "quit"
    new "esci"

    # interface.rpy:232
    old "Due to package format limitations, non-ASCII file and directory names are not allowed."
    new "Per via delle limitazioni del formato del pacchetto, file e cartelle con nomi non-ASCII non sono permessi."

    # interface.rpy:327
    old "ERROR"
    new "ERRORE"

    # interface.rpy:356
    old "While [what!qt], an error occurred:"
    new "Durante [what!qt], si è verificato un errore:"

    # interface.rpy:356
    old "[exception!q]"
    new "[exception!q]"

    # interface.rpy:375
    old "Text input may not contain the {{ or [[ characters."
    new "Il testo inserito non può contenere i caratteri {{ o [[."

    # interface.rpy:380
    old "File and directory names may not contain / or \\."
    new "I nomi di file e cartelle non possono contenere / o \\."

    # interface.rpy:386
    old "File and directory names must consist of ASCII characters."
    new "I nomi di file e cartelle possono contenere solo caratteri ASCII."

    # interface.rpy:454
    old "PROCESSING"
    new "ELABORAZIONE"

    # interface.rpy:471
    old "QUESTION"
    new "DOMANDA"

    # interface.rpy:484
    old "CHOICE"
    new "SELEZIONA"

    # ios.rpy:28
    old "To build iOS packages, please download renios, unzip it, and place it into the Ren'Py directory. Then restart the Ren'Py launcher."
    new "Per compilare pacchetti iOS scarica renios, scompattalo, e ponilo nella cartella di Ren'Py. Quindi riavvia il Ren'Py launcher."

    # ios.rpy:29
    old "The directory in where Xcode projects will be placed has not been selected. Choose 'Select Directory' to select it."
    new "La cartella nella quale saranno contenuti i progetti Xcode non è stata selezionata. Scegli 'Seleziona Cartella' per definirla."

    # ios.rpy:30
    old "There is no Xcode project corresponding to the current Ren'Py project. Choose 'Create Xcode Project' to create one."
    new "Non esiste un progetto Xcode che corrisponda al progetto Ren'Py corrente. Scegli 'Crea Progetto Xcode' per crearne uno."

    # ios.rpy:31
    old "An Xcode project exists. Choose 'Update Xcode Project' to update it with the latest game files, or use Xcode to build and install it."
    new "E' presente un progetto Xcode. Scegli 'Aggiorna Progetto Xcode' per aggiornarlo con gli ultimi file di gioco, o usa Xcode per crearlo ed installarlo."

    # ios.rpy:33
    old "Attempts to emulate an iPhone.\n\nTouch input is emulated through the mouse, but only when the button is held down."
    new "Tenta di emulare un iPhone.\n\nIl touchscreen è emulato col mouse, ma solo quando si clicca su un pulsante."

    # ios.rpy:34
    old "Attempts to emulate an iPad.\n\nTouch input is emulated through the mouse, but only when the button is held down."
    new "Tenta di emulare un iPad.\n\nIl touchscreen è emulato col mouse, ma solo quando si clicca su un pulsante."

    # ios.rpy:36
    old "Selects the directory where Xcode projects will be placed."
    new "Seleziona la cartella dove saranno disposti i progetti Xcode."

    # ios.rpy:37
    old "Creates an Xcode project corresponding to the current Ren'Py project."
    new "Crea un progetto Xcode corrispondente all'attuale progetto Ren'Py."

    # ios.rpy:38
    old "Updates the Xcode project with the latest game files. This must be done each time the Ren'Py project changes."
    new "Aggiorna il progetto Xcode con gli ultimi file di gioco. Questo deve venire fatto ogni volta che vi sono cambiamenti nel progetto Ren'Py."

    # ios.rpy:39
    old "Opens the Xcode project in Xcode."
    new "Apre il progetto in Xcode."

    # ios.rpy:41
    old "Opens the directory containing Xcode projects."
    new "Apre la cartella che contiene i progetti Xcode."

    # ios.rpy:126
    old "The Xcode project already exists. Would you like to rename the old project, and replace it with a new one?"
    new "Il progetto Xcode esiste già. Vuoi cambiare nome al vecchio progetto e rimpiazzarlo con uno nuovo?"

    # ios.rpy:211
    old "iOS: [project.current.name!q]"
    new "iOS: [project.current.name!q]"

    # ios.rpy:240
    old "iPhone"
    new "iPhone"

    # ios.rpy:244
    old "iPad"
    new "iPad"

    # ios.rpy:264
    old "Select Xcode Projects Directory"
    new "Seleziona Cartella Progetti Xcode"

    # ios.rpy:268
    old "Create Xcode Project"
    new "Crea Progetto Xcode"

    # ios.rpy:272
    old "Update Xcode Project"
    new "Aggiorna Progetto Xcode"

    # ios.rpy:277
    old "Launch Xcode"
    new "Lancia Xcode"

    # ios.rpy:312
    old "Open Xcode Projects Directory"
    new "Apri Cartella Progetti Xcode"

    # ios.rpy:345
    old "Before packaging iOS apps, you'll need to download renios, Ren'Py's iOS support. Would you like to download renios now?"
    new "Prima di assemblare le app iOS, devi scaricare il supporto Ren'Py 'renios'. Vuoi scaricare renios adesso?"

    # ios.rpy:354
    old "XCODE PROJECTS DIRECTORY"
    new "CARTELLA PROGETTI XCODE"

    # ios.rpy:354
    old "Please choose the Xcode Projects Directory using the directory chooser.\n{b}The directory chooser may have opened behind this window.{/b}"
    new "Scegli la Cartella Progetti XCode con il selezionatore.\n{b}Il selezionatore potrebbe essersi aperto dietro questa finestra.{/b}"

    # ios.rpy:359
    old "Ren'Py has set the Xcode Projects Directory to:"
    new "Ren'Py ha impostato la Cartella Progetti Xcode a:"

    # itch.rpy:60
    old "The built distributions could not be found. Please choose 'Build' and try again."
    new "Non trovo le distribuzioni compilate. Scegli 'Compila' e prova di nuovo."

    # itch.rpy:91
    old "No uploadable files were found. Please choose 'Build' and try again."
    new "Non trovo file adeguati all'upload. Scegli 'Compila' e prova di nuovo."

    # itch.rpy:99
    old "The butler program was not found."
    new "Non trovo il programma butler."

    # itch.rpy:99
    old "Please install the itch.io app, which includes butler, and try again."
    new "Prego installa l'app itch.io, che include butler, e prova di nuovo."

    # itch.rpy:108
    old "The name of the itch project has not been set."
    new "Il nome del progetto itch non è stato impostato."

    # itch.rpy:108
    old "Please {a=https://itch.io/game/new}create your project{/a}, then add a line like \n{vspace=5}define build.itch_project = \"user-name/game-name\"\n{vspace=5} to options.rpy."
    new "Prego {a=https://itch.io/game/new}crea il tuo progetto{/a}, quindi aggiungi una linea simile a \n{vspace=5}define build.itch_project = \"user-name/game-name\"\n{vspace=5} nel file options.rpy."

    # mobilebuild.rpy:109
    old "{a=%s}%s{/a}"
    new "{a=%s}%s{/a}"

    # navigation.rpy:168
    old "Navigate: [project.current.name]"
    new "Esplorazione: [project.current.name]"

    # navigation.rpy:177
    old "Order: "
    new "Ordine: "

    # navigation.rpy:178
    old "alphabetical"
    new "alfabetico"

    # navigation.rpy:180
    old "by-file"
    new "per file"

    # navigation.rpy:182
    old "natural"
    new "naturale"

    # navigation.rpy:194
    old "Category:"
    new "Categoria:"

    # navigation.rpy:196
    old "files"
    new "file"

    # navigation.rpy:197
    old "labels"
    new "label"

    # navigation.rpy:198
    old "defines"
    new "define"

    # navigation.rpy:199
    old "transforms"
    new "transform"

    # navigation.rpy:200
    old "screens"
    new "screen"

    # navigation.rpy:201
    old "callables"
    new "callable"

    # navigation.rpy:202
    old "TODOs"
    new "TODO"

    # navigation.rpy:241
    old "+ Add script file"
    new "+ Aggiungi file di script"

    # navigation.rpy:249
    old "No TODO comments found.\n\nTo create one, include \"# TODO\" in your script."
    new "Nessun commento TODO trovato.\n\nPer crearne uno, includi \"# TODO\" nel tuo script."

    # navigation.rpy:256
    old "The list of names is empty."
    new "La lista dei nomi è vuota."

    # new_project.rpy:38
    old "New GUI Interface"
    new "Nuova Interfaccia Grafica"

    # new_project.rpy:48
    old "Both interfaces have been translated to your language."
    new "Entrambe le interfacce sono state tradotte nella tua lingua."

    # new_project.rpy:50
    old "Only the new GUI has been translated to your language."
    new "Solo la nuova interfaccia grafica è stata tradotta nella tua lingua."

    # new_project.rpy:52
    old "Only the legacy theme interface has been translated to your language."
    new "Solo le interfacce del vecchio tema sono state tradotte nella tua lingua."

    # new_project.rpy:54
    old "Neither interface has been translated to your language."
    new "Nessuna interfaccia è stata tradotta nella tua lingua."

    # new_project.rpy:63
    old "The projects directory could not be set. Giving up."
    new "Non è stato possibile impostare la cartella dei progetti. Annullamento."

    # new_project.rpy:69
    old "Which interface would you like to use? The new GUI has a modern look, supports wide screens and mobile devices, and is easier to customize. Legacy themes might be necessary to work with older example code.\n\n[language_support!t]\n\nIf in doubt, choose the new GUI, then click Continue on the bottom-right."
    new "Quale interfaccia vorresti usare? La nuova interfaccia grafica ha un aspetto moderno, supporta il wide screen e i dispositivi mobili, ed è più facile da personalizzare. I vecchi temi possono essere necessari per operare con con esempi datati.\n\n[language_support!t]\n\nNel dubbio, scegli la nuova interfaccia, quindi clicca Continua in basso a destra."

    # new_project.rpy:69
    old "Legacy Theme Interface"
    new "Interfaccia Temi Vecchia"

    # new_project.rpy:90
    old "Choose Project Template"
    new "Scegli un Modello"

    # new_project.rpy:108
    old "Please select a template to use for your new project. The template sets the default font and the user interface language. If your language is not supported, choose 'english'."
    new "Scegli un modello da usare per il tuo nuovo progetto. Il modello imposta il font predefinito e la lingua dell'interfaccia. Se la tua lingua non è supportata, scegli 'english'."

    # preferences.rpy:64
    old "Launcher Preferences"
    new "Preferenze del Launcher"

    # preferences.rpy:85
    old "Projects Directory:"
    new "Cartella Progetti:"

    # preferences.rpy:92
    old "[persistent.projects_directory!q]"
    new "[persistent.projects_directory!q]"

    # preferences.rpy:94
    old "Projects directory: [text]"
    new "Cartella progetti: [text]"

    # preferences.rpy:96
    old "Not Set"
    new "Non Impostato"

    # preferences.rpy:111
    old "Text Editor:"
    new "Editor Testi:"

    # preferences.rpy:117
    old "Text editor: [text]"
    new "Editor di testo: [text]"

    # preferences.rpy:133
    old "Update Channel:"
    new "Canale Aggiornamenti:"

    # preferences.rpy:153
    old "Navigation Options:"
    new "Opzioni Navigazione:"

    # preferences.rpy:157
    old "Include private names"
    new "Includi nomi privati"

    # preferences.rpy:158
    old "Include library names"
    new "Includi nomi di libreria"

    # preferences.rpy:168
    old "Launcher Options:"
    new "Opzioni del Launcher:"

    # preferences.rpy:172
    old "Hardware rendering"
    new "Rendering via hardware"

    # preferences.rpy:173
    old "Show templates"
    new "Mostra modelli"

    # preferences.rpy:174
    old "Show edit file section"
    new "Mostra sezione modifica file"

    # preferences.rpy:175
    old "Large fonts"
    new "Caratteri grandi"

    # preferences.rpy:178
    old "Console output"
    new "Output console"

    # preferences.rpy:199
    old "Open launcher project"
    new "Apri progetto del launcher"

    # preferences.rpy:338
    old "Open projects.txt"
    new "Apri projects.txt"

    # preferences.rpy:213
    old "Language:"
    new "Lingua:"

    # project.rpy:47
    old "After making changes to the script, press shift+R to reload your game."
    new "Dopo aver modificato lo script, premi Shift+R per ricaricare il gioco."

    # project.rpy:47
    old "Press shift+O (the letter) to access the console."
    new "Premi Shift+O (la lettera O) per aprire la console."

    # project.rpy:47
    old "Press shift+D to access the developer menu."
    new "Premi Shift+D per accedere al Menu Sviluppatore."

    # project.rpy:47
    old "Have you backed up your projects recently?"
    new "Hai fatto copie di sicurezza del tuo progetto?"

    # project.rpy:229
    old "Launching the project failed."
    new "Avvio del progetto fallito."

    # project.rpy:229
    old "Please ensure that your project launches normally before running this command."
    new "Prego verificare che il progetto si avvii normalmente prima di eseguire questo comando."

    # project.rpy:242
    old "Ren'Py is scanning the project..."
    new "Ren'Py sta analizzando il progetto..."

    # project.rpy:568
    old "Launching"
    new "Avvio"

    # project.rpy:597
    old "PROJECTS DIRECTORY"
    new "CARTELLA PROGETTI"

    # project.rpy:597
    old "Please choose the projects directory using the directory chooser.\n{b}The directory chooser may have opened behind this window.{/b}"
    new "Si prega di scegliere la cartella dei progetti tramite il selettore di cartella.\n{b}Il selettore di cartella potrebbe essersi aperto dietro questa finestra.{/b}"

    # project.rpy:597
    old "This launcher will scan for projects in this directory, will create new projects in this directory, and will place built projects into this directory."
    new "Il Launcher cercherà i progetti in questa cartella, creerà i nuovi progetti in questa cartella e posizionerà i progetti già compilati in questa cartella."

    # project.rpy:602
    old "Ren'Py has set the projects directory to:"
    new "Ren'Py ha impostato la cartella dei progetti a:"

    # translations.rpy:63
    old "Translations: [project.current.name!q]"
    new "Traduzioni: [project.current.name!q]"

    # translations.rpy:104
    old "The language to work with. This should only contain lower-case ASCII characters and underscores."
    new "La lingua con cui lavorare. Dovrebbe contenere solo caratteri ASCII minuscoli e tratti."

    # translations.rpy:130
    old "Generate empty strings for translations"
    new "Crea stringhe vuote nelle traduzioni"

    # translations.rpy:148
    old "Generates or updates translation files. The files will be placed in game/tl/[persistent.translate_language!q]."
    new "Crea o aggiorna file di traduzione. I file saranno piazzati in game/tl/[persistent.translate_language!q]."

    # translations.rpy:168
    old "Extract String Translations"
    new "Estrai Stringhe di Traduzione"

    # translations.rpy:170
    old "Merge String Translations"
    new "Unisci Stringhe di Traduzione"

    # translations.rpy:175
    old "Replace existing translations"
    new "Rimpiazza traduzioni esistenti"

    # translations.rpy:176
    old "Reverse languages"
    new "Inverti lingue"

    # translations.rpy:180
    old "Update Default Interface Translations"
    new "Aggiorna Traduzioni Predefinite Interfaccia"

    # translations.rpy:200
    old "The extract command allows you to extract string translations from an existing project into a temporary file.\n\nThe merge command merges extracted translations into another project."
    new "Il comando di estrazione ti consente di estrarre stringhe da tradurre da un progetto esistente in un file temporaneo.\n\nIl comando unione unisce le traduzioni estratte dentro un altro progetto."

    # translations.rpy:224
    old "Ren'Py is generating translations...."
    new "Ren'Py sta generando le traduzioni..."

    # translations.rpy:235
    old "Ren'Py has finished generating [language] translations."
    new "Ren'Py ha finito di generare le traduzioni in [language]."

    # translations.rpy:248
    old "Ren'Py is extracting string translations..."
    new "Ren'Py sta estraendo le stringhe da tradurre..."

    # translations.rpy:251
    old "Ren'Py has finished extracting [language] string translations."
    new "Ren'Py ha finito di estrarre le stringhe in [language]."

    # translations.rpy:271
    old "Ren'Py is merging string translations..."
    new "Ren'Py sta unendo le stringhe di traduzione..."

    # translations.rpy:274
    old "Ren'Py has finished merging [language] string translations."
    new "Ren'Py ha finito di unire le stringhe in [language]."

    # translations.rpy:282
    old "Updating default interface translations..."
    new "Aggiornamento traduzioni interfaccia..."

    # translations.rpy:306
    old "Extract Dialogue: [project.current.name!q]"
    new "Estrazione Dialoghi: [project.current.name!q]"

    # translations.rpy:322
    old "Format:"
    new "Formato:"

    # translations.rpy:330
    old "Tab-delimited Spreadsheet (dialogue.tab)"
    new "Foglio di calcolo (dialogue.tab)"

    # translations.rpy:331
    old "Dialogue Text Only (dialogue.txt)"
    new "Testo semplice (dialogue.txt)"

    # translations.rpy:344
    old "Strip text tags from the dialogue."
    new "Escludi etichette dal dialogo."

    # translations.rpy:345
    old "Escape quotes and other special characters."
    new "ESC e caratteri speciali."

    # translations.rpy:346
    old "Extract all translatable strings, not just dialogue."
    new "Estrai tutte le stringhe traducibili, non solo i dialoghi"

    # translations.rpy:374
    old "Ren'Py is extracting dialogue...."
    new "Ren'Py sta estrendo il dialogo..."

    # translations.rpy:378
    old "Ren'Py has finished extracting dialogue. The extracted dialogue can be found in dialogue.[persistent.dialogue_format] in the base directory."
    new "Ren'Py ha finito di estrarre i dialoghi. I dialghi estratti si trovano in dialogue.[persistent.dialogue_format] nella cartella base."

    # updater.rpy:75
    old "Select Update Channel"
    new "Seleziona Canale di Aggiornamento"

    # updater.rpy:86
    old "The update channel controls the version of Ren'Py the updater will download. Please select an update channel:"
    new "Il canale di aggiornamento determina la versione di Ren'Py che l'updater scaricherà. Prego scegliere un canale di aggiornamento:"

    # updater.rpy:91
    old "Release"
    new "Release"

    # updater.rpy:97
    old "{b}Recommended.{/b} The version of Ren'Py that should be used in all newly-released games."
    new "{b}Raccomandato.{/b} La versione di Ren'Py che dovrebbe essere usata in tutti i giochi più recenti."

    # updater.rpy:102
    old "Prerelease"
    new "Prerelease"

    # updater.rpy:108
    old "A preview of the next version of Ren'Py that can be used for testing and taking advantage of new features, but not for final releases of games."
    new "Un'anteprima della prossima versione di Ren'Py che può essere usata per provare le nuove funzionalità ma non per il rilascio finale dei giochi"

    # updater.rpy:114
    old "Experimental"
    new "Sperimentale"

    # updater.rpy:120
    old "Experimental versions of Ren'Py. You shouldn't select this channel unless asked by a Ren'Py developer."
    new "Versioni sperimentali di Ren'Py. Di interesse soltanto per gli sviluppatori di Ren'Py."

    # updater.rpy:126
    old "Nightly"
    new "Nightly"

    # updater.rpy:132
    old "The bleeding edge of Ren'Py development. This may have the latest features, or might not run at all."
    new "La versione più recente di Ren'Py. Potrebbe avere maggiori funzionalità come anche non funzionare affatto."

    # updater.rpy:152
    old "An error has occurred:"
    new "Si è verificato un errore:"

    # updater.rpy:154
    old "Checking for updates."
    new "Controllo degli aggiornamenti."

    # updater.rpy:156
    old "Ren'Py is up to date."
    new "Ren'Py è aggiornato."

    # updater.rpy:158
    old "[u.version] is now available. Do you want to install it?"
    new "[u.version] è disponibile. Vuoi installarlo?"

    # updater.rpy:160
    old "Preparing to download the update."
    new "Preparazione  dello scaricamento dell'aggiornamento."

    # updater.rpy:162
    old "Downloading the update."
    new "Scaricamento dell'aggiornamento."

    # updater.rpy:164
    old "Unpacking the update."
    new "Estrazione dell'aggiornamento."

    # updater.rpy:166
    old "Finishing up."
    new "Finalizzazione."

    # updater.rpy:168
    old "The update has been installed. Ren'Py will restart."
    new "L'aggiornamento è stato installato. Ren'Py verrà riavviato."

    # updater.rpy:170
    old "The update has been installed."
    new "L'aggiornamento è stato installato."

    # updater.rpy:172
    old "The update was cancelled."
    new "L'aggiornamento è stato annullato."

    # updater.rpy:189
    old "Ren'Py Update"
    new "Aggiornamento di Ren'Py"

    # updater.rpy:195
    old "Proceed"
    new "Procedi"

    # choose_directory.rpy:104
    old "The selected projects directory is not writable."
    new "La cartella progetti selezionata non è scrivibile"

    # distribute.rpy:1061
    old "Signing the Macintosh application...\n(This may take a long time.)"
    new "Firma dell'applicazione Macintosh...\n(Può volerci molto tempo.)"

    # front_page.rpy:91
    old "PROJECTS:"
    new "PROGETTI:"

    # game/add_file.rpy:37
    old "The file name may not be empty."
    new "Il nome del file non può essere vuoto."

    # game/android.rpy:37
    old "A 64-bit/x64 Java [JDK_REQUIREMENT] Development Kit is required to build Android packages on Windows. The JDK is different from the JRE, so it's possible you have Java without having the JDK.\n\nPlease {a=https://www.renpy.org/jdk/[JDK_REQUIREMENT]}download and install the JDK{/a}, then restart the Ren'Py launcher."
    new "Per creare pacchetti Android su Windows è necessario un Java Development Kit [JDK_REQUIREMENT] a 64 bit/x64. Il JDK è diverso dal JRE, quindi è possibile che tu abbia Java senza avere il JDK.\n\n{a=https://www.renpy.org/jdk/[JDK_REQUIREMENT]}Scarica ed installa il JDK{/a}, quindi riavvia il launcher Ren'Py."

    # game/android.rpy:39
    old "RAPT has been installed, but a key hasn't been configured. Please generate new keys, or copy android.keystore and bundle.keystore to the base directory."
    new "RAPT è stato installato, ma non è stata configurata una chiave. Genera delle nuove chiavi, o copia dei file android.keystore e bundle.keystore esistenti nella cartella di base."

    # game/android.rpy:41
    old "Please select if you want a Play Bundle (for Google Play), or a Universal APK (for sideloading and other app stores)."
    new "Seleziona se vuoi un Play Bundle (per Google Play) o un APK universale (per il sideloading ed altri app store)."

    # game/android.rpy:46
    old "Attempts to emulate a televison-based Android console.\n\nController input is mapped to the arrow keys, Enter is mapped to the select button, Escape is mapped to the menu button, and PageUp is mapped to the back button."
    new "Tenta di emulare una console Android per televisori.\n\nL'input del controller è mappato ai tasti freccia, Invio al pulsante di selezione, Esc al pulsante del menu, e PageUp al pulsante indietro."

    # game/android.rpy:48
    old "Downloads and installs the Android SDK and supporting packages."
    new "Scarica e installa l'SDK di Android e i pacchetti di supporto."

    # game/android.rpy:49
    old "Generates the keys required to sign the package."
    new "Genera le chiavi necessarie per firmare il pacchetto."

    # game/android.rpy:56
    old "Lists the connected devices."
    new "Elenca i dispositivi collegati."

    # game/android.rpy:57
    old "Pairs with a device over Wi-Fi, on Android 11+."
    new "Si abbina ad un dispositivo tramite Wi-Fi, su Android 11+."

    # game/android.rpy:58
    old "Connects to a device over Wi-Fi, on Android 11+."
    new "Si collega ad un dispositivo tramite Wi-Fi, su Android 11+."

    # game/android.rpy:59
    old "Disconnects a device connected over Wi-Fi."
    new "Disconnette un dispositivo connesso tramite Wi-Fi."

    # game/android.rpy:61
    old "Removes Android temporary files."
    new "Rimuove i file temporanei di Android."

    # game/android.rpy:63
    old "Builds an Android App Bundle (ABB), intended to be uploaded to Google Play. This can include up to 2GB of data."
    new "Compila un Bundle Applicazione Android (ABB), destinato ad essere caricato su Google Play. Può includere fino a 2 GB di dati."

    # game/android.rpy:64
    old "Builds a Universal APK package, intended for sideloading and stores other than Google Play. This can include up to 2GB of data."
    new "Crea un pacchetto APK universale, destinato al sideloading e a negozi diversi da Google Play. Può includere fino a 2 GB di dati."

    # game/android.rpy:327
    old "Android: [project.current.display_name!q]"
    new "Android: [project.current.display_name!q]"

    # game/android.rpy:383
    old "Install SDK"
    new "Installazione dell'SDK"

    # game/android.rpy:387
    old "Generate Keys"
    new "Generazione delle Chiavi"

    # game/android.rpy:397
    old "Play Bundle"
    new "Play Bundle"

    # game/android.rpy:402
    old "Universal APK"
    new "APK Universale"

    # game/android.rpy:452
    old "List Devices"
    new "Elenco Dispositivi"

    # game/android.rpy:456
    old "Wi-Fi Debugging Pair"
    new "Abbinamento Debug Wi-Fi"

    # game/android.rpy:460
    old "Wi-Fi Debugging Connect"
    new "Connessione Debug Wi-Fi"

    # game/android.rpy:464
    old "Wi-Fi Debugging Disconnect"
    new "Disconnessione Debug Wi-Fi"

    # game/android.rpy:468
    old "Clean"
    new "Pulisci"

    # game/android.rpy:573
    old "Wi-Fi Pairing Code"
    new "Codice di Abbinamento Wi-Fi"

    # game/android.rpy:573
    old "If supported, this can be found in 'Developer options', 'Wireless debugging', 'Pair device with pairing code'."
    new "Se supportato, si trova in \"Opzioni sviluppatore\", \"Debug wireless\", \"Abbinamento del dispositivo con codice\"."

    # game/android.rpy:580
    old "Pairing Host & Port"
    new "Abbinamento di Host e Porta"

    # game/android.rpy:596
    old "IP Address & Port"
    new "Indirizzo IP e Porta"

    # game/android.rpy:596
    old "If supported, this can be found in 'Developer options', 'Wireless debugging'."
    new "Se supportato, si trova in 'Opzioni sviluppatore', 'Debug wireless'."

    # game/android.rpy:612
    old "This can be found in 'List Devices'."
    new "Si trova in 'Elenco dispositivi'."

    # game/android.rpy:632
    old "Cleaning up Android project."
    new "Pulizia del progetto Android."

    # game/androidstrings.rpy:7
    old "{} is not a directory."
    new "{} non è una cartella."

    # game/androidstrings.rpy:8
    old "{} does not contain a Ren'Py game."
    new "{} non contiene un gioco Ren'Py."

    # game/androidstrings.rpy:10
    old "Run configure before attempting to build the app."
    new "Esegui configura prima di tentare la compilazione della app."

    # game/androidstrings.rpy:11
    old "Updating project."
    new "Aggiornamento del progetto."

    # game/androidstrings.rpy:12
    old "Creating assets directory."
    new "Creazione della cartella delle risorse."

    # game/androidstrings.rpy:13
    old "Packaging internal data."
    new "Pacchettizzazione dei dati interni."

    # game/androidstrings.rpy:14
    old "I'm using Gradle to build the package."
    new "Sto usando Gradle per creare il pacchetto."

    # game/androidstrings.rpy:15
    old "The build seems to have failed."
    new "La compilazione sembra aver fallito."

    # game/androidstrings.rpy:16
    old "I'm installing the bundle."
    new "Sto installando il pacchetto."

    # game/androidstrings.rpy:17
    old "Installing the bundle appears to have failed."
    new "L'installazione del pacchetto sembra aver fallita."

    # game/androidstrings.rpy:18
    old "Launching app."
    new "Avvio dell'applicazione."

    # game/androidstrings.rpy:19
    old "Launching the app appears to have failed."
    new "L'avvio dell'applicazione sembra aver fallito."

    # game/androidstrings.rpy:20
    old "The build seems to have succeeded."
    new "La compilazione sembra essere riuscita."

    # game/androidstrings.rpy:21
    old "What is the full name of your application? This name will appear in the list of installed applications."
    new "Qual è il nome completo per la tua applicazione? Questo nome apparirà nell'elenco delle applicazioni installate."

    # game/androidstrings.rpy:22
    old "What is the short name of your application? This name will be used in the launcher, and for application shortcuts."
    new "Qual è il nome breve per la tua applicazione? Questo nome verrà utilizzato nel launcher e per le scorciatoie all'applicazione."

    # game/androidstrings.rpy:23
    old "What is the name of the package?\n\nThis is usually of the form com.domain.program or com.domain.email.program. It may only contain ASCII letters and dots. It must contain at least one dot."
    new "Qual è il nome del pacchetto?\n\nDi solito ha la forma com.dominio.programma o com.dominio.email.programma. Può contenere solo lettere ASCII e punti. Deve contenere almeno un punto."

    # game/androidstrings.rpy:24
    old "The package name may not be empty."
    new "Il nome del pacchetto non può essere vuoto."

    # game/androidstrings.rpy:25
    old "The package name may not contain spaces."
    new "Il nome del pacchetto non può contenere spazi."

    # game/androidstrings.rpy:26
    old "The package name must contain at least one dot."
    new "Il nome del pacchetto deve contenere almeno un punto."

    # game/androidstrings.rpy:27
    old "The package name may not contain two dots in a row, or begin or end with a dot."
    new "Il nome del pacchetto non può contenere due punti di seguito, né iniziare o terminare con un punto."

    # game/androidstrings.rpy:28
    old "Each part of the package name must start with a letter, and contain only letters, numbers, and underscores."
    new "Ogni parte del nome del pacchetto deve iniziare con una lettera e contenere solo lettere, numeri e trattini bassi."

    # game/androidstrings.rpy:29
    old "{} is a Java keyword, and can't be used as part of a package name."
    new "{} è una parola chiave di Java e non può essere usata come parte del nome di un pacchetto."

    # game/androidstrings.rpy:30
    old "What is the application's version?\n\nThis should be the human-readable version that you would present to a person. It must contain only numbers and dots."
    new "Qual è la versione dell'applicazione?\n\nDeve essere la versione leggibile che presenteresti anche ad una persona. Deve contenere solo numeri e punti."

    # game/androidstrings.rpy:31
    old "The version number must contain only numbers and dots."
    new "Il numero di versione deve contenere solo numeri e punti."

    # game/androidstrings.rpy:32
    old "How much RAM (in GB) do you want to allocate to Gradle?\nThis must be a positive integer number."
    new "Quanta RAM (in GB) vuoi allocare a Gradle?\nDeve essere un numero intero positivo."

    # game/androidstrings.rpy:33
    old "The RAM size must contain only numbers and be positive."
    new "La dimensione della RAM deve contenere solo numeri ed essere positiva."

    # game/androidstrings.rpy:34
    old "How would you like your application to be displayed?"
    new "Come desideri che venga visualizzata l'applicazione?"

    # game/androidstrings.rpy:35
    old "In landscape orientation."
    new "In orientamento orizzontale."

    # game/androidstrings.rpy:36
    old "In portrait orientation."
    new "In orientamento verticale."

    # game/androidstrings.rpy:37
    old "In the user's preferred orientation."
    new "Nell'orientamento preferito dall'utente."

    # game/androidstrings.rpy:38
    old "Which app store would you like to support in-app purchasing through?"
    new "Attraverso quale app store vuoi supportare gli acquisti in-app?"

    # game/androidstrings.rpy:39
    old "Google Play."
    new "Google Play."

    # game/androidstrings.rpy:40
    old "Amazon App Store."
    new "Amazon App Store."

    # game/androidstrings.rpy:41
    old "Both, in one app."
    new "Entrambi in un'unica applicazione."

    # game/androidstrings.rpy:42
    old "Neither."
    new "Né l'uno né l'altro."

    # game/androidstrings.rpy:43
    old "Do you want to automatically update the Java source code?"
    new "Vuoi aggiornare automaticamente il codice sorgente Java?"

    # game/androidstrings.rpy:44
    old "Yes. This is the best choice for most projects."
    new "Sì. Questa è la scelta migliore per la maggior parte dei progetti."

    # game/androidstrings.rpy:45
    old "No. This may require manual updates when Ren'Py or the project configuration changes."
    new "No. Potrebbe essere necessario un aggiornamento manuale quando cambiano Ren'Py o la configurazione del progetto."

    # game/androidstrings.rpy:46
    old "Unknown configuration variable: {}"
    new "Variabile di configurazione sconosciuta: {}"

    # game/androidstrings.rpy:47
    old "I'm compiling a short test program, to see if you have a working JDK on your system."
    new "Sto compilando un piccolo programma di prova, per vedere se hai un JDK funzionante sul sistema."

    # game/androidstrings.rpy:48
    old "I was unable to use javac to compile a test file. If you haven't installed the Java Development Kit yet, please download it from:\n\n{a=https://adoptium.net/?variant=openjdk8}https://adoptium.net/?variant=openjdk8{/a}\n\nThe JDK is different from the JRE, so it's possible you have Java without having the JDK. Please make sure you installed the 'JavaSoft (Oracle) registry keys'.\n\nWithout a working JDK, I can't continue."
    new "Non ho potuto usare javac per compilare un file di prova. Se non hai ancora installato il Java Development Kit, scaricalo da:\n\n{a=https://adoptium.net/?variant=openjdk8}https://adoptium.net/?variant=openjdk8{/a}\n\nIl JDK è diverso dal JRE, quindi è possibile che tu abbia Java senza avere il JDK. Assicurati di aver installato le 'chiavi di registro JavaSoft (Oracle)'.\n\nSenza un JDK funzionante, non posso continuare."

    # game/androidstrings.rpy:49
    old "The version of Java on your computer does not appear to be JDK 8, which is the only version supported by the Android SDK. If you need to install JDK 8, you can download it from:\n\n{a=https://adoptium.net/?variant=openjdk8}https://adoptium.net/?variant=openjdk8{/a}\n\nYou can also set the JAVA_HOME environment variable to use a different version of Java."
    new "La versione di Java presente sul computer non sembra essere JDK 8, che è l'unica versione supportata dall'SDK di Android. Se devi installare JDK 8, puoi scaricarlo da:\n\n{a=https://adoptium.net/?variant=openjdk8}https://adoptium.net/?variant=openjdk8{/a}\n\nPuoi anche impostare la variabile d'ambiente JAVA_HOME per utilizzare una versione diversa di Java."

    # game/androidstrings.rpy:50
    old "The JDK is present and working. Good!"
    new "Il JDK è presente e funzionante. Bene!"

    # game/androidstrings.rpy:51
    old "The Android SDK has already been unpacked."
    new "L'SDK Android è già stato scompattato."

    # game/androidstrings.rpy:52
    old "Do you accept the Android SDK Terms and Conditions?"
    new "Accetti i Termini e le Condizioni dell'SDK di Android?"

    # game/androidstrings.rpy:53
    old "I'm downloading the Android SDK. This might take a while."
    new "Sto scaricando l'SDK di Android. Potrebbe volerci un po'."

    # game/androidstrings.rpy:54
    old "I'm extracting the Android SDK."
    new "Sto estraendo l'SDK di Android."

    # game/androidstrings.rpy:55
    old "I've finished unpacking the Android SDK."
    new "Ho finito di scompattare l'SDK di Android."

    # game/androidstrings.rpy:56
    old "I'm about to download and install the required Android packages. This might take a while."
    new "Sto per scaricare ed installare i pacchetti Android necessari. Potrebbe volerci un po'."

    # game/androidstrings.rpy:57
    old "I was unable to accept the Android licenses."
    new "Non ho potuto accettare le licenze di Android."

    # game/androidstrings.rpy:59
    old "I was unable to install the required Android packages."
    new "Non ho potuto  installare i pacchetti Android necessari."

    # game/androidstrings.rpy:60
    old "I've finished installing the required Android packages."
    new "Ho finito di installare i pacchetti Android necessari."

    # game/androidstrings.rpy:61
    old "It looks like you're ready to start packaging games."
    new "Sembra che sia tutto pronto per iniziare a confezionare giochi."

    # game/androidstrings.rpy:62
    old "Please enter your name or the name of your organization."
    new "Inserisci il tuo nome o quello della tua organizzazione."

    # game/androidstrings.rpy:63
    old "I found an android.keystore file in the rapt directory. Do you want to use this file?"
    new "Ho trovato un file android.keystore nella cartella rapt. Vuoi usare questo?"

    # game/androidstrings.rpy:64
    old "I can create an application signing key for you. This key is required to create Universal APK for sideloading and stores other than Google Play.\n\nDo you want to create a key?"
    new "Posso creare una chiave di firma dell'applicazione per te. Questa chiave è necessaria per creare APK universali per il sideloading e per gli store diversi da Google Play.\n\nVuoi creare una chiave?"

    # game/androidstrings.rpy:65
    old "I will create the key in the android.keystore file.\n\nYou need to back this file up. If you lose it, you will not be able to upgrade your application.\n\nYou also need to keep the key safe. If evil people get this file, they could make fake versions of your application, and potentially steal your users' data.\n\nWill you make a backup of android.keystore, and keep it in a safe place?"
    new "Creerò la chiave nel file android.keystore.\n\nDovrai eseguire un backup di questo file. Se lo si perde, non sarà possibile aggiornare l'applicazione.\n\nÈ inoltre necessario tenere la chiave al sicuro. Se persone malintenzionate ottengono questo file, potrebbero creare versioni false della tua applicazione e potenzialmente rubare i dati dei tuoi utenti.\n\nFarai un backup di android.keystore e lo conserverai al sicuro?"

    # game/androidstrings.rpy:66
    old "\n\nSaying 'No' will prevent key creation."
    new "\n\nDicendo 'No' si impedisce la creazione della chiave."

    # game/androidstrings.rpy:67
    old "Could not create android.keystore. Is keytool in your path?"
    new "Impossibile creare android.keystore. Il programma keytool è presente nel path di sistema?"

    # game/androidstrings.rpy:68
    old "I've finished creating android.keystore. Please back it up, and keep it in a safe place."
    new "Ho creato il file android.keystore. Eseguine il backup, e conservalo al sicuro."

    # game/androidstrings.rpy:69
    old "I found a bundle.keystore file in the rapt directory. Do you want to use this file?"
    new "Ho trovato un file bundle.keystore nella cartella rapt. Vuoi usarlo?"

    # game/androidstrings.rpy:70
    old "I can create a bundle signing key for you. This key is required to build an Android App Bundle (AAB) for upload to Google Play.\n\nDo you want to create a key?"
    new "Posso creare una chiave di firma del bundle per te. Questa è necessaria per creare un Bundle di Applicazioni Android (AAB) da caricare su Google Play.\n\nVuoi creare una chiave?"

    # game/androidstrings.rpy:71
    old "I will create the key in the bundle.keystore file.\n\nYou need to back this file up. If you lose it, you will not be able to upgrade your application.\n\nYou also need to keep the key safe. If evil people get this file, they could make fake versions of your application, and potentially steal your users' data.\n\nWill you make a backup of bundle.keystore, and keep it in a safe place?"
    new "Creerò la chiave nel file bundle.keystore.\n\nDovrai eseguire un backup di questo file. Se lo si perde, non sarà possibile aggiornare l'applicazione.\n\nÈ inoltre necessario tenere la chiave al sicuro. Se persone malintenzionate ottengono questo file, potrebbero creare versioni false della tua applicazione e potenzialmente rubare i dati dei tuoi utenti.\n\nFarai un backup di bundle.keystore e lo conserverai al sicuro?"

    # game/androidstrings.rpy:73
    old "Could not create bundle.keystore. Is keytool in your path?"
    new "Impossibile creare bundle.keystore. Il programma keytool è nel path di sistema?"

    # game/androidstrings.rpy:74
    old "I've opened the directory containing android.keystore and bundle.keystore. Please back them up, and keep them in a safe place."
    new "Ho aperto la cartella contenente android.keystore e bundle.keystore. Eseguine il backup e conservali al sicuro."

    # game/choose_directory.rpy:67
    old "Select Projects Directory"
    new "Selezionare la cartella dei progetti"

    # game/choose_theme.rpy:508
    old "changing the theme"
    new "cambio del tema"

    # game/distribute.rpy:1745
    old "Copying files..."
    new "Copia dei file..."

    # game/distribute_gui.rpy:157
    old "Build Distributions: [project.current.display_name!q]"
    new "Compila Distribuzioni: [project.current.display_name!q]"

    # game/distribute_gui.rpy:195
    old "Update old-game"
    new "Aggiornamento old-game"

    # game/distribute_gui.rpy:231
    old "(DLC)"
    new "(DLC)"

    # game/dmgcheck.rpy:50
    old "Ren'Py is running from a read only folder. Some functionality will not work."
    new "Ren'Py sta girando da una cartella di sola lettura. Alcune funzionalità non saranno disponibili."

    # game/dmgcheck.rpy:50
    old "This is probably because Ren'Py is running directly from a Macintosh drive image. To fix this, quit this launcher, copy the entire %s folder somewhere else on your computer, and run Ren'Py again."
    new "Ciò è probabilmente perché Ren'Py è stato eseguito direttamente da un'immagine di unità Macintosh. Per risolvere il problema, chiudi questo launcher, copia l'intera cartella %s in un altro punto del computer, ed eseguire nuovamente Ren'Py."

    # game/editor.rpy:152
    old "A modern editor with many extensions including advanced Ren'Py integration."
    new "Un editor moderno con molte estensioni, compresa un'integrazione avanzata con Ren'Py."

    # game/editor.rpy:153
    old "A modern editor with many extensions including advanced Ren'Py integration.\n{a=jump:reinstall_vscode}Upgrade Visual Studio Code to the latest version.{/a}"
    new "Un editor moderno con molte estensioni, compresa un'integrazione avanzata con Ren'Py.\n{a=jump:reinstall_vscode}Aggiornare Visual Studio Code alla versione più recente.{/a}"

    # game/editor.rpy:169
    old "Visual Studio Code"
    new "Visual Studio Code"

    # game/editor.rpy:169
    old "Up to 110 MB download required."
    new "È richiesto un download fino a 110 MB."

    # game/editor.rpy:182
    old "A modern and approachable text editor."
    new "Un editor di testo moderno e accessibile."

    # game/editor.rpy:196
    old "Atom"
    new "Atom"

    # game/editor.rpy:196
    old "Up to 150 MB download required."
    new "È richiesto un download fino a 150 MB."

    # game/editor.rpy:211
    old "jEdit"
    new "jEdit"

    # game/editor.rpy:220
    old "Visual Studio Code (System)"
    new "Visual Studio Code (Sistema)"

    # game/editor.rpy:220
    old "Uses a copy of Visual Studio Code that you have installed outside of Ren'Py. It's recommended you install the language-renpy extension to add support for Ren'Py files."
    new "Utilizza una copia di Visual Studio Code installata al di fuori di Ren'Py. Si consiglia di installare l'estensione language-renpy per aggiungere il supporto ai file di Ren'Py."

    # game/editor.rpy:226
    old "System Editor"
    new "Editor di Sistema"

    # game/editor.rpy:245
    old "None"
    new "Nessuno"

    # game/editor.rpy:352
    old "Edit [text]."
    new "Modifica [text]."

    # game/front_page.rpy:165
    old "audio"
    new "audio"

    # game/front_page.rpy:182
    old "Open project"
    new "Apri progetto"

    # game/front_page.rpy:188
    old "Actions"
    new "Azioni"

    # game/front_page.rpy:219
    old "Web"
    new "Web"

    # game/front_page.rpy:219
    old "(Beta)"
    new "(Beta)"

    # game/gui7.rpy:302
    old "{size=-4}\n\nThis will not overwrite gui/main_menu.png, gui/game_menu.png, and gui/window_icon.png, but will create files that do not exist.{/size}"
    new "{size=-4}\n\nNon saranno sovrascritti i file gui/main_menu.png, gui/game_menu.png e gui/window_icon.png, ma saranno creati i file che non esistono.{/size}"

    # game/gui7.rpy:333
    old "Custom. The GUI is optimized for a 16:9 aspect ratio."
    new "Personalizzato. L'interfaccia grafica è ottimizzata per un rapporto d'aspetto 16:9."

    # game/gui7.rpy:350
    old "WIDTH"
    new "LARGHEZZA"

    # game/gui7.rpy:350
    old "Please enter the width of your game, in pixels."
    new "Inserisci la larghezza del gioco, in pixel."

    # game/gui7.rpy:360
    old "The width must be a number."
    new "La larghezza deve essere un numero."

    # game/gui7.rpy:366
    old "HEIGHT"
    new "ALTEZZA"

    # game/gui7.rpy:366
    old "Please enter the height of your game, in pixels."
    new "Inserisci l'altezza del gioco, in pixel."

    # game/gui7.rpy:376
    old "The height must be a number."
    new "L'altezza deve essere un numero."

    # game/gui7.rpy:424
    old "creating a new project"
    new "creazione di un nuovo progetto"

    # game/gui7.rpy:428
    old "activating the new project"
    new "attivazione del nuovo progetto"

    # game/install.rpy:33
    old "Could not install [name!t], as a file matching [zipglob] was not found in the Ren'Py SDK directory."
    new "Impossibile installare [name!t], poiché non è stato trovato un file corrispondente a [zipglob] nella cartella dell'SDK di Ren'Py."

    # game/install.rpy:79
    old "Successfully installed [name!t]."
    new "[name!t] installato con successo."

    # game/install.rpy:111
    old "This screen allows you to install libraries that can't be distributed with Ren'Py. Some of these libraries may require you to agree to a third-party license before being used or distributed."
    new "Questa schermata ti permette di installare le librerie che non possono essere distribuite con Ren'Py. Alcune di queste possono richiedere l'accettazione di una licenza di terze parti prima di essere utilizzate o distribuite."

    # game/install.rpy:117
    old "Install Steam Support"
    new "Installa il Supporto a Steam"

    # game/install.rpy:126
    old "Before installing Steam support, please make sure you are a {a=https://partner.steamgames.com/}Steam partner{/a}."
    new "Prima di installare il supporto a Steam, assicurati di essere un partner di Steam {a=https://partner.steamgames.com/}{/a}."

    # game/install.rpy:138
    old "Steam support has already been installed."
    new "Il supporto a Steam è già stato installato."

    # game/install.rpy:142
    old "Install Live2D Cubism SDK for Native"
    new "Installa l'SDK Live2D Cubism for Native"

    # game/install.rpy:156
    old "Install Libraries"
    new "Installa Librerie"

    # game/install.rpy:182
    old "The {a=https://www.live2d.com/en/download/cubism-sdk/download-native/}Cubism SDK for Native{/a} adds support for displaying Live2D models. Place CubismSdkForNative-4-{i}version{/i}.zip in the Ren'Py SDK directory, and then click Install. Distributing a game with Live2D requires you to accept a license from Live2D, Inc."
    new "Il {a=https://www.live2d.com/en/download/cubism-sdk/download-native/}Cubism SDK for Native{/a} aggiunge il supporto per la visualizzazione di modelli Live2D. Posiziona CubismSdkForNative-4-{i}version{/i}.zip nella cartella dell'SDK di Ren'Py, quindi clicca Installa. La distribuzione di un gioco con Live2D richiede l'accettazione di una licenza da parte di Live2D, Inc."

    # game/install.rpy:186
    old "Live2D in Ren'Py doesn't support the Web, Android x86_64 (including emulators and Chrome OS), and must be added to iOS projects manually. Live2D must be reinstalled after upgrading Ren'Py or installing Android support."
    new "Live2D in Ren'Py non supporta il Web, Android x86_64 (compresi gli emulatori e Chrome OS), e deve essere aggiunto manualmente ai progetti iOS. Live2D deve essere reinstallato dopo l'aggiornamento di Ren'Py o l'installazione del supporto Android."

    # game/install.rpy:191
    old "Open Ren'Py SDK Directory"
    new "Aprire la Cartella dell'SDK di Ren'Py"

    # game/installer.rpy:10
    old "Downloading [extension.download_file]."
    new "Scaricamento di [extension.download_file]."

    # game/installer.rpy:11
    old "Could not download [extension.download_file] from [extension.download_url]:\n{b}[extension.download_error]"
    new "Impossibile scaricare [extension.download_file] da [extension.download_url]:\n{b}[extension.download_error]"

    # game/installer.rpy:12
    old "The downloaded file [extension.download_file] from [extension.download_url] is not correct."
    new "Il file [extension.download_file] scaricato da [extension.download_url] non è corretto."

    # game/interface.rpy:124
    old "[interface.version]"
    new "[interface.version]"

    # game/interface.rpy:141
    old "Ren'Py Sponsor Information"
    new "Informazioni sugli Sponsor di Ren'Py"

    # game/interface.rpy:385
    old "opening the log file"
    new "apertura del file di registro"

    # game/ios.rpy:269
    old "iOS: [project.current.display_name!q]"
    new "iOS: [project.current.display_name!q]"

    # game/ios.rpy:379
    old "There are known issues with the iOS simulator on Apple Silicon. Please test on x86_64 or iOS devices."
    new "Ci sono noti problemi con il simulatore iOS su Apple Silicon. Dovresti testare su x86_64 o dispositivi iOS."

    # game/itch.rpy:45
    old "Downloading the itch.io butler."
    new "Scaricamento dell'itch.io butler."

    # game/navigation.rpy:168
    old "Navigate: [project.current.display_name!q]"
    new "Navigazione: [project.current.display_name!q]"

    # game/new_project.rpy:81
    old "You will be creating an [new_project_language]{#this substitution may be localized} language project. Change the launcher language in preferences to create a project in another language."
    new "Verrà creato un progetto in [new_project_language]{#this substitution may be localized}. Cambia la lingua di lancio in preferenze per creare un progetto in un'altra lingua."

    # game/preferences.rpy:106
    old "General"
    new "Generale"

    # game/preferences.rpy:107
    old "Options"
    new "Opzioni"

    # game/preferences.rpy:258
    old "Prefer the web documentation"
    new "Preferire la documentazione web"

    # game/preferences.rpy:224
    old "Sponsor message"
    new "Messaggio dello sponsor"

    # game/preferences.rpy:262
    old "Restore window position"
    new "Ripristina posizione della finestra"

    # game/preferences.rpy:227
    old "Daily check for update"
    new "Controllo giornaliero degli aggiornamenti"

    # game/preferences.rpy:266
    old "Prefer RPU updates"
    new "Preferire aggiornamenti RPU"

    # game/preferences.rpy:246
    old "Launcher Theme:"
    new "Tema del Launcher:"

    # game/preferences.rpy:250
    old "Default theme"
    new "Tema predefinito"

    # game/preferences.rpy:251
    old "Dark theme"
    new "Tema scuro"

    # game/preferences.rpy:252
    old "Custom theme"
    new "Tema personalizzato"

    # game/preferences.rpy:256
    old "Information about creating a custom theme can be found {a=[skins_url]}in the Ren'Py Documentation{/a}."
    new "Le informazioni sulla creazione di un tema personalizzato sono disponibili all'indirizzo {a=[skins_url]}nella documentazione di Ren'Py{/a}."

    # game/preferences.rpy:273
    old "Install Libraries:"
    new "Installa Librerie:"

    # game/preferences.rpy:300
    old "Reset window size"
    new "Ripristina le dimensioni della finestra"

    # game/preferences.rpy:301
    old "Clean temporary files"
    new "Pulisci i file temporanei"

    # game/preferences.rpy:308
    old "Cleaning temporary files..."
    new "Pulizia dei file temporanei..."

    # game/preferences.rpy:338
    old "{#in language font}Welcome! Please choose a language"
    new "{#in language font}Salve! Per favore scegli una lingua"

    # game/preferences.rpy:373
    old "{#in language font}Start using Ren'Py in [lang_name]"
    new "{#in language font}Inizia ad usare Ren'Py in [lang_name]"

    # game/preferences.rpy:234
    old "Game Options:"
    new "Opzioni di Gioco"

    # game/preferences.rpy:241
    old "Skip splashscreen"
    new "Salta schermata di caricamento"

    # game/preferences.rpy:364
    old "Lint toggles:"
    new "Opzioni lint:"

    # game/preferences.rpy:368
    old "Check for orphan/obsolete translations"
    new "Controlla la presenza di traduzioni orfane/obsolete"

    # game/preferences.rpy:371
    old "Check parameters shadowing reserved names"
    new "Controlla la presenza di parametri che oscurano nomi riservati"

    # game/preferences.rpy:374
    old "Print block, word, and character counts by speaking character."
    new "Riporta il numero di blocchi, parole, e caratteri per personaggio parlante"

    # game/preferences.rpy:377
    old "Unclosed text tags"
    new "Frammenti di testo non chiusi"

    # game/preferences.rpy:380
    old "Show all unreachable blocks and orphaned translations."
    new "Mostra tutti i blocchi irraggiungibili e le traduzioni orfane"

    # game/project.rpy:46
    old "Lint checks your game for potential mistakes, and gives you statistics."
    new "Lint controlla la presenza nel gioco di potenziali errori e fornisce statistiche."

    # game/project.rpy:283
    old "This may be because the project is not writeable."
    new "Ciò può essere dovuto al fatto che il progetto non è scrivibile."

    # game/translations.rpy:91
    old "Translations: [project.current.display_name!q]"
    new "Traduzioni: [project.current.display_name!q]"

    # game/translations.rpy:342
    old "Extract Dialogue: [project.current.display_name!q]"
    new "Estrazione Dialogo: [project.current.display_name!q]"

    # game/translations.rpy:391
    old "Language (or None for the default language):"
    new "Lingua (o None per la lingua predefinita):"

    # game/updater.rpy:64
    old "Release (Ren'Py 8, Python 3)"
    new "Release (Ren'Py 8, Python 3)"

    # game/updater.rpy:65
    old "Release (Ren'Py 7, Python 2)"
    new "Release (Ren'Py 7, Python 2)"

    # game/updater.rpy:69
    old "Prerelease (Ren'Py 8, Python 3)"
    new "Prerelease (Ren'Py 8, Python 3)"

    # game/updater.rpy:70
    old "Prerelease (Ren'Py 7, Python 2)"
    new "Prerelease (Ren'Py 7, Python 2)"

    # game/updater.rpy:77
    old "Nightly (Ren'Py 8, Python 3)"
    new "Nightly (Ren'Py 8, Python 3)"

    # game/updater.rpy:78
    old "Nightly (Ren'Py 7, Python 2)"
    new "Nightly (Ren'Py 7, Python 2)"

    # game/updater.rpy:108
    old "The update channel controls the version of Ren'Py the updater will download."
    new "Il canale di aggiornamento controlla la versione di Ren'Py che il programma di aggiornamento scaricherà."

    # game/updater.rpy:116
    old "• {a=https://www.renpy.org/doc/html/changelog.html}View change log{/a}"
    new "• {a=https://www.renpy.org/doc/html/changelog.html}Visualizza il registro delle modifiche{/a}"

    # game/updater.rpy:118
    old "• {a=https://www.renpy.org/dev-doc/html/changelog.html}View change log{/a}"
    new "• {a=https://www.renpy.org/dev-doc/html/changelog.html}Visualizza il registro delle modifiche{/a}"

    # game/updater.rpy:124
    old "• This version is installed and up-to-date."
    new "• Questa versione è installata e aggiornata."

    # game/updater.rpy:136
    old "%B %d, %Y"
    new "%B %d, %Y"

    # game/updater.rpy:215
    old "Fetching the list of update channels"
    new "Recupero dell'elenco dei canali di aggiornamento"

    # game/updater.rpy:220
    old "downloading the list of update channels"
    new "scaricamento dell'elenco dei canali di aggiornamento"

    # game/web.rpy:428
    old "Preparing progressive download"
    new "Preparazione del download progressivo"

    # game/web.rpy:485
    old "Creating package..."
    new "Creazione pacchetto..."

    # game/web.rpy:505
    old "Web: [project.current.display_name!q]"
    new "Web: [project.current.display_name!q]"

    # game/web.rpy:535
    old "Build Web Application"
    new "Compila un'Applicazione Web"

    # game/web.rpy:536
    old "Build and Open in Browser"
    new "Compila e Apri nel Browser"

    # game/web.rpy:537
    old "Open in Browser"
    new "Apri nel Browser"

    # game/web.rpy:538
    old "Open build directory"
    new "Apri la cartella di compilazione"

    # game/web.rpy:560
    old "Images and music can be downloaded while playing. A 'progressive_download.txt' file will be created so you can configure this behavior."
    new "Le immagini e la musica possono essere scaricate mentre si gioca. Verrà creato un file \"progressive_download.txt\" per configurare questo comportamento."

    # game/web.rpy:568
    old "Before packaging web apps, you'll need to download RenPyWeb, Ren'Py's web support. Would you like to download RenPyWeb now?"
    new "Prima di pacchettizzare le applicazioni web, è necessario scaricare RenPyWeb, il supporto web di Ren'Py. Vuoi scaricare RenPyWeb ora?"

    # game/updater.rpy:79
    old "A nightly build of fixes to the release version of Ren'Py."
    new "Una build nightly con correzioni alla versione di rilascio di Ren'Py."

