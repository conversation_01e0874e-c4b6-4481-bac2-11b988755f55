
translate indonesian strings:

    # gui.rpy:2
    old "## Initialization"
    new "## Inisialisasi"

    # gui.rpy:5
    old "## The init offset statement causes the init code in this file to run before init code in any other file."
    new "## Pernyataan offset init menyebabkan kode init di file ini berjalan sebelum kode init di dalam file lainnya."

    # gui.rpy:9
    old "## Calling gui.init resets the styles to sensible default values, and sets the width and height of the game."
    new "## Memanggil gui.init. mereset gaya ke value bawaan, dan menset lebar dan tinggi dari permainan."

    # gui.rpy:21
    old "## Colors"
    new "## Warna"

    # gui.rpy:23
    old "## The colors of text in the interface."
    new "## Warna text pada antarmuka."

    # gui.rpy:25
    old "## An accent color used throughout the interface to label and highlight text."
    new "## Warna aksen yang digunakan sepanjang interface sampai pewarnaan text."

    # gui.rpy:29
    old "## The color used for a text button when it is neither selected nor hovered."
    new "## Warna yang di gunakan untuk warna tombol text jika di pilih atau di tekan."

    # gui.rpy:32
    old "## The small color is used for small text, which needs to be brighter/darker to achieve the same effect."
    new "## Warna kecil yang di gunakan untuk text kecil, yang membutuhkan lebih terang/lebih gelap untuk mencapai efek yang sama"

    # gui.rpy:36
    old "## The color that is used for buttons and bars that are hovered."
    new "## Warna yang di gunakan untuk tombol dan bar yang di pilih."

    # gui.rpy:39
    old "## The color used for a text button when it is selected but not focused. A button is selected if it is the current screen or preference value."
    new "## Warna yang digunakan untuk text tombol ketika di pijit tapi tidak di fokus. Tombol di pilih jika terdapat di layar saat ini atau value preferensi."

    # gui.rpy:43
    old "## The color used for a text button when it cannot be selected."
    new "## Warna yang di gunakan untuk tombol text ketika tidak bisa di pilih."

    # gui.rpy:46
    old "## Colors used for the portions of bars that are not filled in. These are not used directly, but are used when re-generating bar image files."
    new "## Warna yang di gunakan untuk beberapa bagian dari bar yang tidak terisi. Ini tidak di gunakan secara langsung, Tapi di gunakan ketika me regenerasi file gambar bar."

    # gui.rpy:51
    old "## The colors used for dialogue and menu choice text."
    new "## Warna yang di gunakan untuk dialog dan text pilihan menu."

    # gui.rpy:56
    old "## Fonts and Font Sizes"
    new "## Font dan ukuran Font"

    # gui.rpy:58
    old "## The font used for in-game text."
    new "## Font yang digunakan untuk text in-game."

    # gui.rpy:61
    old "## The font used for character names."
    new "## Font yang di gunakan untuk nama karakter."

    # gui.rpy:64
    old "## The font used for out-of-game text."
    new "## Font yang digunakan untuk text di luar permainan."

    # gui.rpy:67
    old "## The size of normal dialogue text."
    new "## Ukuran normal dialog text."

    # gui.rpy:70
    old "## The size of character names."
    new "## Ukuran dari nama karakter."

    # gui.rpy:73
    old "## The size of text in the game's user interface."
    new "## Ukuran text antarmuka permainan."

    # gui.rpy:76
    old "## The size of labels in the game's user interface."
    new "## Ukuran label di antarmuka permainan."

    # gui.rpy:79
    old "## The size of text on the notify screen."
    new "## Ukuran dari text di layar notifikasi."

    # gui.rpy:82
    old "## The size of the game's title."
    new "## Ukuran judul permainan."

    # gui.rpy:86
    old "## Main and Game Menus"
    new "## Menu utama dan Menu permainan."

    # gui.rpy:88
    old "## The images used for the main and game menus."
    new "## Gambar yang di gunakan untuk Menu utama dan Menu permainan."

    # gui.rpy:92
    old "## Should we show the name and version of the game?"
    new "## Haruskah kita menunjukkan nama dan versi permainan game?"

    # gui.rpy:96
    old "## Dialogue"
    new "## Dialog"

    # gui.rpy:98
    old "## These variables control how dialogue is displayed on the screen one line at a time."
    new "## Variabel ini mengendalikan bagaimana dialog di tampilkan pada layar pada satu waktu."

    # gui.rpy:101
    old "## The height of the textbox containing dialogue."
    new "## Tinggi textbox yang berisi dialog."

    # gui.rpy:104
    old "## The placement of the textbox vertically on the screen. 0.0 is the top, 0.5 is center, and 1.0 is the bottom."
    new "## Penempatan texbox secara vertikal pada layar. 0.0 adalah atas, 0.5 adalah tengah, dan 1.0 adalah bawah."

    # gui.rpy:109
    old "## The placement of the speaking character's name, relative to the textbox. These can be a whole number of pixels from the left or top, or 0.5 to center."
    new "## Penempatan nama karakter yang berbicara, hampir sama dengan kotak text. "

    # gui.rpy:114
    old "## The horizontal alignment of the character's name. This can be 0.0 for left-aligned, 0.5 for centered, and 1.0 for right-aligned."
    new "## Penempatan  horizontal nama karakter. Ini dapat berupa 0.0 untuk rata kiri, 0.5 untuk rata tengah, dan 1.0 untuk rata kanan. "

    # gui.rpy:118
    old "## The width, height, and borders of the box containing the character's name, or None to automatically size it."
    new "## Lebar, panjang, dan tepi dari kotak berisi nama karakter, Atau None untuk secara otomatis mengukur nya."

    # gui.rpy:123
    old "## The borders of the box containing the character's name, in left, top, right, bottom order."
    new "## Tepi kotak bersisi urutan nama karakter, di kiri, atas, kanan, bawah."

    # gui.rpy:127
    old "## If True, the background of the namebox will be tiled, if False, the background of the namebox will be scaled."
    new "## Jika Benar, latar dari kotaknama akan di beri judul, jika Salah, latar dari kotaknama akan di ukur ulang."

    # gui.rpy:132
    old "## The placement of dialogue relative to the textbox. These can be a whole number of pixels relative to the left or top side of the textbox, or 0.5 to center."
    new "## Penempatan dialog itu relatif pada kotaktext. Ini dapat berisi angka dari pixel yang relativ mulai dari sisi kiri sampai atas dari kotaknama, atau 0.5 untuk tengah."

    # gui.rpy:138
    old "## The maximum width of dialogue text, in pixels."
    new "## Lebar maximum dari dialog text, dalam pixel."

    # gui.rpy:141
    old "## The horizontal alignment of the dialogue text. This can be 0.0 for left-aligned, 0.5 for centered, and 1.0 for right-aligned."
    new "## rata tengah dari text dialog. Ini dapat berisi 0.0 untuk rata kiri, atau 0.5 untuk tengah, dan 1.0 untuk kanan."

    # gui.rpy:146
    old "## Buttons"
    new "## Tombol"

    # gui.rpy:148
    old "## These variables, along with the image files in gui/button, control aspects of how buttons are displayed."
    # Automatic translation.
    new "## Variabel-variabel ini, bersama dengan file gambar di gui/button, mengontrol aspek-aspek bagaimana tombol ditampilkan."

    # gui.rpy:151
    old "## The width and height of a button, in pixels. If None, Ren'Py computes a size."
    # Automatic translation.
    new "## Lebar dan tinggi tombol, dalam piksel. Jika tidak ada, Ren'Py akan menghitung ukuran."

    # gui.rpy:155
    old "## The borders on each side of the button, in left, top, right, bottom order."
    # Automatic translation.
    new "## Batas di setiap sisi tombol, dalam urutan kiri, atas, kanan, bawah."

    # gui.rpy:158
    old "## If True, the background image will be tiled. If False, the background image will be linearly scaled."
    # Automatic translation.
    new "## Jika Benar, gambar latar belakang akan dibuat ubin. Jika Salah, gambar latar belakang akan diskalakan secara linier."

    # gui.rpy:162
    old "## The font used by the button."
    # Automatic translation.
    new "## Font yang digunakan oleh tombol."

    # gui.rpy:165
    old "## The size of the text used by the button."
    # Automatic translation.
    new "## Ukuran teks yang digunakan oleh tombol."

    # gui.rpy:179
    old "## These variables override settings for different kinds of buttons. Please see the gui documentation for the kinds of buttons available, and what each is used for."
    # Automatic translation.
    new "## Variabel-variabel ini mengganti pengaturan untuk berbagai jenis tombol. Silakan lihat dokumentasi gui untuk mengetahui jenis tombol yang tersedia, dan untuk apa tombol tersebut digunakan."

    # gui.rpy:183
    old "## These customizations are used by the default interface:"
    # Automatic translation.
    new "## Kustomisasi ini digunakan oleh antarmuka default:"

    # gui.rpy:198
    old "## You can also add your own customizations, by adding properly-named variables. For example, you can uncomment the following line to set the width of a navigation button."
    # Automatic translation.
    new "## Anda juga dapat menambahkan kustomisasi Anda sendiri, dengan menambahkan variabel yang diberi nama yang tepat. Sebagai contoh, Anda dapat menghapus baris berikut ini untuk mengatur lebar tombol navigasi."

    # gui.rpy:205
    old "## Choice Buttons"
    # Automatic translation.
    new "## Tombol Pilihan"

    # gui.rpy:220
    old "## File Slot Buttons"
    # Automatic translation.
    new "## Tombol Slot File"

    # gui.rpy:222
    old "## A file slot button is a special kind of button. It contains a thumbnail image, and text describing the contents of the save slot. A save slot uses image files in gui/button, like the other kinds of buttons."
    # Automatic translation.
    new "## Tombol slot file adalah jenis tombol khusus. Tombol ini berisi gambar mini, dan teks yang menjelaskan isi slot penyimpanan. Slot penyimpanan menggunakan file gambar dalam gui/tombol, seperti jenis tombol lainnya."

    # gui.rpy:226
    old "## The save slot button."
    # Automatic translation.
    new "## Tombol simpan slot."

    # gui.rpy:234
    old "## The width and height of thumbnails used by the save slots."
    # Automatic translation.
    new "## Lebar dan tinggi gambar mini yang digunakan oleh slot penyimpanan."

    # gui.rpy:238
    old "## The number of columns and rows in the grid of save slots."
    # Automatic translation.
    new "## Jumlah kolom dan baris dalam kisi-kisi slot penyimpanan."

    # gui.rpy:243
    old "## Positioning and Spacing"
    # Automatic translation.
    new "## Pemosisian dan Spasi"

    # gui.rpy:245
    old "## These variables control the positioning and spacing of various user interface elements."
    # Automatic translation.
    new "## Variabel-variabel ini mengontrol pemosisian dan jarak berbagai elemen antarmuka pengguna."

    # gui.rpy:248
    old "## The position of the left side of the navigation buttons, relative to the left side of the screen."
    # Automatic translation.
    new "## Posisi sisi kiri tombol navigasi, relatif terhadap sisi kiri layar."

    # gui.rpy:252
    old "## The vertical position of the skip indicator."
    # Automatic translation.
    new "## Posisi vertikal indikator lompatan."

    # gui.rpy:255
    old "## The vertical position of the notify screen."
    # Automatic translation.
    new "## Posisi vertikal layar notifikasi."

    # gui.rpy:258
    old "## The spacing between menu choices."
    # Automatic translation.
    new "## Jarak spasi di antara pilihan menu."

    # gui.rpy:264
    old "## Controls the amount of spacing between preferences."
    # Automatic translation.
    new "## Mengontrol jumlah spasi di antara preferensi."

    # gui.rpy:267
    old "## Controls the amount of spacing between preference buttons."
    # Automatic translation.
    new "## Mengontrol jumlah spasi di antara tombol preferensi."

    # gui.rpy:270
    old "## The spacing between file page buttons."
    # Automatic translation.
    new "## Jarak antara tombol halaman file."

    # gui.rpy:273
    old "## The spacing between file slots."
    # Automatic translation.
    new "## Jarak antara slot file."

    # gui.rpy:277
    old "## Frames"
    # Automatic translation.
    new "## Frame"

    # gui.rpy:279
    old "## These variables control the look of frames that can contain user interface components when an overlay or window is not present."
    # Automatic translation.
    new "## Variabel ini mengontrol tampilan frame yang dapat berisi komponen antarmuka pengguna ketika overlay atau jendela tidak ada."

    # gui.rpy:282
    old "## Generic frames that are introduced by player code."
    # Automatic translation.
    new "## Bingkai umum yang diperkenalkan oleh kode pemutar."

    # gui.rpy:285
    old "## The frame that is used as part of the confirm screen."
    # Automatic translation.
    new "## Bingkai yang digunakan sebagai bagian dari layar konfirmasi."

    # gui.rpy:288
    old "## The frame that is used as part of the skip screen."
    # Automatic translation.
    new "## Bingkai yang digunakan sebagai bagian dari layar lewati."

    # gui.rpy:291
    old "## The frame that is used as part of the notify screen."
    # Automatic translation.
    new "## Bingkai yang digunakan sebagai bagian dari layar pemberitahuan."

    # gui.rpy:294
    old "## Should frame backgrounds be tiled?"
    # Automatic translation.
    new "## Haruskah latar belakang bingkai dibuat berubin?"

    # gui.rpy:298
    old "## Bars, Scrollbars, and Sliders"
    # Automatic translation.
    new "## Bilah, Bilah Gulir, dan Geser"

    # gui.rpy:300
    old "## These control the look and size of bars, scrollbars, and sliders."
    # Automatic translation.
    new "## Ini mengontrol tampilan dan ukuran bilah, bilah gulir, dan penggeser."

    # gui.rpy:302
    old "## The default GUI only uses sliders and vertical scrollbars. All of the other bars are only used in creator-written code."
    # Automatic translation.
    new "## GUI default hanya menggunakan slider dan scrollbar vertikal. Semua bilah lainnya hanya digunakan dalam kode yang ditulis oleh pembuat."

    # gui.rpy:305
    old "## The height of horizontal bars, scrollbars, and sliders. The width of vertical bars, scrollbars, and sliders."
    # Automatic translation.
    new "## Ketinggian bilah horizontal, bilah gulir, dan penggeser. Lebar bilah vertikal, bilah gulir, dan penggeser."

    # gui.rpy:311
    old "## True if bar images should be tiled. False if they should be linearly scaled."
    # Automatic translation.
    new "## Benar jika gambar batang harus diubin. Salah jika gambar harus diskalakan secara linier."

    # gui.rpy:316
    old "## Horizontal borders."
    # Automatic translation.
    new "## Batas horizontal."

    # gui.rpy:321
    old "## Vertical borders."
    # Automatic translation.
    new "## Batas vertikal."

    # gui.rpy:326
    old "## What to do with unscrollable scrollbars in the gui. \"hide\" hides them, while None shows them."
    # Automatic translation.
    new "## Apa yang harus dilakukan dengan scrollbar yang tidak dapat digulir di gui. \"hide\" menyembunyikannya, sedangkan None tidak menampilkannya."

    # gui.rpy:331
    old "## History"
    # Automatic translation.
    new "## Sejarah"

    # gui.rpy:333
    old "## The history screen displays dialogue that the player has already dismissed."
    # Automatic translation.
    new "## Layar riwayat menampilkan dialog yang telah diberhentikan oleh pemain."

    # gui.rpy:335
    old "## The number of blocks of dialogue history Ren'Py will keep."
    # Automatic translation.
    new "## Jumlah blok riwayat dialog yang akan disimpan Ren'Py."

    # gui.rpy:338
    old "## The height of a history screen entry, or None to make the height variable at the cost of performance."
    # Automatic translation.
    new "## Ketinggian entri layar riwayat, atau Tidak Ada untuk membuat variabel ketinggian dengan mengorbankan kinerja."

    # gui.rpy:342
    old "## The position, width, and alignment of the label giving the name of the speaking character."
    # Automatic translation.
    new "## Posisi, lebar, dan perataan label yang memberikan nama karakter yang berbicara."

    # gui.rpy:349
    old "## The position, width, and alignment of the dialogue text."
    # Automatic translation.
    new "## Posisi, lebar, dan perataan teks dialog."

    # gui.rpy:356
    old "## NVL-Mode"
    new "## NVL-Mode"

    # gui.rpy:358
    old "## The NVL-mode screen displays the dialogue spoken by NVL-mode characters."
    # Automatic translation.
    new "## Layar mode NVL menampilkan dialog yang diucapkan oleh karakter mode NVL."

    # gui.rpy:360
    old "## The borders of the background of the NVL-mode background window."
    # Automatic translation.
    new "## Batas latar belakang jendela latar belakang mode NVL."

    # gui.rpy:363
    old "## The height of an NVL-mode entry. Set this to None to have the entries dynamically adjust height."
    # Automatic translation.
    new "## Ketinggian entri mode NVL. Atur ini ke None (Tidak Ada) agar entri menyesuaikan tinggi secara dinamis."

    # gui.rpy:367
    old "## The spacing between NVL-mode entries when gui.nvl_height is None, and between NVL-mode entries and an NVL-mode menu."
    # Automatic translation.
    new "## Spasi antara entri mode NVL ketika gui.nvl_height adalah None, dan antara entri mode NVL dan menu mode NVL."

    # gui.rpy:384
    old "## The position, width, and alignment of nvl_thought text (the text said by the nvl_narrator character.)"
    # Automatic translation.
    new "## Posisi, lebar, dan perataan teks nvl_thought (teks yang diucapkan oleh karakter nvl_narrator)."

    # gui.rpy:391
    old "## The position of nvl menu_buttons."
    # Automatic translation.
    new "## Posisi tombol menu nvl."

    # gui.rpy:403
    old "## This increases the size of the quick buttons to make them easier to touch on tablets and phones."
    # Automatic translation.
    new "## Ini meningkatkan ukuran tombol cepat agar lebih mudah disentuh pada tablet dan ponsel."

    # gui.rpy:409
    old "## This changes the size and spacing of various GUI elements to ensure they are easily visible on phones."
    # Automatic translation.
    new "## Ini mengubah ukuran dan spasi berbagai elemen GUI untuk memastikan elemen-elemen tersebut mudah terlihat pada ponsel."

    # gui.rpy:421
    old "## Adjust the location of the textbox."
    # Automatic translation.
    new "## Sesuaikan lokasi kotak teks."

    # gui.rpy:427
    old "## Change the size and spacing of items in the game menu."
    # Automatic translation.
    new "## Mengubah ukuran dan spasi item dalam menu permainan."

    # gui.rpy:436
    old "## File button layout."
    # Automatic translation.
    new "## Tata letak tombol file."

    # gui.rpy:440
    old "## NVL-mode."
    # Automatic translation.
    new "## Mode NVL."

    # gui.rpy:456
    old "## Quick buttons."
    # Automatic translation.
    new "## Tombol cepat."

    # gui.rpy:17
    old "## GUI Configuration Variables"
    new "## Variabel konfigurasi GUI"

    # gui.rpy:168
    old "## The color of button text in various states."
    new "## Warna tombol text di berbagai kondisi."

    # gui.rpy:174
    old "## The horizontal alignment of the button text. (0.0 is left, 0.5 is center, 1.0 is right)."
    new "## Alignment horisontal tombol text. (0.0 itu kiri, 0.5 tengah, 1.0 kanan)."

    # gui.rpy:276
    old "## The position of the main menu text."
    new "## posisi text menu utama."

    # gui.rpy:398
    old "## Localization"
    new "## Lokalisasi"

    # gui.rpy:400
    old "## This controls where a line break is permitted. The default is suitable for most languages. A list of available values can be found at https://www.renpy.org/doc/html/style_properties.html#style-property-language"
    new "## Ini mengendalikan dimana line break di ijinkan. Pengaturan bawaan sudah cocok untuk kebanyakan bahasa. Daftar value yang tersedia dapat di lihat di https://www.renpy.org/doc/html/style_properties.html#style-property-language"

    # gui.rpy:408
    old "## Mobile devices"
    new "## Perangkat mobile"


translate indonesian strings:

    # gui.rpy:5
    old "## The init offset statement causes the initialization statements in this file to run before init statements in any other file."
    new "## pernyataan offset init menyebabkan pernyataan inisialisasi di file ini untuk berjalan lebih dahulu daripada pernyataan init di file lain nya."

    # gui.rpy:282
    old "## Generic frames."
    new "## Frame generic"

    # gui.rpy:302
    old "## The default GUI only uses sliders and vertical scrollbars. All of the other bars are only used in creator-written screens."
    new "## GUI Bawaan hanya menggunakan slider dan scrollbars vertikal. Bar lainnya hanya di gunakan pada layar GUI yang di tulis sendiri oleh pembuat/creator."

    # gui.rpy:434
    old "## Change the size and spacing of various things."
    new "## Ubah ukuran dan jarak dari berbagai hal."


translate indonesian strings:

    # gui/game/gui.rpy:14
    old "## Enable checks for invalid or unstable properties in screens or transforms"
    # Automatic translation.
    new "## Mengaktifkan pemeriksaan untuk properti yang tidak valid atau tidak stabil di layar atau transformasi"

    # gui/game/gui.rpy:368
    old "## The maximum number of NVL-mode entries Ren'Py will display. When more entries than this are to be show, the oldest entry will be removed."
    # Automatic translation.
    new "## Jumlah maksimum entri mode NVL yang akan ditampilkan Ren'Py. Ketika lebih banyak entri daripada ini akan ditampilkan, entri tertua akan dihapus."
