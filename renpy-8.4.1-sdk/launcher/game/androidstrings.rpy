
# This file contains strings used by RAPT, so the Ren'Py translation framework
# can find them. It's automatically generated by rapt/update_translations.py, and
# hence should not be changed by hand.

init python hide:
    __("{} is not a directory.")
    __("{} does not contain a Ren'Py game.")
    __("{} does not contain a Ren'Py game.")
    __("Run configure before attempting to build the app.")
    __("Updating project.")
    __("Creating assets directory.")
    __("Packaging internal data.")
    __("I'm using <PERSON>radle to build the package.")
    __("The build seems to have failed.")
    __("I'm installing the bundle.")
    __("Installing the bundle appears to have failed.")
    __("Launching app.")
    __("Launching the app appears to have failed.")
    __("The build seems to have succeeded.")
    __("What is the full name of your application? This name will appear in the list of installed applications.")
    __("What is the short name of your application? This name will be used in the launcher, and for application shortcuts.")
    __("What is the name of the package?\n\nThis is usually of the form com.domain.program or com.domain.email.program. It may only contain ASCII letters and dots. It must contain at least one dot.")
    __("The package name may not be empty.")
    __("The package name may not contain spaces.")
    __("The package name must contain at least one dot.")
    __("The package name may not contain two dots in a row, or begin or end with a dot.")
    __("Each part of the package name must start with a letter, and contain only letters, numbers, and underscores.")
    __("{} is a Java keyword, and can't be used as part of a package name.")
    __("How much RAM (in GB) do you want to allocate to Gradle?\nThis must be a positive integer number.")
    __("The RAM size must contain only numbers and be positive.")
    __("How would you like your application to be displayed?")
    __("In landscape orientation.")
    __("In portrait orientation.")
    __("In the user's preferred orientation.")
    __("Which app store would you like to support in-app purchasing through?")
    __("Google Play.")
    __("Amazon App Store.")
    __("Both, in one app.")
    __("Neither.")
    __("Do you want to automatically update the Java source code?")
    __("Yes. This is the best choice for most projects.")
    __("No. This may require manual updates when Ren'Py or the project configuration changes.")
    __("Unknown configuration variable: {}")
    __("I'm compiling a short test program, to see if you have a working JDK on your system.")
    __("I was unable to use javac to compile a test file. If you haven't installed the Java Development Kit yet, please download it from:\n\n{a=https://adoptium.net}https://adoptium.net/{/a}\n\nThe JDK is different from the JRE, so it's possible you have Java without having the JDK. Please install JDK [JDK_REQUIREMENT], and add it to your PATH.\n\nWithout a working JDK, I can't continue.")
    __("The version of Java on your computer does not appear to be JDK [JDK_REQUIREMENT], which is required to build Android apps. If you need to install a newer JDK, you can download it from:\n\n{a=https://adoptium.net/}https://adoptium.net/{/a}, and add it to your PATH.\n\nYou can also set the JAVA_HOME environment variable to use a different version of Java.")
    __("The JDK is present and working. Good!")
    __("The Android SDK has already been unpacked.")
    __("Do you accept the Android SDK Terms and Conditions?")
    __("I'm downloading the Android SDK. This might take a while.")
    __("I'm extracting the Android SDK.")
    __("I've finished unpacking the Android SDK.")
    __("I'm about to download and install the required Android packages. This might take a while.")
    __("I was unable to accept the Android licenses.")
    __("I was unable to accept the Android licenses.")
    __("I was unable to install the required Android packages.")
    __("I've finished installing the required Android packages.")
    __("It looks like you're ready to start packaging games.")
    __("Please enter your name or the name of your organization.")
    __("I found an android.keystore file in the rapt directory. Do you want to use this file?")
    __("I will create the key in the android.keystore file.\n\nYou need to back this file up. If you lose it, you will not be able to upgrade your application.\n\nYou also need to keep the key safe. If evil people get this file, they could make fake versions of your application, and potentially steal your users' data.\n\nWill you make a backup of android.keystore, and keep it in a safe place?")
    __("\n\nSaying 'No' will prevent key creation.")
    __("Could not create android.keystore. Is keytool in your path?")
    __("I've finished creating android.keystore. Please back it up, and keep it in a safe place.")
    __("I found a bundle.keystore file in the rapt directory. Do you want to use this file?")
    __("I've opened the directory containing android.keystore and bundle.keystore. Please back them up, and keep them in a safe place.")
