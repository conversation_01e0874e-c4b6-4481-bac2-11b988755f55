
import os, json
from os import environ #, path
from dotenv import load_dotenv

basedir = os.path.abspath(os.path.dirname(__file__))
load_dotenv(os.path.join(basedir, '.env'))

# Import config file
def conf():
    with open(os.path.join(basedir,'config.json'), 'r') as f:
        conf = json.load(f)
        #print("Config file loaded...")
    return conf

# Assign config data to local variable
appsettings = conf()

class AppConfig:
    DEBUG = environ.get('FLASK_DEBUG')
    SECRET_KEY = environ.get('SECRET_KEY') or os.urandom(12)
    # Read IPAM Credentials
    IPAM_USER = environ.get('IPAM_USER')
    IPAM_PASSWORD = environ.get('IPAM_PASSWORD')
    IPAM_APPID = environ.get('IPAM_APPID')
    # For domain access  
    CPS_USR = environ.get('CPS_USR')
    CPS_PWD = environ.get('CPS_PWD')  
    # For database connection
    DB_USER = environ.get('MARIADB_USER')
    DB_PWD = str(environ.get('MARIADB_PASSWORD'))
    #dbpwd = str(environ.get('MARIADB_PASSWORD'))
    #DB_PWD = dbpwd.encode('unicode-escape').decode('ascii')
    dbs = appsettings['MARIADB']['MARIADB_CPSDB']
    host = appsettings['MARIADB']['MARIADB_HOST']
    port = appsettings['MARIADB']['MARIADB_PORT']  
    # Read JIRA Credentials
    JIRA_USR = environ.get('JIRA_USR')
    JIRA_TKN = environ.get('JIRA_TKN')

    # Database
    SQLALCHEMY_DATABASE_URI = 'mariadb+mariadbconnector://{}:{}@{}:{}/{}'
    #SQLALCHEMY_DATABASE_URI = r"mariadb+mariadbconnector://"+str(DB_USER)+":"+repr(DB_PWD)+"@"+repr(host)+":"+str(port)+r"/"+str(dbs)
    SQLALCHEMY_DATABASE_URI = SQLALCHEMY_DATABASE_URI.format(DB_USER, DB_PWD, host, port, dbs)
        
    SQLALCHEMY_TRACK_MODIFICATIONS = False    