import sys, json, time, requests
import config
# Assign config data to local variable
config = config.conf()
from config import AppConfig

from src.utils import get_xlsx2df, get_xlsx2df_auth

# Import pandas
import pandas as pd
#import numpy as np

cps_user = AppConfig.CPS_USR
cps_pwrd = AppConfig.CPS_PWD

df_confdata = pd.DataFrame()
df_confmgmt = pd.DataFrame()
df_hosting = pd.DataFrame()
df_osconfig = pd.DataFrame()
df_hostingconf = pd.DataFrame()
df_osconf = pd.DataFrame()
job_hostconf = {}
job_osconf = {}
jobconf = {}
config_data = config['TFWEBSVC']['TFWEBSVC_HOSTING_CONFIG']
##print(config_data)

# Get Config Management Data
def GetConfigData(url):
    # Hosting Configs
    try:
        df_confmgmt = get_xlsx2df(url)
    except:
        df_confmgmt = get_xlsx2df_auth(url,cps_user,cps_pwrd)

    df_hosting = df_confmgmt['HostingConfig']                                   # Extract Hosting Configs from Config Mgmt Data
    ##print(df_hosting.info())
    df_hosting.columns = map(str.lower, df_hosting.columns)                     # Convert column names to lowercase
    df_hosting.columns = df_hosting.columns.str.replace(' ', '_')				# Replace space with underscore
    df_hosting.columns = df_hosting.columns.str.replace('(', '')				# Replace space with underscore
    df_hosting.columns = df_hosting.columns.str.replace(')', '')				# Replace space with underscore
    df_hosting['ip_cidr'] = df_hosting['ip_cidr'].str.replace(" ","")	        # Replace spaces in ip cidr
    df_hosting = df_hosting.dropna(subset=['tenant'])
    df_hosting = df_hosting.fillna("")
    ##print(df_hosting.info())

    # Windows OS Configs
    df_osconfig = df_confmgmt['WinOSConfig']                                       # Extract WinOS Configs from Config Mgmt Data
    ##print(df_osconfig.info())# Extract Hosting Configs from Config Mgmt Data
    df_osconfig.columns = map(str.lower, df_osconfig.columns)                     # Convert column names to lowercase
    df_osconfig.columns = df_osconfig.columns.str.replace(' ', '_')				# Replace space with underscore
    df_osconfig.columns = df_osconfig.columns.str.replace('(', '')				# Replace space with underscore
    df_osconfig.columns = df_osconfig.columns.str.replace(')', '')				# Replace space with underscore
    df_osconfig = df_osconfig.dropna(subset=['business_unit'])
    df_osconfig = df_osconfig.fillna("")
    ##print(df_osconfig.info())

    return df_hosting, df_osconfig        

capacity_data = config['TFWEBSVC']['TFWEBSVC_HOSTING_CAPACITY']
##print(capacity_data)

# Get Config Management Data
def GetCapacityData(url):
    # capacity Configs
    try:
        df_cpctmgmt = get_xlsx2df(url)
    except:
        df_cpctmgmt = get_xlsx2df_auth(url,cps_user,cps_pwrd)
    
    df_compcpct = df_cpctmgmt['Compute']                                         # Extract capacity Configs from Config Mgmt Data
    ##print(df_compcpct.info())
    df_compcpct.columns = map(str.lower, df_compcpct.columns)                     # Convert column names to lowercase

    ##print(df_compcpct.info())

    # Windows OS Configs
    df_storage = df_cpctmgmt['Storage']                                       # Extract WinOS Configs from Config Mgmt Data
    ##print(df_storage.info())# Extract capacity Configs from Config Mgmt Data
    df_storage.columns = map(str.lower, df_storage.columns)                     # Convert column names to lowercase

    ##print(df_storage.info())

    return df_compcpct, df_storage
    
df_hostingconfig, df_osconf = GetConfigData(config_data)

df_compute, df_storage = GetCapacityData(capacity_data)