import os,sys, json, time, requests
from requests_ntlm import HttpNtlmAuth
import config
# Assign config data to local variable
config = config.conf()
from config import AppConfig


cps_user = AppConfig.CPS_USR
cps_pwrd = AppConfig.CPS_PWD

# Import data from xlsx with Auth
def get_url_data(url,dstPath,user,passw):
    srcFname = url.split("/")[-1]
    dstFile = os.path.join(dstPath,srcFname)
    print("Auth Downloading ",url)    
    try:
        h = requests.get(url,auth=HttpNtlmAuth(user,passw), verify=False)
    except Exception as e:
        print(e)
    header = h.headers
    srcLength = header.get('content-length', None)
    open(dstFile, 'wb').write(h.content)

    print(f"\nSource data:  {url}", )
    print(f"Source size:    {srcLength}")
    print(f"Source file:    {srcFname}")
    print(f"Destination:    {dstFile}\n")
    return srcFname

url = "https://srv005879.mud.internal.co.za/AutoDeploy/dcstmplt_v1c.docx"
fname = get_url_data(url, "/data/deploytst", cps_user,cps_pwrd)
print(fname)
print("done!\n")
