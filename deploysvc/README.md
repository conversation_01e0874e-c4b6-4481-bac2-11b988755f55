# AutoDeploy
Automated deployment of virtual machines

## Changes History
2022-03-31 v0.4.6 Capacity Bugs and UpdateResources endpoint
2022-03-20 v0.4.5 Added JIRAUI Options endpoint
v0.4.4 Added bearer token auth, fixed database connection timeout, cleaned code
v0.4.3 Logging to database
v0.4.2 Added GetAllJobs API endpoint
v0.4.1 Bugfix OS Disk only datastore select
v0.4.0 Added NameGenerator feature
v0.3.2 VM_REDEPLOY option added
v0.3.1 Enhanced Capacity Refresh and DB Management
v0.3.0 Updated Testing (External Job management)
v0.2.0 Testing Release
v0.1.0 Initial proof of concept test

### Deploy as a linux service
0. Activate python virtual environment, install dependency modules.
''' python3 -m venv venv
''' source venv/bin/activate
''' pip3 install -r requirements.txt

1. Install uWSGI with pip
2. Create and configure uwsgi ini file - see included .ini example. PORT must not conflict.
3. Create and configure serviced service config file, see included .service example
4. Systemctl start service and enable to start up on reboot.

## Update
1. Refresh PIP requirements.txt
2. Apply PIP upgrades to packages
3. Configure database if necessary
4. Update configuration files
5. Update ConfigReference data
6. Restart services to apply changes.

CONFIG.JSON Format:
{
  "DEPLOYSVC" : {
      "TOKEN_EXPIRE_DAYS" : 366
  },
  "IPAM": {
      "IPAM_HOST":"url",
      "IPAM_PORT":"8000",
      "IPAM_API":"/api/"
  },
  "MARIADB": {
    "MARIADB_HOST":"host",
    "MARIADB_PORT":"port",
    "MARIADB_CPSDB": "db"
  },
  "CPSDB": {
    "MARIADB_HOST":"host",
    "MARIADB_PORT":"port",
    "MARIADB_CPSDB": "db"
  },
  "CPS": {
      "SVC_HOST":"0.0.0.0",
      "SVC_PORT":"5000",
      "CPS_INFRAVIEW":"url",
      "SVC_CONFMGMT":"url",
      "SVC_CAPACITY":"url",
      "SVC_QUALITY":"url"            
  },
  "NAMEGEN": {
    "FOR_TESTING": true,
    "APP_TYPE" : {
      "APPLICATION": "SR",
      "SHARED": "SR",
      "APPLIANCE": "SA",
      "SHELL": "SA",
      "POC": "POC",      
      "CLUSTER": "CLS",
      "VDI": "VDST"
    },
    "ENVIRONMENT" : {
      "POC": "POC",
      "LAB": "LAB"      
    },
    "CHASSIS" : {
      "PHYSICAL": "P",
      "VIRTUAL": "V"      
    },    
    "NAME_RANGE" : {
      "SEED" : "04",
      "PRODUCTION": "1001",
      "TESTING": "9001"
    }            
  },  
  "JIRA": {
    "ENABLED": false,
    "JIRA_HOST":"0.0.0.0",
    "JIRA_PORT":"port",
    "JIRA_API_URL" : "http://host:ort"
  },
  "SERVER"  : {
    "WORKFLOW" : {
      "FULFIL": 31,
      "FULFIL_MSG": "Request fulfilled.",
      "CLOSE": 41,
      "CLOSE_MSG" : "Request is now closed. Have a nice day :)"
    },
    "NAME_TYPE" : {
      "APPLICATION": "SR",
      "SHARED": "SR",
      "APPLIANCE": "SA",
      "SHELL": "SA",
      "MSSQL": "SR",
      "POC": "POC",      
      "CLUSTER": "CLS",
      "VDI": "VDST"
    },
    "FOR_TESTING" : true,
    "NON_SERVER" : ["CLS","POC","VDST"],
    "ENVIRONMENT" : {
      "POC": "POC",
      "LAB": "LAB"      
    },
    "CHASSIS" : {
      "PHYSICAL": "P",
      "VIRTUAL": "V"      
    },    
    "NAME_RANGE" : {
      "SEED" : "04",
      "PRODUCTION": "1001",
      "TESTING": "9001"
    }            
  }
}
