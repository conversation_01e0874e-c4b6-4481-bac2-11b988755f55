
import os

print("Importing database modules...")
# Import database modules
import sqlalchemy
from sqlalchemy import create_engine, inspect, text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy import Table, Column, Integer, String, Text, BLOB, Numeric, Date, ForeignKey, orm, DateTime, Date, Boolean
from sqlalchemy.ext.mutable import MutableList
from sqlalchemy.types import PickleType
from sqlalchemy.orm.session import sessionmaker
from sqlalchemy.schema import DropTable, DropConstraint
from collections import defaultdict

print("SQLAlchemy",sqlalchemy.__version__)

Base = declarative_base()

def mdbconnect(user, pwd, db, host, port):
    print("Establishing connection to database...")  
    
    url = 'mariadb+mariadbconnector://{}:{}@{}:{}/{}?charset=utf8mb4'
    url = url.format(user, pwd, host, port, db)

    # The return value of create_engine() is our connection object
    engine = create_engine(url)
    conn = engine.raw_connection()

    # Bind the connection to MetaData()
    meta = sqlalchemy.MetaData(bind=conn)
    #meta.reflect()

    Session = sessionmaker(bind=engine)
    session = Session()
 
    print("Database connected, Returning connection (conn) and a metadata (meta) object")

    return conn, meta, engine, session

def mdbsession(engine):
    Session = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    session = Session()
    return session

def mdbengine(user, pwd, db, host, port):
    #print("Establishing connection to database...")      
    url = 'mariadb+mariadbconnector://{}:{}@{}:{}/{}'
    url = url.format(user, pwd, host, port, db)
    # The return value of create_engine() is our connection object
    engine = create_engine(url) 
    #print("Created database engine object...")
    return engine

def init_db(meta,engine):
    print("Initializing Tables...")
    #import src.models
    meta.create_all(bind=engine)
    print("Tables initialized!")
    for table in meta.tables:
        print ("Table: ",table)    
    return

def df2sql(engine,data,table,action):
    # Receive data and upload to database
    print("Received dataframe...")
    #print(data)
    data.to_sql(name=table, con=engine, if_exists = action, index=False)
    print(action," to database complete!","\n")
    return

def df2conn(conn,data,table,action):
    # Receive data and upload to database
    print("Received dataframe...")
    #print(data)
    #conn = engine.raw_connection()
    data.to_sql(name=table, con=conn, if_exists = action, index=False)
    #conn.close()
    print(action," to database complete!","\n")
    return

def object_as_dict(obj):
    return {c.key: getattr(obj, c.key)
            for c in inspect(obj).mapper.column_attrs}

# How to use
# names, data = query_to_list(rset)
# df2 = pd.DataFrame.from_records(data, columns=names)
def query_to_list(rset):
    """List of result
    Return: columns name, list of result
    """
    result = []
    for obj in rset:
        instance = inspect(obj)
        items = instance.attrs.items()
        result.append([x.value for _,x in items])
    return instance.attrs.keys(), result

# How to use
## df = pd.DataFrame(query_to_dict(rset))
def query_to_dict(rset):
    result = defaultdict(list)
    for obj in rset:
        instance = inspect(obj)
        for key, x in instance.attrs.items():
            result[key].append(x.value)
    return result
