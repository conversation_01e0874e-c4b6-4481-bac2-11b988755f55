
import os, datetime
from os import path
import json

from flask import Flask, jsonify, abort, make_response, request
from flask_sqlalchemy import SQLAlchemy
#from flask_mail import Mail
from importlib import import_module
from config import appsettings, AppConfig
from flask_jwt_extended import JWTManager
from flask_bcrypt import Bcrypt

db = SQLAlchemy()
jwt = JWTManager()
bcrypt = Bcrypt()

def register_extensions(app):
    db.init_app(app)
    jwt.init_app(app)
    bcrypt.init_app(app)
    return

def create_app(config_class=AppConfig):
#def create_app():
    app = Flask(__name__)
    app.config.from_object(AppConfig)

    with app.app_context():
        register_extensions(app)
        from src.main.routes import main_bp
        app.register_blueprint(main_bp)

        return app