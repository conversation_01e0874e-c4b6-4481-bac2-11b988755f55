import json #,sys, time, requests
from datetime import date, time, datetime
from rich import print

import config
# Assign config data to local variable
config = config.conf()
from config import AppConfig

from src.utils import get_xlsx2df, get_xlsx2df_auth
# Import Database module
from src.main.dbmgmt import df2sql, dbSelectQuery

# Import pandas
import pandas as pd
#import numpy as np

cps_user = AppConfig.CPS_USR
cps_pwrd = AppConfig.CPS_PWD

df_confdata = pd.DataFrame()
df_confmgmt = pd.DataFrame()
df_hosting = pd.DataFrame()

df_osconfig = pd.DataFrame()
df_hostingconf = pd.DataFrame()
df_osconf = pd.DataFrame()

hostconfig = pd.DataFrame()
job_hostconf = {}
job_osconf = {}
jobconf = {}
config_data = config['CPS']['SVC_CONFMGMT']
##print(config_data)

# Get Config Management Data
def UpdateConfigData():
    #fillzero = []
    fill0 = ['cpu_sockets','cpu_ratio', 'max_vcpu', 'max_vram', 'disk_blocksize']
    # Hosting Configs
    try:
        df_confmgmt = get_xlsx2df(config_data)
    except:
        df_confmgmt = get_xlsx2df_auth(config_data,cps_user,cps_pwrd)

    df_hosting = df_confmgmt['HostingConfig']                                   # Extract Hosting Configs from Config Mgmt Data
    ##print(df_hosting.info())
    updated = str(datetime.now()).split(".")[0]

    df_hosting.columns = map(str.lower, df_hosting.columns)                     # Convert column names to lowercase
    df_hosting.columns = df_hosting.columns.str.replace(' ', '_', regex=False)				# Replace space with underscore
    df_hosting.columns = df_hosting.columns.str.replace('(', '', regex=False)				# Replace space with underscore
    df_hosting.columns = df_hosting.columns.str.replace(')', '', regex=False)				# Replace space with underscore
    df_hosting['active'] = df_hosting['active'].astype(bool)            # Convert to boolean
    df_hosting['ip_cidr'] = df_hosting['ip_cidr'].str.replace(" ","", regex=False)	        # Replace spaces in ip cidr
    df_hosting['cluster'] = df_hosting['cluster'].str.upper()	                # String to uppercase
    df_hosting['updated'] = updated #pd.to_datetime(updated)	                # Update timestamp
    df_hosting = df_hosting.dropna(subset=['tenant'])
    df_hosting[fill0] = df_hosting[fill0].fillna(0)
    df_hosting = df_hosting.fillna("")
    print(df_hosting.info())

    # Update Hosting Config in Database
    df2sql(df_hosting,"hostingconfigs","replace")

    # Windows OS Configs
    df_osconfig = df_confmgmt['OSConfig']                                       # Extract WinOS Configs from Config Mgmt Data
    ##print(df_osconfig.info())# Extract Hosting Configs from Config Mgmt Data
    df_osconfig.columns = map(str.lower, df_osconfig.columns)                     # Convert column names to lowercase
    df_osconfig.columns = df_osconfig.columns.str.replace(' ', '_', regex=False)				# Replace space with underscore
    df_osconfig.columns = df_osconfig.columns.str.replace('(', '', regex=False)				# Replace space with underscore
    df_osconfig.columns = df_osconfig.columns.str.replace(')', '', regex=False)				# Replace space with underscore
    df_osconfig = df_osconfig.dropna(subset=['business_unit'])
    df_osconfig['updated'] = updated #pd.to_datetime(updated)	                # Update timestamp
    df_osconfig = df_osconfig.fillna("")
    print(df_osconfig.info())

    # Update OS Config in Database
    df2sql(df_osconfig,"osconfigs","replace")

    response = { "success":True, "status":"Config Reference Data updated!"}

    return response

# Build query string
def BuildAndQuery(data):
    print(data)
    qry = ""
    for k,v in data.items():
        if v != "":
            qry = qry + "lower("+ str(k).lower() + ") like '%" + str(v).lower() + "%' and "
    qry = qry.rstrip(" and ") + ";"

    return qry

# Query Hosting COnfig
def GetHostingConfig(data):
    issues = 0
    comments = []
    print("\n","GET HOSTING CONFIGs","\n")
    #print(json.dumps(data, indent=2),"\n")

    if "Windows" in str(data["os_version"]):
        os_type  = "Windows"
    elif "Linux" in str(data["os_version"]):
        os_type  = "Linux"
    else:
        os_type  = "Other"

    pocmatch=['POC','Proof-Of-Concept','Concept']
    if any(c in str(data['environment']) for c in pocmatch):
        app_type = "POC"
    else:
        app_type = data['app_type']

    query = {   
        "business_unit" : str(data['business_unit']),
        "app_tier" : str(data['app_tier']),
        "app_type" : app_type,
        "os_type" : os_type
    }
    select = BuildAndQuery(query)
    df_result = dbSelectQuery('hostingconfigs',select)
    print("HOSTING CONFIGS RESULT","\n",df_result.head(10),"\n")
    #timestamp = str(datetime.now()).split(".")[0] 
    if len(df_result) > 0:
        df_result = df_result.loc[lambda df_result: df_result['active']==True]
        hosting = {}
        hosting = df_result.to_dict(orient='records')
        response = { "has_hosting":True, "hosting_issues": comments, "issue_count":issues, "hosting_configs": hosting}
        return response
    else:
        comments.append("No Hosting Config found.")              
        issues += 1
        response = { "has_hosting":False, "hosting_issues": comments, "issue_count":issues, "hosting_configs": ""}

        return response


# Query OS Config
def GetOSConfig(data):
    print("\n","GET OS CONFIGs","\n") #,json.dumps(data, indent=2),"\n")
    issues = 0
    comments = []
    if "Windows" in str(data["os_version"]):
        os_type  = "Windows"
    elif "Linux" in str(data["os_version"]):
        os_type  = "Linux"
    else:
        os_type  = "Other"

    query = {   
        "business_unit" : str(data['business_unit']),
        "app_tier" : str(data['app_tier']),
        "environment" : str(data['environment']).lower(),
        "app_type" : str(data['app_type']),
        "os_version" : str(data['os_version']),
    }
    select = BuildAndQuery(query)
    df_result = dbSelectQuery('osconfigs',select)
    print("OS CONFIGS RESULT","\n",df_result.head(10),"\n")
    #timestamp = str(datetime.now()).split(".")[0] 
    if len(df_result) > 0:
        df_result = df_result.loc[lambda df_result: df_result['active_status']==True]
        dict_result = {}
        dict_result = df_result.to_dict(orient='records')
        #print("TYPE",type(results),"\n",results,"\n") 
        response = { "has_osconfig":True, "osconfig_issues": comments, "issue_count":issues, "os_configs": dict_result}
        #return results
        return response
    else:
        comments.append("No Hosting Config found.")              
        issues += 1
        response = { "has_osconfig":False, "osconfig_issues": comments, "issue_count":issues, "os_configs": ""}

        return response

# Validate Deployment Request
def ValidateRequest(job):
    print("REQUEST VALIDATION","\n",json.dumps(job, indent=2),"\n")
    validation = {}
    issues = 0
    comments = []

    # Check Non-Production is marked as ATV.        
    if str(job["environment"]).upper() in ["PRODUCTION","PRD"]: 
        if str(job["app_tier"]).upper() == "ATV":
            comments.append(str(job["environment"]).upper() + " environment DR Tier must NOT be ATV!")
            issues += 1

    # Check Production is not marked as ATV.
    if str(job["environment"]).upper() not in ["PRODUCTION","PRD"]:
        if str(job["app_tier"]).upper() != "ATV":
            comments.append(str(job["environment"]).upper() + " environment cannot be DR Tier " + str(job["app_tier"]).upper())              
            issues += 1
 
    # Check user not requesting T2 Storage for ATV.
    if str(job["app_tier"]).upper() in ["ATV","NONE"]:
        if str(job['t2storage']) not in ["0",""]: # != 0 or job['t2storage'] != "":
            comments.append("Gold (Tier-2) Storage not available for DR ATV or NONE, request manually.")       
            issues += 1

    ## Check multiples of 2 instances requested for ATI and ATII.
    #if str(job["app_tier"]).upper() in ["ATI","ATII"]:
    #    if int(job["vm_count"]) % 2 != 0:            
    #       comments.append(str(job["app_tier"]).upper() +" requires vm_count in multiples of 2 vm instances!")       
    #        issues += 1        

    # Update response data.
    validation["validation_issues"] = comments
    validation["issue_count"] = issues 
    if issues == 0:
        validation["is_valid"] = True
    else:
        validation["is_valid"] = False
    
    return validation