import sys, json, time, requests
import config
# Assign config data to local variable
config = config.conf()
from config import AppConfig

from src.utils import get_xlsx2df, get_xlsx2df_auth

# Import pandas
import pandas as pd
#import numpy as np

cps_user = AppConfig.CPS_USR
cps_pwrd = AppConfig.CPS_PWD

df_confdata = pd.DataFrame()
df_confmgmt = pd.DataFrame()
df_hosting = pd.DataFrame()
df_osconfig = pd.DataFrame()
df_hostingconf = pd.DataFrame()
df_osconf = pd.DataFrame()
job_hostconf = {}
job_osconf = {}
jobconf = {}
config_data = config['CPS']['SVC_CONFMGMT']
##print(config_data)

# Get Config Management Data
def GetConfigData(url):
    # Hosting Configs
    try:
        df_confmgmt = get_xlsx2df(url)
    except:
        df_confmgmt = get_xlsx2df_auth(url,cps_user,cps_pwrd)

    df_hosting = df_confmgmt['HostingConfig']                                   # Extract Hosting Configs from Config Mgmt Data
    ##print(df_hosting.info())
    df_hosting.columns = map(str.lower, df_hosting.columns)                     # Convert column names to lowercase
    df_hosting.columns = df_hosting.columns.str.replace(' ', '_')				# Replace space with underscore
    df_hosting.columns = df_hosting.columns.str.replace('(', '')				# Replace space with underscore
    df_hosting.columns = df_hosting.columns.str.replace(')', '')				# Replace space with underscore
    df_hosting['ip_cidr'] = df_hosting['ip_cidr'].str.replace(" ","")	        # Replace spaces in ip cidr
    df_hosting = df_hosting.dropna(subset=['tenant'])
    df_hosting = df_hosting.fillna("")
    ##print(df_hosting.info())

    # Windows OS Configs
    df_osconfig = df_confmgmt['OSConfig']                                       # Extract WinOS Configs from Config Mgmt Data
    ##print(df_osconfig.info())# Extract Hosting Configs from Config Mgmt Data
    df_osconfig.columns = map(str.lower, df_osconfig.columns)                     # Convert column names to lowercase
    df_osconfig.columns = df_osconfig.columns.str.replace(' ', '_')				# Replace space with underscore
    df_osconfig.columns = df_osconfig.columns.str.replace('(', '')				# Replace space with underscore
    df_osconfig.columns = df_osconfig.columns.str.replace(')', '')				# Replace space with underscore
    df_osconfig = df_osconfig.dropna(subset=['business_unit'])
    df_osconfig = df_osconfig.fillna("")
    ##print(df_osconfig.info())

    return df_hosting, df_osconfig


# Validate Deployment Request
def ValidateRequest(job):
    request_status = {}
    issues = 0
    comments = []

    # Check Non-Production is marked as ATV.        
    if str(job["environment"]).upper() == "PRODUCTION": 
        if str(job["app_tier"]).upper() == "ATV":
            comments.append(str(job["environment"]).upper() + " DR Tier must NOT be ATV!")
            issues += 1       
        else:
            issues += 0

    # Check Production is not marked as ATV.
    if str(job["environment"]).upper() != "PRODUCTION":
        if str(job["app_tier"]).upper() != "ATV":
            comments.append(str(job["environment"]).upper() + " cannot be DR Tier " + str(job["app_tier"]).upper())              
            issues += 1   
        else:
            issues += 0
 
    # Check user not requesting T2 Storage for ATV.
    if str(job["app_tier"]).upper() == "ATV":
        if str(job["t2storage"]).upper() != "":            
            comments.append("Tier-2 Storage not available for ATV, request separately.")       
            issues += 1   
        else:
            issues += 0
    for x in range(0, int(job["vm_count"])):
        print("VM_#",x+1)

    # Check multiples of 2 instances requested for ATI and ATII.
    if str(job["app_tier"]).upper() == "ATI" or str(job["app_tier"]).upper() == "ATII":
        if int(job["vm_count"]) % 2 != 0:            
            comments.append(str(job["app_tier"]).upper() +" requires vm_count is multiples of 2 vm instances!")       
            issues += 1   
        else:
            issues += 0

    # Update response data. 
    if issues == 0:
        request_status["request_valid"] = True
        comments.append("Deployment request is valid.")    
        request_status["request_comments"] = comments          
    else:
        request_status["request_valid"] = False       
        comments.append("Deployment request is invalid.")       
        request_status["request_comments"] = comments    

    request_status["request_issues"] = issues
    
    return request_status

# Get Hosting Config Settings
def HostingConfig(job, hostconfig):

    if "Windows" in str(job["os_version"]):
        is_windows_image = True
        os_type  = "Windows"
    elif "Linux" in str(job["os_version"]):
        is_windows_image = False
        os_type  = "Linux"
    else:
        is_windows_image = False
        os_type  = "Other"

    hostconfig.set_index(['tenant','business_unit','app_tier','app_type','os_type','hosting_location','active'])
    hostconfig = hostconfig.loc[lambda hostconfig: hostconfig['active']==True]
    #hostconfig = hostconfig.loc[lambda hostconfig: hostconfig['tenant']==str(job['tenant'])]
    hostconfig = hostconfig.loc[lambda hostconfig: hostconfig['business_unit'].str.contains(str(job['business_unit']))]    
    hostconfig = hostconfig.loc[lambda hostconfig: hostconfig['app_type'].str.contains(str(job['app_type']))]
    hostconfig = hostconfig.loc[lambda hostconfig: hostconfig['app_tier'].str.contains(str(job['app_tier']))]
    hostconfig = hostconfig.loc[lambda hostconfig: hostconfig['os_type'].str.contains(os_type)]
    # Move to filter out based on AT placement
    hostconfig = hostconfig.loc[lambda hostconfig: hostconfig['hosting_location'].str.contains(str(job['hosting_location']))]
    # Rest dataframe to filter ATs
    hostconfig.reset_index()

    ##print(hostconfig)
    if len(hostconfig) != 0:
        print("Host Configs Found:", len(hostconfig))
        #print(hostconfig.iloc[0])
        print(hostconfig)

        job_dict = hostconfig.to_dict(orient='records')
        job_dict[0]["is_windows_image"] = is_windows_image
        job_dict[0]["os_type"] = os_type
        job_dict[0]["business_unit"] = str(job['business_unit'])        
        job_json = json.dumps(job_dict[0])

        return job_json
    else:
        job_dict = {"host_config":"Host Config Not Found!"}
        job_json = json.dumps(job_dict)

        return job_json   


# Get Windows OS Config Settings
def OSConfig(job, osconfig):

    osconfig.set_index(['business_unit','environment','app_type','os_version'])
    osconfig = osconfig.loc[lambda osconfig: osconfig['business_unit']==str(job['business_unit'])]    
    osconfig = osconfig.loc[lambda osconfig: osconfig['app_tier'].str.contains(str(job['app_tier']).upper())]
    ##print(osconfig)
    osconfig = osconfig.loc[lambda osconfig: osconfig['environment'].str.contains(str(job['environment']).lower())]
    osconfig = osconfig.loc[lambda osconfig: osconfig['app_type'].str.contains(str(job['app_type']))]    
    osconfig = osconfig.loc[lambda osconfig: osconfig['os_version'].str.contains(str(job['os_version']))]

    ##print(osconfig)
    if len(osconfig) != 0:
        #print("OS Configs Found:", len(osconfig))
        print("OS_CONFIG")
        #print(osconfig)
        print(osconfig.iloc[0])        

        job_dict = osconfig.to_dict(orient='records')
        job_json = json.dumps(job_dict[0])

        return job_json
    else:
        #print("OS Configs Found:", len(osconfig))
        job_dict = {"os_config":"OS Config Not Found!"}
        job_json = json.dumps(job_dict)

        return job_json        


# Obtain Configuration Reference Data
def GetConfigReference(jobrequest):
    jobconf = {}
    request_status = ValidateRequest(jobrequest)
    if request_status["request_valid"]:
        df_hostingconfig, df_osconf = GetConfigData(config_data)
        # Get Hosting Config
        job_hostconf = json.loads(HostingConfig(jobrequest, df_hostingconfig))
        # Get WinOS Config
        job_osconf = json.loads(OSConfig(jobrequest, df_osconf))
        # Merge Config Settings
        jobconf = dict(list(job_osconf.items()) + list(job_hostconf.items()))

        jobconf["request_valid"] = True

        return jobconf
    else:
        return request_status