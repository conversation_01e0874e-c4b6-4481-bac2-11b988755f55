import os, requests, json
import subprocess
from rich import print
from datetime import date, time, datetime
from random import randrange
import pandas as pd
import numpy as np
import math, decimal
from copy import deepcopy
from requests_ntlm import HttpNtlmAuth
from io import BytesIO

from docx.shared import Cm
from docxtpl import DocxTemplate, InlineImage
# Import custom modules
#from src.utils import Docx2Pdf
from src.main.dbmgmt import df2sql, dbQuery
#import MariaDB connection
from src.mdbsql import mdbengine, df2conn, text
from src.main.jiramgmt import ChangeJiraStatus, AddJiraComment, AddJiraAttachment
import config
# Assign config data to local variable
config = config.conf()
from config import AppConfig, basedir

cps_user = AppConfig.CPS_USR
cps_pwrd = AppConfig.CPS_PWD
rates_data = config['AUTODEPLOY']['DCSRATES']
tmplt_name = config['AUTODEPLOY']['QUOTETMPLT'] #"dcstmplt_v1b.docx"
context = config['AUTODEPLOY']['DCSCONTEXT']
quotes_enabled = config['AUTODEPLOY']['QUOTES_ENABLED']

#basepath = os.path.abspath(os.path.dirname(__file__))
print("BaseDIR", basedir)
datapath = os.path.join(basedir,"data")
print("DataPath", datapath)
dcstmplt = os.path.join(datapath, tmplt_name)
print(f"TEMPLATE PATH: {dcstmplt}\n")

#print(f"Template {dcstmplt}\n")
doc = DocxTemplate(dcstmplt)
wdFormatPDF = 17

mem_baseline = 32
memswap_ratio = 1.5
windows_os = 120
linux_os = 50
no_os_disk = ['APPLIANCE','SHELL','VDI']

d = datetime.today()
datestr = str(datetime.now()).split(".")[0]
today = d.date()
year1 = today.year
year2 = year1 + 1
year3 = year1 + 2
months = 12
months_left = months - today.month


# Convert to PDF
def Convert2Pdf(infile):
    print(f"\nTEMPLATE: {tmplt_name}")
    #print("DOCX2PDF\n")
    print(f"DOCFILE: {infile}")
    outdir = os.path.split(infile)[0]
    print(f"OUTDIR: {outdir}")
    outfile = str(infile).rsplit(".")[0] +".pdf"
    print(f"PDFFILE: {outfile}")
    convert2pdf = rf"soffice --headless --convert-to pdf {infile} --outdir {outdir}"
    subprocess.run(convert2pdf, shell=True) 
    return outfile


# Get Config Management Data
def UpdateRates():
    try:
        print("No AUTH")
        df = pd.read_excel(rates_data,sheet_name="rates")
    except:
        print("With AUTH")
        r = requests.get(rates_data,auth=HttpNtlmAuth(cps_user,cps_pwrd), verify=False)
        df = pd.read_excel(BytesIO(r.content),engine='openpyxl',sheet_name="rates")
    df2sql(df,"rates","replace")
    status = {'success':True, 'message':'DCS rates updated.'}
    return status

# Round Up to Base
def roundup(x,base):
    return int(math.ceil(x / base))*base

def getASO(bu):
    if bu in config['AUTODEPLOY']['SGT']:
        return "SGT"
    elif bu in config['AUTODEPLOY']['SANTAM']:
        return "Santam"
    elif bu in config['AUTODEPLOY']['RETAILMASS']:
        return "Retail Mass"
    else:
        return "SGT"
    
def prepContext(dctIn):
    dctRequest = dctIn.copy()
    dctRequest.update({
        'doc_date' : str(datetime.now()).split(".")[0],
        'submit_date' : str(today),
        'monthsleft' : months_left,
        'year1': year1,
        'year2': year2,
        'year3': year3
    })
    return dctRequest

def prepQuote(dctIN):
    print("\nPREP QUOTE\n")
    os_added = False
    if str(dctIN['app_type']).upper() in no_os_disk:
        os_disk_size = 0
    else:
        if dctIN['os_type'] == "Windows":
            if int(dctIN['vram']) > mem_baseline:
                os_disk_size = windows_os + roundup(int(dctIN['vram']*memswap_ratio),10)
            else:
                os_disk_size = windows_os
        else:
            os_disk_size = linux_os
    dctIN['vm_count'] = int(dctIN['vm_count'])
    if dctIN['t2storage'] == "":
        dctIN['t2total'] = 0
    else:
        t2disks = str(dctIN['t2storage']).split(",")
        dctIN['t2storage'] = str(os_disk_size) + "," + dctIN['t2storage'] 
        dctIN['t2total'] = sum([int(x) for x in t2disks]) + os_disk_size
        os_added = True

    if dctIN['t3storage'] == "":
        dctIN['t3total'] = 0
    else:
        t3disks = str(dctIN['t3storage']).split(",")
        if not os_added:
            dctIN['t3storage'] = str(os_disk_size) + "," + dctIN['t3storage']
            dctIN['t3total'] = sum([int(x) for x in t3disks]) + os_disk_size
        else:
            dctIN['t3total'] = sum([int(x) for x in t3disks])

    print(json.dumps(dctIN, indent=2),"\n") 
    dctTMP = {k:[v] for k,v in dctIN.items()}  # WORKAROUND
    dfIN = pd.DataFrame.from_dict(dctTMP)
    #print(dfIN.info(),"\n")
    dfQte = deepcopy(dfIN[['business_unit','reference','app_tier','vm_count','os_version','vcpus','vram','t2total','t3total','t2storage','t3storage']]) #'vm_name','os_type',

    if dctIN['app_tier'].upper() in ["ATIII","ATIV"]:            # Check 1st server AppTier = ATIII(3) or ATIV (4), backup all servers
        dfQte['has_backups'] = dfQte['vm_count']
    elif dctIN['app_tier'].upper() in ["ATI","ATII"]:
        dfQte.loc[0,'has_backups'] = 1
    else:
        dfQte['has_backups'] = 0                                    # Set all servers to NO Backups
    dfQte['backups'] = dfQte['has_backups'] * (dfQte['t2total'] + dfQte['t3total'])
    dfQte['support'] = 1
    dfQte['total'] = 0

    #dfQte.set_index('vm_name', inplace=True)                        # set the servernames as index
    dfQte.fillna(0,inplace=True)
    print(dfQte.info())
    print(dfQte,"\n")

    dfOUT = dfQte.transpose()
    # print(dfOUT,"\n")

    return dfOUT

def getRates(dctIn):
    print("\nDCS RATES SPECS\n",dctIn,"\n")
    aso = getASO(dctIn)
    if str(dctIn['environment']).lower() in ["proof-of-concept","poc"]:
        os_type = "POC"
    else:
        os_type = dctIn['os_type']

    qry_rates = f"SELECT * from rates where asset_owner = '{aso}' and os_type = '{os_type}' ;" #and fy_period = '{year1}'
    dfRates = dbQuery(qry_rates)
    dfRates.columns = map(str.lower, dfRates.columns)                     # Convert column names to lowercase
    dfRates.columns = dfRates.columns.str.replace(' ', '_')				# Replace space with underscore
    dfRates.fillna(0,inplace=True)
    # Convert rates query to dict object
    dctRates = dfRates.to_dict(orient='records')
    print(f"RATES\n{json.dumps(dctRates, indent=2)}\n")
    return dctRates

# Calculate Quote Costs
def generateQuote(dfIn,rates):
    print("\nGENERATE QUOTE\n", dfIn,"\n")
    vm_count = dfIn[0]['vm_count']
    droprows = ['business_unit','reference','app_tier','vm_count','os_version','has_backups'] #
    datarows = ['vcpus','vram','t2total','t3total','backups','support']
    df = deepcopy(dfIn)
    df.drop(droprows,axis=0,inplace=True)

    support_cost = \
        float(rates['hardware_maintenance']) + \
        float(rates['gti_cost']) + \
        float(rates['shared_tech']) + \
        float(rates['bcx_service_cost']) + \
        float(rates['software_maintenance']) + \
        float(rates['rhel']) + \
        float(rates['ha_licence']) + \
        float(rates['facilities']) + \
        float(rates['fc_port'])
    # using apply method
    df['DETAIL'] = ""
    df = df.apply(pd.to_numeric, errors = 'coerce')
    df['QUANTITY'] = df.sum(axis=1,numeric_only = True)
    df['QUANTITY'] = df['QUANTITY'] * vm_count
    df['MONTHLY'] = 0
    bau_y1 = "BAU_Y1"
    est_y2 = "EST_Y2"
    est_y3 = "EST_Y3"
    
    df = df[['DETAIL','QUANTITY','MONTHLY']]
    df.loc['vcpus','DETAIL']   = dfIn.loc['vcpus',0]
    df.loc['vram','DETAIL']    = dfIn.loc['vram',0]
    df.loc['t2total','DETAIL'] = dfIn.loc['t2storage',0]
    df.loc['t3total','DETAIL'] = dfIn.loc['t3storage',0]
    df.drop(['t2storage','t3storage'],axis=0,inplace=True)
    df[bau_y1] = 0
    df[est_y2] = 0
    df[est_y3] = 0
    df['ITEM'] = df.index
    df.loc['vcpus','MONTHLY']   = rates['cpu'] * df.loc['vcpus','QUANTITY']
    df.loc['vram','MONTHLY']    = rates['ram'] * df.loc['vram','QUANTITY']
    df.loc['t2total','MONTHLY'] = rates['stg_tier_2'] * df.loc['t2total','QUANTITY']
    df.loc['t3total','MONTHLY'] = rates['stg_tier_3'] * df.loc['t3total','QUANTITY']
    df.loc['backups','MONTHLY'] = rates['stg_back-up'] * df.loc['backups','QUANTITY']
    df.loc['support','MONTHLY'] = int(support_cost) * df.loc['support','QUANTITY']

    df.loc['vcpus',bau_y1]      = months_left * df.loc['vcpus','MONTHLY']
    df.loc['vram',bau_y1]       = months_left * df.loc['vram','MONTHLY']
    df.loc['t2total',bau_y1]    = months_left * df.loc['t2total','MONTHLY']
    df.loc['t3total',bau_y1]    = months_left * df.loc['t3total','MONTHLY']
    df.loc['backups',bau_y1]    = months_left * df.loc['backups','MONTHLY']
    df.loc['support',bau_y1]    = months_left * df.loc['support','MONTHLY']

    df.loc['vcpus',est_y2]      = months * df.loc['vcpus','MONTHLY']
    df.loc['vram',est_y2]       = months * df.loc['vram','MONTHLY']
    df.loc['t2total',est_y2]    = months * df.loc['t2total','MONTHLY']
    df.loc['t3total',est_y2]    = months * df.loc['t3total','MONTHLY']
    df.loc['backups',est_y2]    = months * df.loc['backups','MONTHLY']
    df.loc['support',est_y2]    = months * df.loc['support','MONTHLY']

    df.loc['vcpus',est_y3]      = months * df.loc['vcpus','MONTHLY']
    df.loc['vram',est_y3]       = months * df.loc['vram','MONTHLY']
    df.loc['t2total',est_y3]    = months * df.loc['t2total','MONTHLY']
    df.loc['t3total',est_y3]    = months * df.loc['t3total','MONTHLY']
    df.loc['backups',est_y3]    = months * df.loc['backups','MONTHLY']
    df.loc['support',est_y3]    = months * df.loc['support','MONTHLY']

    df.loc['total', 'MONTHLY'] = df['MONTHLY'].sum()
    df.loc['total', bau_y1] = df[bau_y1].sum()
    df.loc['total', est_y2] = df[est_y2].sum()
    df.loc['total', est_y3] = df[est_y3].sum()

    df = df.reset_index()
    df.fillna(0,inplace=True)
    #df['DETAIL'] = df['DETAIL'].astype(int)
    df['QUANTITY'] = df['QUANTITY'].astype(int)
    df['MONTHLY'] = df['MONTHLY'].apply('R {:,.0f}'.format)
    df[bau_y1] = df[bau_y1].apply('R {:,.0f}'.format)
    df[est_y2] = df[est_y2].apply('R {:,.0f}'.format)
    df[est_y3] = df[est_y3].apply('R {:,.0f}'.format)

    # print(df.info(),"\n")
    # print(df,"\n")

    dctOut = df.to_dict('records')
    #print(dctOut,"\n")
    return dctOut

# Process Quote request
def DcsQuote(dctIn):
    print("\nDCS QUOTE SPECS\n",dctIn['data'])
    request = prepContext(dctIn['data'])
    dfSpecs = prepQuote(dctIn['data'])
    aso_rates = getRates(dctIn['data'])
    dctQuote = generateQuote(dfSpecs,aso_rates[0])
    context.update({'request' : request , 'quote' : dctQuote})
    doc_name = str(request['reference']).upper()
    #print(json.dumps(context, indent=2),"\n")
    doc.render(context)
    doc_docx = os.path.join(datapath,doc_name+".docx") 
    print(f"DOCX IN: {doc_docx}\n")
    #doc_docx=doc_name+".docx"
    doc.save(doc_docx)
    doc_pdf = Convert2Pdf(doc_docx) #Docx2Pdf(doc_docx)
    jira_attached = AddJiraAttachment(doc_name, doc_pdf)
    if jira_attached['success']:
        print("Cleaning up JIRA attachments...")
        os.remove(doc_docx)
        os.remove(doc_pdf)

    return {'success':True, 'message':f'DCS Quote Spec generated - {doc_name}','reference': str(request['reference']).upper(),'filename':str(doc_pdf)}
