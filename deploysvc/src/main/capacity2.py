import sys, json, time, requests
from datetime import date, time, datetime

# Import pandas
import pandas as pd
#import numpy as np

from src.utils import get_xlsx2df, get_xlsx2df_auth
# Import Database module
from src.main.dbmgmt import dbGetAllJobs, dbRefreshCapacity, dbGetCapacity, dbRefreshResources, dbUpdateCapacity

import config
# Assign config data to local variable
config = config.conf()
from config import AppConfig

cps_user = AppConfig.CPS_USR
cps_pwrd = AppConfig.CPS_PWD

df_confdata = pd.DataFrame()
df_cpctmgmt = pd.DataFrame()
df_compute = pd.DataFrame()
df_compcpct = pd.DataFrame()
df_hosting = pd.DataFrame()
df_storage = pd.DataFrame()
df_computeconf = pd.DataFrame()
df_osconf = pd.DataFrame()
df_t2storage = pd.DataFrame()
df_t3storage = pd.DataFrame()

df_capdata = pd.DataFrame
df_available_cmpt = pd.DataFrame()
df_computecap = pd.DataFrame()
df_available_strg = pd.DataFrame()
df_available_t2strg = pd.DataFrame()
df_available_t3strg = pd.DataFrame()
df_available = pd.DataFrame()   
df_allocated = pd.DataFrame() 
df_pending = pd.DataFrame() 

job_hostconf = {}
job_osconf = {}
jobconf = {}
capacity_dict = {}


t2_tags = ["DIA","SAP"]
t3_tags = ["AMB","QUA","RUB","EME","VSAN","C:"]
#mirror_tags = ["NML","NMP"]
mirror_tags = ["MSP"]
notmirrored = ["ATV"]
excl_tags = ["-0","DNU","PDF","MAP"]
dedicated = 3584

capacity_data = config['CPS']['SVC_CAPACITY']
##print(capacity_data)

# Get Config Management Data
def GetResourcesData():
    # capacity Configs
    try:
        df_cpctmgmt = get_xlsx2df(capacity_data)
    except:
        df_cpctmgmt = get_xlsx2df_auth(capacity_data,cps_user,cps_pwrd)
    
    df_resources = df_cpctmgmt['Combined']                                         # Extract capacity Configs from Config Mgmt Data
    #print(df_resources.info())
    df_resources.columns = map(str.lower, df_resources.columns)                     # Convert column names to lowercase
    df_resources['clustername'] = df_resources['clustername'].str.replace(".mud.internal.co.za","", regex=False)	        # Strip dns suffix from clustername
    df_resources['clustername'] = df_resources['clustername'].str.upper()
    df_resources = df_resources.fillna(0)
    updated = str(datetime.now()).split(".")[0]
    df_resources['updated'] = updated
    df_resources.reset_index(inplace=True)

    query_pending = {
        "hosting_platform"  : "",
        "request_jobid"     : "",
        "job_stage"         : "VM_DEPLOY",
        "job_status"        : "PENDING",
        "job_state"         : ""
    } 
    df_vmdeploy = dbGetAllJobs(query_pending)
    query_pending = {
        "hosting_platform"  : "",
        "request_jobid"     : "",
        "job_stage"         : "AD_OBJECT",
        "job_status"        : "PENDING",
        "job_state"         : ""
    } 
    df_adobject = dbGetAllJobs(query_pending)
    df_pending = df_adobject.append(df_vmdeploy)
    print("\n","PENDING JOBs","\n")
    print(df_pending[['job_stage','job_status','target_cluster','vcpus','vram','t2_datastore','t2total','t3_datastore','t3total']],"\n")

    # Update database table
    #dbRefreshResources(df_resources)
    status = {
        'success' : True,
        'message' : "Available Resources updated!"
    }

    return status

# Update Capacity Data
def UpdateResources(data_dict):
    #print(data_dict)
    df = pd.DataFrame.from_dict(data_dict)
    
    df.reset_index(inplace=True)
    print(df.info(),"\n")
    print(df.head(5),"\n")
    #df.drop(columns=['level_0'],inplace=True)
    
    dbRefreshResources(df)
    status = {
        'success' : True,
        'message' : "Available resources updated!"
    }

    return status

# Get Config Management Data
def GetCapacityData(url):
    # capacity Configs
    try:
        df_cpctmgmt = get_xlsx2df(url)
    except:
        df_cpctmgmt = get_xlsx2df_auth(url,cps_user,cps_pwrd)
    
    df_compcpct = df_cpctmgmt['Compute']                                         # Extract capacity Configs from Config Mgmt Data
    #print(df_compcpct.info())
    df_compcpct.columns = map(str.lower, df_compcpct.columns)                     # Convert column names to lowercase
    df_compcpct['cluster'] = df_compcpct['cluster'].str.replace(".mud.internal.co.za","")	        # Strip dns suffix from clustername
    df_compcpct['cluster'] = df_compcpct['cluster'].str.upper()
    df_compcpct = df_compcpct.fillna(0)

    # Windows OS Configs
    df_storage = df_cpctmgmt['Storage']                                       # Extract WinOS Configs from Config Mgmt Data
    ##print(df_storage.info())# Extract capacity Configs from Config Mgmt Data
    df_storage.columns = map(str.lower, df_storage.columns)                     # Convert column names to lowercase
    df_storage['clustername'] = df_storage['clustername'].str.replace(".mud.internal.co.za","")	        # Strip dns suffix from clustername
    df_storage['clustername'] = df_storage['clustername'].str.upper()
    #df_storage = df_storage.fillna(0)
    df_storage.fillna(value={'name': ""}, inplace=True)

    #print(df_storage.info())

    return df_compcpct, df_storage

# Get capacity Config Settings
def ComputeCapacity(job, compute):
    #print("JOB", job)
    compute_status = {}
    compute.set_index(['cluster'])
    #compute = compute.loc[lambda compute: compute['cluster'].str.contains(str(job['cluster']).upper())]
    tgt_compute = compute.loc[compute['cluster'] == str(job['cluster']).upper()]
    print("COMPUTE")
    print(tgt_compute)

    try:
        compute_status["target_cluster"] = str(tgt_compute.iloc[0]['cluster']).upper()
        ##print(compute_status)
        if len(tgt_compute) != 0:
            #print("Compute Capacity Found:", len(tgt_compute))    
            print(tgt_compute.iloc[0])
            #print()
            #tgt_cls = tgt_compute.to_dict(orient='records')
            compute_status['preferred_host']  = str(tgt_compute.iloc[0]['preferred_host']).lower()
            # vCPUs Capacity
            if tgt_compute.iloc[0]['availablecpu'] >= int(job['vcpus']):
                vcpus_ok = True
            else:
                vcpus_ok = False 
            #print("vcpus_ok:",vcpus_ok)       
            compute_status["vcpus_free"]  = int(tgt_compute.iloc[0]['availablecpu'])
            compute_status["vcpus_ok"] = vcpus_ok  

            # vRAM Capacity
            if tgt_compute.iloc[0]['availablemem'] >= int(job['vram']):
                vram_ok = True
                compute_status["vram_mb"] = int(job['vram'])*1024
            else:
                vram_ok = False        
            #print("vram_ok:",vram_ok)       
            compute_status["vram_free"] = int(tgt_compute.iloc[0]['availablemem'])
            compute_status["vram_ok"] = vram_ok

            if vcpus_ok and vram_ok:
                compute_status["compute_status"] = "Compute Capacity Sufficient!"
                compute_status["compute_ok"] = True
            if not vcpus_ok and vram_ok:
                compute_status["compute_status"] = "vCPUs Capacity Insufficient!"
                compute_status["compute_ok"] = False
            if vcpus_ok and not vram_ok:
                compute_status["compute_status"] = "vRAM Capacity Insufficient!"
                compute_status["compute_ok"] = False        
            if not vcpus_ok and not vram_ok:
                compute_status["compute_status"] = "vCPUs and vRAM Capacity Insufficient!"
                compute_status["compute_ok"] = False

            return json.dumps(compute_status)
    except:
        compute_status["compute_status"] = "Compute Target Cluster Not Found!"
        compute_status["compute_ok"] = False
        #job_json = json.dumps(job_dict)
        return json.dumps(compute_status)


# Get Storage Config Settings
def StorageCapacity(job, storage):
    print("STORAGE JOB","\n", job)
    storage_status = {}
    storage.set_index(['clustername'])
    storage_status["t2_datastore"] = ""
    storage_status["t3_datastore"] = ""
    #storage = storage.loc[lambda storage: storage['cluster'].str.contains(str(job['cluster']).upper())]
    tgt_storage = storage.loc[storage['clustername'] == str(job['cluster'])]
    tgt_storage.sort_values(by=['availablestorage'], inplace = True)
    #print(tgt_storage.info(), "\n")
    #print(tgt_storage[['clustername','datastore_name','tier','availablestorage','mirrored','availablecpu','availablemem','preferred_host']])
    
    try:        
        storage_status["target_cluster"] = str(tgt_storage.iloc[0]['clustername']).upper()
        clustername = str(storage_status["target_cluster"])

        if job['business_unit'].upper() == "SANTAM":
            if job['app_tier'].upper() == "ATV":
                mirrored = False
                mirror_tag = "NML"                
            else:
                mirrored = True
                mirror_tag = "MSP"
        else:
            if job['app_tier'].upper() == "ATIII":
                mirrored = True
                mirror_tag = "MSP"
            else:
                mirrored = False
                mirror_tag = "NML"
        
    except:
        storage_status["storage_status"] = "Storage Target Not Found!"
        storage_status["storage_ok"] = False
        #job_json = json.dumps(job_dict)
        return json.dumps(storage_status)

    # Process Tier 2 Storage Request
    if job['t2storage'] != "":          
        #print("ST2:",job['t2storage'])
        t2_disks = []
        t2_drives = []        
        try:
            t2_list = job['t2storage'].split(",")            
            t2_total = sum([int(x) for x in t2_list])
            storage_status["t2storage_disks"] = list(map(int,t2_list))
            t2_drives = job['t2drives'].split(",")            
            #print("T2 TOTAL:",t2_total)                 
        except ValueError:
            t2_disks.append(int(job['t2storage']))
            t2_total = int(job['t2storage'])
            #print("T2 TOTAL:",t2_total)
            t2_drives.append(job['t2drives'])
        
        if job['app_tier'].upper() != "ATV":
            t2_total = t2_total + int(job['os_disksize'])
        
        storage_status["t2storage_total"] = t2_total
        storage_status["t2storage_drives"] = t2_drives
        storage_status["t2storage_disks"] = str(job['t2storage'])
        

        if t2_total <= dedicated:
            #print("T2 Total within standard size!. Mirrored =", mirrored)
            try:
                t2_storage = tgt_storage.loc[storage['datastore_name'].str.contains('|'.join(t2_tags))]
                #print("Available T2:", t2_storage)

                if mirrored:
                    t2_storage = t2_storage.loc[storage['datastore_name'].str.contains('|'.join(mirror_tags))]                
                    #print("Keep Mirrored T2:",t2_storage)
                else:
                    t2_storage = t2_storage.loc[~storage['datastore_name'].str.contains('|'.join(mirror_tags))]                
                    #print("Dropped Mirrored T2:",t2_storage)                    
                
                t2_storage = t2_storage[~t2_storage['datastore_name'].str.contains('|'.join(excl_tags),na=False)]        # Remove dedicated datastores
                #print("Drop Excl T2:",t2_storage)

                # Find all available datastores with enough capacity for the request
                t2_storage = t2_storage.loc[storage['availablestorage'] > t2_total]
                #print(t2_storage)
                if len(t2_storage) > 0:
                    # Find the smallest datastore from available list.
                    t2_storage.sort_values(by=['availablestorage'], inplace = True)
                    
                    #print(t2_storage.iloc[0])
                    t2storage_ok = True
                    storage_status["t2storage_ok"] = t2storage_ok  
                    storage_status["t2_datastore"] = t2_storage.iloc[0]["datastore_name"]
                    storage_status["t2storage_status"] = ""
                    #storage_status["t2_available"] = t2_storage.iloc[0]["availablestorage"]                            
                else:
                    storage_status["t2storage_status"] = clustername + " Tier 2 " + str(mirror_tag) + " Storage Required!"
                    #print(storage_status["t2storage_status"])
                    t2storage_ok = False
                    storage_status["t2storage_ok"] = t2storage_ok      
                    storage_status["t2_datastore"] = ""
                
                #print(tgt_storage[['clustername','datastore_name','tier','availablestorage','mirrored','availablecpu','availablemem','preferred_host']])
                        
            except:
                storage_status["t2storage_status"] = clustername + " Tier 2 "+ str(mirror_tag) +" Storage Not Found!"
                t2storage_ok = False
                storage_status["t2storage_ok"] = t2storage_ok  
                storage_status["t2_datastore"] = ""
        else:
            storage_status["t2storage_status"] = clustername + " Dedicated Tier 2 "+ str(mirror_tag) +" Storage Required!"
            t2storage_ok = False
            storage_status["t2storage_ok"] = t2storage_ok      
            #storage_status["t2_datastore"] = ""
            storage_status["t2storage_disks"] = str(job['t2storage'])
    else:

        storage_status["t2storage_status"] = "Tier 2 Storage Not Required!"
        t2storage_ok = True
        storage_status["t2storage_ok"] = t2storage_ok 
        #storage_status["t2_datastore"] = ""
        storage_status["t2storage_total"] = 0
        storage_status["t2storage_disks"] = str(job['t2storage'])
        storage_status["t2storage_drives"] = str(job['t2drives'])
    
    # Process Tier 3 Storage Request
    if job['t3storage'] != "":          
        #print("ST3:",job['t3storage'])
        t3_disks = []
        t3_drives = []
        try:
            t3_list = job['t3storage'].split(",")
            t3_total = sum([int(x) for x in t3_list])
            #print("T3 TOTAL:",t3_total)                 
            storage_status["t3storage_disks"] = list(map(int,t3_list))
            t3_drives = job['t3drives'].split(",")
            
        except ValueError:            
            t3_disks.append(int(job['t3storage']))
            t3_total = int(job['t3storage'])
            #print("T3 TOTAL:",t3_total)
            storage_status["t3storage_disks"] = t3_disks
            t3_drives.append(job['t3drives'])

        if job['app_tier'].upper() == "ATV":
            t3_total = t3_total + int(job['os_disksize'])

        storage_status["t3storage_total"] = t3_total
        storage_status["t3storage_drives"] = t3_drives
        storage_status["t3storage_disks"] = str(job['t3storage'])
        

        if t3_total <= dedicated:
            #print("T3 Total within standard size!. Mirrored =", mirrored)
            try:
                t3_storage = tgt_storage.loc[storage['datastore_name'].str.contains('|'.join(t3_tags))]
                #print("Available T3:", t3_storage)

                if mirrored:
                    t3_storage = t3_storage.loc[storage['datastore_name'].str.contains('|'.join(mirror_tags))]                
                    #print("Keep Mirrored T3:",t3_storage)
                else:
                    t3_storage = t3_storage.loc[~storage['datastore_name'].str.contains('|'.join(mirror_tags))]                
                    #print("Dropped Mirrored T3:",t3_storage)                    
                

                t3_storage = t3_storage[~t3_storage['datastore_name'].str.contains('|'.join(excl_tags),na=False)]        # Remove dedicated datastores
                #print("Drop Excl T3:",t3_storage)

                # Find all available datastores with enough capacity for the request
                t3_storage = t3_storage.loc[storage['availablestorage'] > t3_total]
                #print(t3_storage)
                if len(t3_storage) > 0:
                    # Find the smallest datastore from available list.
                    t3_storage.sort_values(by=['availablestorage'], inplace = True)
                    
                    #print(t3_storage.iloc[0])
                    t3storage_ok = True
                    storage_status["t3storage_ok"] = t3storage_ok  
                    storage_status["t3_datastore"] = t3_storage.iloc[0]["datastore_name"]
                    storage_status["t3storage_status"] = ""
                    #storage_status["t3_available"] = t3_storage.iloc[0]["availablestorage"]                            
                else:
                    storage_status["t3storage_status"] = clustername + " Tier 3 " + str(mirror_tag) + " Storage Required!"
                    #print(storage_status["t3storage_status"])
                    t3storage_ok = False
                    storage_status["t3storage_ok"] = t3storage_ok      
                    storage_status["t3_datastore"] = ""      
                    storage_status["t3storage_total"] = 0        
                
                #print(tgt_storage[['clustername','datastore_name','tier','availablestorage','mirrored','availablecpu','availablemem','preferred_host']])
    
            except:
                storage_status["t3storage_status"] = clustername + " Tier 3 "+ str(mirror_tag) +" Storage Not Found!"
                t3storage_ok = False
                storage_status["t3storage_ok"] = t3storage_ok  
                storage_status["t3_datastore"] = ""
             
        else:
            storage_status["t3storage_status"] = clustername + " Dedicated Tier 3 "+ str(mirror_tag) +" Storage Required!"
            t3storage_ok = False
            storage_status["t3storage_ok"] = t3storage_ok      
            #storage_status["t3_datastore"] = ""
            storage_status["t3storage_disks"] = str(job['t3storage'])
    else:
        storage_status["t3storage_status"] = "Tier 3 Storage Not Required!"
        t3storage_ok = True
        storage_status["t3storage_ok"] = t3storage_ok 
        #storage_status["t3_datastore"] = ""
        storage_status["t3storage_disks"] = str(job['t3storage'])
        storage_status["t3storage_drives"] = str(job['t3drives'])
    

    if t2storage_ok and t3storage_ok:
        storage_status["storage_status"] = "Storage Capacity Sufficient!"
        storage_status["storage_ok"] = True
    if not t2storage_ok and t3storage_ok:
        storage_status["storage_status"] = "Tier 2 Capacity Insufficient!"
        storage_status["storage_ok"] = False
    if t2storage_ok and not t3storage_ok:
        storage_status["storage_status"] = "Tier 3 Capacity Insufficient!"
        storage_status["storage_ok"] = False    
    if not t2storage_ok and not t3storage_ok:
        storage_status["storage_status"] = "Tier 2 and Tier 3 Capacity Insufficient!"
        storage_status["storage_ok"] = False

    return json.dumps(storage_status)


# Perform Capacity Check
def CapacityCheck(jobrequest):
    print("REQUESTED:", jobrequest)
    jobrequest["cluster"] = str(jobrequest["cluster"]).upper()
    df_compute, df_storage = GetCapacityData(capacity_data)

    ##print(ComputeCapacity(jobrequest,df_compute))
    compute_capacity = json.loads(ComputeCapacity(jobrequest,df_compute))

    ##print(StorageCapacity(jobrequest,df_storage))
    storage_capacity = json.loads(StorageCapacity(jobrequest,df_storage))

    #capacity_check = dict(list(job_osconf.items()) + list(job_hostconf.items()))
    capacity_check = dict(list(compute_capacity.items()) + list(storage_capacity.items()))
    
    #capacity_status = {}
    #comments = []

    print(json.dumps(capacity_check))

    return capacity_check

# Perform Capacity Check
def CapacityData():
    # Load Avaiable Resources Data
    df_available_cmpt, df_available_strg = GetCapacityData(capacity_data)

    df_available = pd.merge(df_available_cmpt, df_available_strg, left_on='cluster',right_on='clustername', how='left')
    df_available.drop(['clustername','vcenter_y'], axis=1, inplace = True)
    df_available.rename(columns={'cluster': 'clustername','vcenter_x': 'vcenter','name': 'datastore_name','preferredhost': 'preferred_host'}, inplace=True)

    print(df_available.info())

    return df_available

# Update Capacity Data
def RefreshCapacityData():
    # Load Avaiable Resources Data
    df_available_cmpt, df_available_strg = GetCapacityData(capacity_data)
    platform_capacity_check = {
        "hosting_platform"  : "",
        "request_jobid"     : "",
        "job_stage"         : "VM_DEPLOY",
        "job_status"        : "PENDING",
        "job_state"         : ""
    } 
    df_pending = dbGetAllJobs(platform_capacity_check)

    # itertuples() yields an iterate to named tuple
    for row in df_pending.itertuples(name='Cluster'):
        # Convert named tuple to dictionary
        dictRow = row._asdict()

        df_available_cmpt.loc[df_available_cmpt['cluster']==dictRow['target_cluster'],"availablecpu"] -= int(dictRow['vcpus'])
        df_available_cmpt.loc[df_available_cmpt['cluster']==dictRow['target_cluster'],"availablemem"] -= int(dictRow['vram'])

        # Access elements from dict i.e. row contents
        #print(dictRow['t2_datastore'],dictRow['t2total'],dictRow['t3_datastore'],dictRow['t3total'])
        if str(dictRow['t2_datastore']) != "":
            #print(df_available_strg.loc[(df_available_strg['clustername']==dictRow['target_cluster']) & (df_available_strg['name']==dictRow['t2_datastore']),["availablestorage"]],"\n")            
            df_available_strg.loc[(df_available_strg['clustername']==dictRow['target_cluster']) & (df_available_strg['name']==dictRow['t2_datastore']),"availablestorage"] -= int(dictRow['t2total'])
            #print(df_available_strg.loc[(df_available_strg['clustername']==dictRow['target_cluster']) & (df_available_strg['name']==dictRow['t2_datastore']),["availablestorage"]],"\n")

        if str(dictRow['t3_datastore']) != "":
            #print(df_available_strg.loc[(df_available_strg['clustername']==dictRow['target_cluster']) & (df_available_strg['name']==dictRow['t3_datastore']),["availablestorage"]],"\n")
            df_available_strg.loc[(df_available_strg['clustername']==dictRow['target_cluster']) & (df_available_strg['name']==dictRow['t3_datastore']),"availablestorage"] -= int(dictRow['t3total'])
            #print(df_available_strg.loc[(df_available_strg['clustername']==dictRow['target_cluster']) & (df_available_strg['name']==dictRow['t3_datastore']),["availablestorage"]],"\n")

    df_available = pd.merge(df_available_cmpt, df_available_strg, left_on='cluster',right_on='clustername', how='left')
    df_available.drop(['clustername','vcenter_y'], axis=1, inplace = True)
    df_available.rename(columns={'cluster': 'clustername','vcenter_x': 'vcenter','name': 'datastore_name','preferredhost': 'preferred_host'}, inplace=True)

    df_available['datastore_name'] = df_available['datastore_name'].astype(str)
    df_available['mirrored'] = df_available['mirrored'].astype(str)

    # Set Capacity Update DateTime
    today = str(datetime.now()).split(".")[0]

    df_available["updated"] = today 

    #return df_available
    print("START:",str(datetime.now()).split(".")[0])
    dbRefreshCapacity(df_available)
    print("END:",str(datetime.now()).split(".")[0])

    capacity_dict["capacity_status"] = "Updated"    
    capacity_dict["updated_at"] = today              
    #jobconf.update(job_hostconf)

    return capacity_dict

# Check Compute Capacity
def CheckCompute(job,compute):
    print("CHECK COMPUTE for ATIII-V","\n")
    tgt_compute = compute.loc[
        (compute['clustername']      == str(job['target_cluster']).upper()) & \
        (compute['availablecpu']    >= int(job['vcpus']))  &          \
        (compute['availablemem']    >= int(job['vram']))           \
        ]
    tgt_compute.sort_values(by=['availablecpu'], ascending=False, inplace = True)
    #print(tgt_compute.info())
    print(tgt_compute.head())
    print("ATIII-V COMPUTE EMPTY:", compute.empty, "\n")
    return tgt_compute

# Check Compute Capacity
def GetClusters(cls):
    print("TARGET CLUSTER:",cls,"\n")
    # Get All Capacity Data
    df_capdata = dbGetCapacity()
    print("CLUSTER CAPACITY DATA","\n", df_capdata.head(),"\n")
    df_resources = df_capdata[df_capdata["clustername"].isin(cls)]
    df_hosting = df_resources.copy()
    # dropping ALL duplicate values
    df_hosting.drop_duplicates(subset="clustername", inplace = True)
    df_hosting.sort_values(by=['availablecpu','availablemem','availablestorage'], ascending=False, inplace = True)
    #print(df_hosting.head(),"\n")
    print("Records:", len(df_hosting), "\n")

    return df_hosting

# Get capacity Config Settings
def CheckCapacity(data):


    # Check T2 Capacity    
    def CheckT2Storage(job,compute):

        if job['business_unit'].upper() == "SANTAM":
            if job['app_tier'].upper() in notmirrored:
                if job['app_type'].upper() == "MSSQL":
                    mirror_tag = "NotMirrored"
                else:
                    mirror_tag = "NotMirrored"
            else:
                mirror_tag = "Mirrored"            
        else:
            if job['app_tier'].upper() == "ATIII":
                mirror_tag = "Mirrored"
            else:
                mirror_tag = "NotMirrored"

        df_t2storage = compute.loc[
            (compute['clustername'] == str(job['target_cluster']).upper()) & \
            (compute['tier'] == 2) &          \
            (compute['mirrored'] == mirror_tag) &          \
            (compute['availablestorage'] >= int(job['t2total']))           \
            ]
        print("T2_STORAGE", df_t2storage.empty)
        df_t2storage.sort_values(by=['availablestorage'], ascending=True, inplace = True)
        df_t2storage.rename(columns={'datastore_name': 't2_datastore'}, inplace=True) 
        #print(df_t2storage.info())
        
        df_available_t2strg = df_t2storage[['clustername','t2_datastore']].copy() #'index',,'availablestorage'
        df_available_t2strg.set_index(['clustername','t2_datastore'])
        print(df_available_t2strg)

        return df_available_t2strg
    # Check T3 Capacity    
    def CheckT3Storage(job,compute):
        
        if job['business_unit'].upper() == "SANTAM":
            if job['app_tier'].upper() in notmirrored:
                if job['app_type'].upper() == "MSSQL":
                    mirror_tag = "NotMirrored"
                else:
                    mirror_tag = "NotMirrored"     
            else:
                mirror_tag = "Mirrored"                         
        else:
            if job['app_tier'].upper() == "ATIII":
                mirror_tag = "Mirrored"
            else:
                mirror_tag = "NotMirrored"

        df_t3storage = compute.loc[
            (compute['clustername'] == str(job['target_cluster']).upper()) & \
            (compute['tier'] == 3) &          \
            (compute['mirrored'] == mirror_tag) &          \
            (compute['availablestorage'] >= int(job['t3total']))           \
            ]
        print("T3_STORAGE", df_t3storage.empty)
        df_t3storage.sort_values(by=['availablestorage'], ascending=True, inplace = True)
        df_t3storage.rename(columns={'datastore_name': 't3_datastore'}, inplace=True)
        #print(df_t3storage.info())
        
        df_available_t3strg = df_t3storage[['clustername','t3_datastore']].copy() #'index',,'availablestorage'
        df_available_t3strg.set_index(['clustername','t3_datastore'])
        print(df_available_t3strg)
        return df_available_t3strg

    #print("JOB","\n", job)
    capacity_dict = {}
    capacity_dict.update({'capacity_comments':  ""})

    data["target_cluster"] = str(data["clustername"]).upper()
    data.pop("clustername")
    data["app_tier"] = str(data["app_tier"]).upper()

    compute_dict = {}
    df_capdata = dbGetCapacity()
    df_capdata['availablecpu'] = df_capdata['availablecpu'].astype(int)
    df_capdata['availablemem'] = df_capdata['availablemem'].astype(int)    
    #print("\n", df_capdata.info(), "\n")
    #print("\n", df_capdata.tail(10), "\n")

    df_available_cmpt = CheckCompute(data,df_capdata)

    df_available_cmpt.set_index(['clustername'])

    # Select specific compute columns for final capacity results
    df_computecap = df_available_cmpt[['clustername','preferred_host']].copy()
    if df_computecap.empty:
        no_compute = True
    else:
        no_compute = False
    print("NO COMPUTE ?", no_compute, "\n", df_computecap.info())    
    df_computecap.set_index(['clustername'])
    
    #try:
    if no_compute:
        capacity_dict.update({'compute_ok': False})
        capacity_dict.update({'clustername': data["target_cluster"]})
        capacity_dict.update({'capacity_comments':  capacity_dict['capacity_comments'] + str(data['vcpus']).upper() + " vCPUS and " + str(data['vram']) + " GB vRAM required in " + str(data["target_cluster"]).upper()+ "; "})
    #except:
    else:
        df_computecap  = df_computecap.iloc[0]
        compute_dict = df_computecap.to_dict() 
        capacity_dict.update({'compute_ok': True})
    print(compute_dict, "\n")

    # Check T2 Storage
    t2storage_dict = {}
    df_t2storage = CheckT2Storage(data,df_available_cmpt)
    if df_t2storage.empty:
        t2storage_dict.update({'t2_datastore' : ""})
        if int(data['t2total']) > 0:
            capacity_dict.update({'t2storage_ok': False})
            capacity_dict.update({'capacity_comments':  capacity_dict['capacity_comments'] + str(data['app_tier']).upper() + " Datastore for " + str(data['t2total']) + " GB Tier2 Storage required in " + str(data["target_cluster"]).upper()+ "; "})
        else:
            capacity_dict.update({'t2storage_ok': True})
    else:
        if int(data['t2total']) > 0:
            df_t2storage  = df_t2storage.iloc[0]
            t2storage_dict = df_t2storage.to_dict()
            capacity_dict.update({'t2storage_ok': True})
        else:
            t2storage_dict.update({'t2_datastore' : ""})
            capacity_dict.update({'t2storage_ok': True})    # True as T3 requested = 0

    print(t2storage_dict, "\n")

    # Check T3 Storage
    t3storage_dict = {}
    df_t3storage = CheckT3Storage(data,df_available_cmpt)
    #try:
    if df_t3storage.empty:
        t3storage_dict.update({'t3_datastore' : ""})
        if int(data['t3total']) > 0:
            capacity_dict.update({'t3storage_ok': False})
            capacity_dict.update({'capacity_comments':  capacity_dict['capacity_comments'] + str(data['app_tier']).upper() + " Datastore for " + str(data['t3total']) + " GB Tier3 Storage required in " +str(data["target_cluster"]).upper()+ "; " })
        else:
            capacity_dict.update({'t3storage_ok': True})
    #except:
    else:
        if int(data['t3total']) > 0:
            df_t3storage  = df_t3storage.iloc[0]
            t3storage_dict = df_t3storage.to_dict()   
            capacity_dict.update({'t3storage_ok': True})
        else:
            t3storage_dict.update({'t3_datastore' : ""})
            capacity_dict.update({'t3storage_ok': True})    # True as T3 requested = 0

    print(t3storage_dict, "\n")   
    
    # Set Storage OK Status
    if capacity_dict['t2storage_ok'] and capacity_dict['t3storage_ok']:
        capacity_dict.update({'storage_ok': True})
    else:
        capacity_dict.update({'storage_ok': False})    
    
    capacity_dict.update(compute_dict)

    print("CAPACITY RESULT:", "\n")
    #print(capacity_dict,"\n")
    capacity_dict.update(t2storage_dict)
    #print(capacity_dict,"\n")
    capacity_dict.update(t3storage_dict)
    capacity_dict['vcpus'] = int(data['vcpus'])
    capacity_dict['vram'] = int(data['vram'])
    capacity_dict['t2total'] = int(data['t2total'])
    capacity_dict['t3total'] = int(data['t3total'])    
    print(capacity_dict,"\n")

    # Remove unnecessary data keys
    pop_list = []
    pop_list = ['vcpus','vram','t2total','t2storage_ok','t3total','t3storage_ok']
    print([capacity_dict.pop(key) for key in pop_list])           

    return capacity_dict