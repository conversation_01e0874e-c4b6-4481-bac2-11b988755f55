import sys, json, time, requests
from rich import print
import config
# Assign config data to local variable
config = config.conf()
from config import AppConfig

jobconf = {}

server = config['IPAM']['IPAM_HOST'] + ":" + config['IPAM']['IPAM_PORT']
ipamapi = config['IPAM']['IPAM_API']
username = AppConfig.IPAM_USER
password = AppConfig.IPAM_PASSWORD
appid = AppConfig.IPAM_APPID

baseurl = server + ipamapi + appid
##print(baseurl)


# Obtain API Token
def loginIpam():
	##print()
	##print('Log In for API Token...')
	res = requests.post(baseurl + '/user/', auth=(username, password))
	tkn = json.loads(res.content)['data']['token']
	#print(tkn)
	return tkn

# Obtain target subnet information for further requests
def SubnetInfo(job):
    #print("Get Subnet Info...")
    subnetcidr = str(job["ip_cidr"])
    apitoken = loginIpam()
    res = requests.get(baseurl + '/subnets/search/' + subnetcidr + "/", headers={'token': apitoken})
    print(res.status_code)
    if res.status_code == 200:
        #print(res.content)
        subnetinfo = json.loads(res.content)
        if subnetinfo["success"]:
            #subnetinfo = json.loads(res.content)['data'][0]
            subnetinfo["cidr_ok"] = True
            return subnetinfo
        else:
            subnetinfo["cidr_ok"] = False
            return subnetinfo            
    else:
        subnetinfo = json.loads(res.content)
        subnetinfo["cidr_ok"] = False
        return subnetinfo        


# Obtain target subnet information for further requests
# Requires json with cidr and "GET or SET" action.
def NextFreeIP(job,action):
    ##print("Get First Free IP...")
    apitoken = loginIpam()
    subnetinfo = SubnetInfo(job)
    if subnetinfo["cidr_ok"]:
        subnetdata = subnetinfo['data'][0]
        #print(subnetdata)
        subnetid = subnetdata['id']
        if action.lower() == "get":
            res = requests.get(baseurl + '/subnets/' + subnetid + "/first_free/", headers={'token': apitoken})
        if action.lower() == "set":
            data = {"hostname":str(job['vm_name']).lower(),"is_gateway" : 0,"excludePing" : 0,"PTRignore" : 1,"note": str(job['environment'])}
            res = requests.post(baseurl + '/addresses/first_free/' + subnetid + '/', headers={'token': apitoken}, data=data)
        print("\n","IP STATUS CODE", res.status_code)
        print("IP RESPONSE", res.content)

        if res.status_code == 200:
            ##print(res.content)
            free_ip = json.loads(res.content)
            ##print(free_ip)
            free_ip["ip_ok"] = True
            free_ip["ip_address"] = free_ip["data"]
            
            return free_ip
        else:
            free_ip = json.loads(res.content)
            free_ip["ip_ok"] = False        
            return free_ip
    else:
        subnetinfo["ip_ok"] = False
        return subnetinfo


