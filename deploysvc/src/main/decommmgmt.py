import sys, json, requests, math #time,
from datetime import date, time, datetime, timedelta
import logging
# Import pandas
import pandas as pd
import numpy as np
from src.main.deploymgmt import getJobData

from src.utils import key_exists, BuildAndQuery, BuildUpdateQuery, recast_json
# Import Config Reference Data
from src.main.configsref import ValidateRequest, GetHostingConfig, GetOSConfig

# Import Database module
from src.main.dbmgmt import dbSelectQuery, dbAddDecommRequest, dbAddDecommJob, dbUpdateDecommJob, dbUpdateQuery

'''
# Import Available Resources Data
from src.main.capacity2 import CapacityData, StorageCapacity, CheckCapacity, GetClusters


 dbGetJobLogs, dbAddJobLog, dbUpdateJobLog, dbAddRequest, dbUpdateRequest, dbAddJob, dbUpdateJob, dbGetNextJob, dbGetAllJobs
'''

import config
# Assign config data to local variable
config = config.conf()
from config import AppConfig

#for_testing = config['NAMEGEN']['FOR_TESTING']

server_cooldown = 7 # days
vdi_cooldown = 3 # days


df_request = pd.DataFrame()
df_confdata = pd.DataFrame()

dict_requested = {}
job_specs = {}

# Round Up to Base
def roundup(x,base):
    return int(math.ceil(x / base))*base

class NpEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, np.integer):
            return int(obj)
        if isinstance(obj, np.floating):
            return float(obj)
        if isinstance(obj, np.bool_):
            return super().encode(bool(obj))
        if isinstance(obj, np.ndarray):
            return obj.tolist()
        return super(NpEncoder, self).default(obj)

# Search for Value X in Key Y from List of Dict Z
def getDictByValue(x,y,dct):
    result = next(z for z in dct if z[y]==x)
    return result

def AddJobLog(issue_type,data_dict):
    today = str(datetime.now()).split(".")[0]
    #print("Issue Data", json.dumps(data_dict, indent = 2),"\n") 
    #for item in data_dict['issues']:
    for x in range(len(data_dict['issues'])):
        logdata = {
            "log_added" : today,
            "request_jobid" : data_dict['request_jobid'],
            "vm_name" : data_dict['vm_name'],
            "job_stage" : data_dict['job_stage'],
            "log_type" : "ISSUE",
            "job_comments" : str(data_dict['issues'][x]),
            "log_status" : "OPEN",
            "log_updated" : today,
            "log_update" : str(today) + " New issue added",
            "issue_type": str(issue_type).upper(),
            "issue_ref" : "",
            "automatic" : False
        }
        #dbAddJobLog(logdata)
    return

# UPdate Decomm Request
def updateDecomm(request_data):
    timestamp = str(datetime.now()).split(".")[0] 
    request_data['decomm_stage'] = "SCHEDULED"
    request_data['decomm_status'] = "PENDING"

    dct_updated = request_data.copy()

    return dct_updated

# Split Instance Names:
def getNames(name_request):
    print("GET DECOMM INSTANCES","\n")
    hostnames = []
    # Check if VM Name was supplied, split into list object else set to blank.
    if key_exists(name_request,"hostname"):
        try:
            hostnames = list(filter(None,str(name_request["hostname"]).upper().strip().split(',')))                  
        except ValueError as e:
            #name_request["instance_names"] = ""
            print("VM Name error ", e, "\n")
    else:
        name_request["hostname"] = []

    return hostnames

# Get Decomm Job Details
def getDecommJobSpecs(data):

    result = {}
    annotations = {}

    # Virtual Server Decomms 
    if str(data['instance']).lower() == "server":
        cooldown_days = server_cooldown
        # Build SQL SELECT WHERE string
        select = "server = '" + str(data['hostname']).lower() + "';"
        df_infraview = dbSelectQuery('infraview',select)
        if not df_infraview.empty:
            df_infraview = df_infraview[0:1].copy()
            dct_infraview = df_infraview.to_dict(orient='records')
            data['competency'] = str(dct_infraview[0]['competency']).upper()

            print("DECOMM ANNOTATIONS","\n",dct_infraview)
            
            annotations['app_tier'] = dct_infraview[0]['app_tier']
            annotations['sla'] = dct_infraview[0]['sla']
            annotations['application'] = dct_infraview[0]['application']
            annotations['app_owner'] = dct_infraview[0]['app_owner']
            annotations['primary_techowner'] = dct_infraview[0]['primary_techowner']
            annotations['decommissioned'] = dct_infraview[0]['decommissioned']
            annotations['operating_system_type'] = dct_infraview[0]['operating_system_type']
            annotations['model'] = dct_infraview[0]['model']
            annotations['location'] = dct_infraview[0]['location']
        result['success'] = True
        data['decomm_comments'] = "New server decomm request."

    # VDI Decomms
    elif str(data['instance']).lower() == "vdi":
        data['competency'] = "GIJIMA"
        cooldown_days = vdi_cooldown
        result['success'] = True
        data['decomm_comments'] = "New VDI decomm request."

    # Other Decomms
    else:
        data['competency'] = "BCX CPS"
        result['success'] = True
        cooldown_days = server_cooldown
        data['decomm_comments'] = "New untagged decomm request."

    if not data['cooldown']:
        cooldown_days = 0
        
    data['cooldown_days'] = cooldown_days
    start_date = datetime.strptime(str(data['start_date']), '%Y-%m-%d')
    final_date = str(start_date + timedelta(days=cooldown_days)).split(" ")[0]
    data['final_date'] = final_date
    

    result['jobspecs'] = data
    result['annotations'] = annotations
    print("NEW DECOMM JOB","\n", json.dumps(result,indent=2,sort_keys=True),"\n")
    return result

# Process Deployment Request
def AddDecommRequest(request_data):
    print("DECOMM REQUEST","\n")
    # Validate Request
    response = {}
    issues = []
    issue_count = 0
    remediation = False
    timestamp = str(datetime.now()).split(".")[0]
    request_data['requested_on'] = timestamp
    updated_decomm = updateDecomm(request_data)

    # Extract or Generate VM Names
    names = getNames(request_data)
    jobs = []
    dct_job = {}
    job_issues = 0
    for x in range(0,len(names)):
        #print("JOB_ID", job['request_jobid'])
        tmp_job = request_data.copy()
        jobid = str(request_data["reference"]).upper() + "_" + str(x+1)
        tmp_job['decomm_jobid'] = jobid
        print("JOB_ID", tmp_job['decomm_jobid']," NAME:", names[x])
        tmp_job.update({"hostname":names[x]})
        job = getDecommJobSpecs(tmp_job)
        if not job['success']:
            job_issues += 1
            dct_job.update({"decomm_jobid":jobid,"success":False,"comment":"Invalid Job Details"})
            jobs.append(dct_job)
        else:
            jobs.append(job['jobspecs'])
        response['job_issues'] = job_issues


    response['timestamp'] = timestamp
    response['updated_decomm'] = updated_decomm
    response['decomm_specs'] = jobs
    

    #print("NEW REQUEST","\n", json.dumps(newrequest,indent=2,sort_keys=True),"\n")
    if job_issues == 0:
        response['success'] = True
        dbAddDecommRequest(updated_decomm)
        for item in jobs:
            print("DECOMM JOB",item, "\n")
            dbAddDecommJob(item)
            print("ADDED DECOMM JOB",item['decomm_jobid'], "\n")
    else:
        response['success'] = False
    print("DONE!","\n")

    
    return response


# GetAllJobs in Queue
def GetAllDecommJobs(data):
    dct_decommsdata = {}
    print("GET_ALL_JOBs DATA","\n", json.dumps(data, indent=2, sort_keys=True),"\n")

    query = {   
        "decomm_jobid" : str(data['decomm_jobid']).lower(),
        "hostname" : str(data['hostname']).lower(),
        "is_virtual" : data['is_virtual'],
        "competency" : str(data['competency']).lower(),
        "instance" : str(data['instance']).lower(),
        "decomm_stage" : str(data['decomm_stage']).lower(),
        "decomm_status" : str(data['decomm_status']).lower()
    }
    select = BuildAndQuery(data)
    if select == ";":
        select = "decomm_jobid like '%';"
    print("SELECT", select,"\n")
    df_decommjobs = dbSelectQuery('decommjobs',select)
    print("DECOMM JOBS DF RESULT","\n",df_decommjobs.head(10),"\n")
    num_rows = df_decommjobs.shape[0]
    if num_rows > 0:
        #df_buildjobs['updated_datetime'] = pd.to_datetime(df_buildjobs['updated_datetime'], errors='coerce').fillna(0)
        df_decommjobs['requested_on'] = df_decommjobs['requested_on'].astype(str)
        df_decommjobs['start_date'] = df_decommjobs['start_date'].astype(str)
        df_decommjobs['final_date'] = df_decommjobs['final_date'].astype(str)
        df_decommjobs['updated_datetime'] = df_decommjobs['updated_datetime'].astype(str)
        df_decommjobs['completed_datetime'] = df_decommjobs['completed_datetime'].astype(str)

    dct_decommjobs = df_decommjobs.to_dict(orient='records')
    dct_decommsdata.update({"count" : num_rows})
    dct_decommsdata.update({"jobs" : dct_decommjobs})
    #dct_decommdata.update({"query" : jobsquery})
    print("DECOMM JOBS JSON RESULT","\n", json.dumps(dct_decommsdata, indent=2),"\n")
    
    return dct_decommsdata


# Update DecommJob
def UpdateDecommJob(data):
    dt = datetime.today()
    today = dt.date()
    timestamp = str(datetime.now()).split(".")[0]

    print("UPDATE_JOB DATA","\n", json.dumps(data, indent=2),"\n")
    qry_target = "decomm_jobid ='"+str(data['decomm_jobid']).upper()+"'"
    df_currentjob = dbSelectQuery('decommjobs', qry_target)
    dict_result, dct_decommreq, dct_decommjob = {},{},{}
    dct_update = {}
    if df_currentjob.empty:
        dict_result['success'] = False
    else:
        upd_targetref = "reference ='"+df_currentjob['reference'][0]+"'"
        df_currentreq = dbSelectQuery('decommrequests', upd_targetref)

        dct_decommreq = df_currentreq.to_dict(orient='records')
        dict_result['success'] = True

        print("CURRENT JOB","\n", df_currentjob.head(), "\n")
        if str(data['decomm_status']).upper() in ['CANCELLED']:
            print("STATUS = CANCELLED","\n")
            upd_data = {
                "decomm_stage" : "CLOSED",
                "decomm_status" : "CANCELLED",
                "decomm_comments" : "Decomm was CANCELLED.",
                "updated_datetime" : timestamp,
                "completed_datetime" : timestamp
            }
            qry_decommjob = BuildUpdateQuery(upd_data)
            dbUpdateQuery("decommjobs",qry_decommjob, upd_targetref)        
            df_output = dbSelectQuery('decommjobs', upd_targetref)
            df_decommjob = recast_json(df_output)

            # UPDATE REQUEST STATUS
            dct_decommreq['decomm_stage'] = "CLOSED"
            dct_decommreq['decomm_status'] = "CANCELLED"
            dct_decommreq['completed_datetime'] = timestamp
            
            qry_decommreq = BuildUpdateQuery(upd_data)
            dbUpdateQuery("decommrequests",qry_decommreq, upd_targetref)

            df_output = dbSelectQuery('decommrequests', upd_targetref)
            df_decommreq = recast_json(df_output)
            if not df_decommreq.empty:
                dict_result['decommreq'] = df_decommreq.to_dict(orient='records')
            # UPDATE JOBS STATUS
            dct_decommjob['decomm_stage'] = "CLOSED"
            dct_decommjob['decomm_status'] = "BACKED_OUT"
            dct_decommjob['completed_datetime'] = timestamp
            dct_decommjob['updated_datetime'] = timestamp
            dct_decommjob['decomm_comments'] = data['decomm_comments']

        else:
            
            print("STATUS =",df_currentjob['decomm_status'][0],"\n")
            data['updated_datetime'] = timestamp
            if key_exists(data,"start_date"):
                print("CURRENT START = ",df_currentjob['start_date'][0])
                
                try:
                    start_date = datetime.strptime(data['start_date'],'%Y-%m-%d %H:%M:%S').date()
                except:
                    start_date = datetime.strptime(data['start_date'],'%Y-%m-%d').date()
                

            if key_exists(data,"cooldown"):
                print("COOLDOWN:",data["cooldown"])
                    # Virtual Server Decomms 
                if str(df_currentjob['instance'][0]).lower() == "server":
                    cooldown_days = server_cooldown
                elif str(df_currentjob['instance'][0]).lower() == "vdi":
                    cooldown_days = vdi_cooldown
                if bool(data["cooldown"]): 
                    final_date = str(start_date + timedelta(days=cooldown_days)) #timedelta(days=int(df_currentjob['cooldown_days'][0]))).split(" ")[0]
                else:
                    final_date = start_date
                data['final_date'] = final_date
            if start_date <= today:
                print(f"OVERDUE: {str(start_date)} already passed!")
                start_date = str(today)
                data['start_date'] = start_date
                data['decomm_stage'] = "SCHEDULED"
                data['decomm_status'] = "PENDING"
                data['final_date'] = final_date
            print("NEW START = ",str(start_date))
            print("NEW FINAL = ",str(final_date),"\n")

            print("UPDATED DECOMM:\n",data)
            qry_decommjob = BuildUpdateQuery(data)            
            ### Syntax: dbUpdateQuery(table, column, target)
            dbUpdateQuery("decommjobs",qry_decommjob, qry_target)
            df_decommjob = dbSelectQuery('decommjobs', qry_target)
            df_decommjob = recast_json(df_decommjob)
            
            if key_exists(data,"decomm_jobid"):
                data.pop("decomm_jobid")
            if key_exists(data,"updated_datetime"):
                data.pop("updated_datetime")
            if key_exists(data,"decomm_comments"):
                data.pop("decomm_comments")
            if key_exists(data,"final_date"):
                data.pop("final_date")
            qry_decommreq = BuildUpdateQuery(data)  
            ### Syntax: dbUpdateQuery(table, column, target)
            dbUpdateQuery("decommrequests",qry_decommreq, upd_targetref)
            df_request = dbSelectQuery('decommrequests', upd_targetref)
            df_request = recast_json(df_request)


        if not df_decommjob.empty:

            dict_result['decommreq'] = df_request.to_dict(orient='records')
            dict_result['decommjob'] = df_decommjob.to_dict(orient='records')
            dict_result['success'] = True
        else:
            dict_result['success'] = False

    return dict_result

# Update Decomm Request
def UpdateDecommRequest(ref,startdate,stage,status):
    # Set Values
    req_target = "reference = '{}'".format(str(ref))
    dct_decommreq = {}
    dct_decommreq['start_date'] = startdate
    dct_decommreq['decomm_stage'] = stage
    dct_decommreq['decomm_status'] = status              
    qry_decommreq = BuildUpdateQuery(dct_decommreq)
    dbUpdateQuery("decommrequests",qry_decommreq, req_target)

    print("DECOMM REQUEST UPDATE!", "\n")
    return

# Update DecommJob
def ManageDecommJobs():

    dt = datetime.today()
    today = dt.date()
    timestamp = str(datetime.now()).split(".")[0]
    #data['updated_datetime'] = timestamp
    dict_result, dct_decommreq, dct_decommjob = {},{},{}
    def setCooldown(tdate):
        select = "decomm_status = '{}' and start_date <= '{}'".format("PENDING", tdate)
        print("SELECT", select,"\n")
        df_jobstoday = dbSelectQuery('decommjobs',select)
        print("COOLDOWN JOBS DF RESULT","\n",df_jobstoday.head(10),"\n")
        #df_jobstoday = df_decommjobs.loc[(df_decommjobs['start_date'] <= str(today))]
        if not df_jobstoday.empty:
            #print("DECOMM JOBS TODAY","\n",df_jobstoday.head(10),"\n")
            dct_decommjob = df_jobstoday.to_dict(orient='records')
            print(dct_decommjob, "\n")

            for x in range(0,len(dct_decommjob)):
                upd_data = {
                    "decomm_jobid" : dct_decommjob[x]['decomm_jobid'],
                    "start_date" : tdate,
                    "cooldown" : dct_decommjob[x]['cooldown'],
                    "decomm_stage" : "COOLDOWN",
                    "decomm_status" : "IN_PROGRESS",
                    "decomm_comments" : "Decomm now in COOLDOWN period."
                }
                
                UpdateDecommJob(upd_data)

                UpdateDecommRequest(dct_decommjob[x]['reference'],"","COOLDOWN","IN_PROGRESS")

                print("COOLDOWN:", upd_data, "\n")

            return {"success":True}
        else:
            return {"success":False}

    def setHousekeeping(tdate):
        select = "decomm_status = '{}' and final_date <= '{}'".format("IN_PROGRESS", tdate)
        print("SELECT", select,"\n")
        df_jobstoday = dbSelectQuery('decommjobs',select)
        print("HOUSEKEEPING JOBS DF RESULT","\n",df_jobstoday.head(10),"\n")
        #df_jobstoday = df_decommjobs.loc[(df_decommjobs['start_date'] <= str(today))]
        if not df_jobstoday.empty:
            #print("DECOMM JOBS TODAY","\n",df_jobstoday.head(10),"\n")
            dct_decommjob = df_jobstoday.to_dict(orient='records')
            print(dct_decommjob, "\n")

            for x in range(0,len(dct_decommjob)):
                upd_data = {
                    "decomm_jobid" : dct_decommjob[x]['decomm_jobid'],
                    "start_date" : tdate,
                    "completed_datetime" : timestamp,
                    "cooldown" : dct_decommjob[x]['cooldown'],
                    "decomm_stage" : "HOUSEKEEPING",
                    "decomm_status" : "COMPLETED",
                    "decomm_comments" : "Decomm COMPLETED, HOUSEKEEPING tasks."
                }
                
                UpdateDecommJob(upd_data)

                UpdateDecommRequest(dct_decommjob[x]['reference'],"","HOUSEKEEPING","CLOSED")

                print("HOUSEKEEPING:", upd_data, "\n")

            return {"success":True}
        else:
            return {"success":False}

    cooldown_status = setCooldown(str(timestamp))
    print("Decomms Found?", cooldown_status['success'])
    cooldown_status = setHousekeeping(str(timestamp))
    print("Decomms Found?", cooldown_status['success'])

    return
