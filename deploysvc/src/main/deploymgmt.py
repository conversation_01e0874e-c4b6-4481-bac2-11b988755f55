import sys, json, requests, math #time,
from rich import print
from datetime import date, time, datetime, timedelta
import logging
# Import pandas
import pandas as pd
import numpy as np

from src.utils import key_exists
# Import Config Reference Data
from src.main.configsref import ValidateRequest, GetHostingConfig, GetOSConfig
# Import Config Reference Data
from src.main.resources import GetResources
# Import NameGen module
from src.main.namegen import getNextName
# Import IPAM module
from src.main.ipam2 import NextFreeIP 
# Import Database module
from src.main.dbmgmt import dbAddRequest, dbAddJob, dbAddJobLog, dbSelectQuery #dbGetRequestStatus

'''
# Import Available Resources Data
from src.main.capacity2 import CapacityData, StorageCapacity, CheckCapacity, GetClusters


 dbGetJobLogs, dbAddJobLog, dbUpdateJobLog, dbAddRequest, dbUpdateRequest, dbAdd<PERSON>ob, dbUpdateJob, dbGetNextJob, dbGetAllJobs
'''

import config
# Assign config data to local variable
config = config.conf()
from config import AppConfig

for_testing = config['NAMEGEN']['FOR_TESTING']
mem_baseline = 32
memswap_ratio = 1.5
windows_osdisksize = 120
winvdi_osdisksize = 100

df_request = pd.DataFrame()
df_confdata = pd.DataFrame()
df_cpctmgmt = pd.DataFrame()
df_hosting = pd.DataFrame()
df_compcpct = pd.DataFrame()
df_compute = pd.DataFrame()
df_hosting = pd.DataFrame()
df_storage = pd.DataFrame()
df_computeconf = pd.DataFrame()
df_osconf = pd.DataFrame()
df_jobspec = pd.DataFrame()
df_requested = pd.DataFrame()
df_available_cmpt = pd.DataFrame()
df_available_strg = pd.DataFrame()
df_available = pd.DataFrame()
df_capacity = pd.DataFrame()
df_resources = pd.DataFrame()
tgt_compute = pd.DataFrame()
tgt_hosting = pd.DataFrame()
tgt_hostconf = pd.DataFrame()
tgt_osconfig = pd.DataFrame()
tgt_capacity = pd.DataFrame()
dict_requested = {}
job_specs = {}
job_hostconf = {}
job_osconf = {}
job_capacity = {}
job_storage = {}
tgt_hostconfig = {}

updates = {}

clusters = []
#job_keys_remove = ['vm_count','compute_status','aci_tenant','disk_type','disk_blocksize','t2storage_status','t3storage_status','active','ad_domain','anp','bridged_domain','client_tag','active_status','comment','compute_ok','cluster','tenant','request_id','cpu_sockets','cpu_ratio','epg','index','ip_ok','max_vcpu','max_vram','os_edition','os_disksize','requested_by','sla','storage_ok','storage_status','t2storage_ok','t3storage_ok']

# Round Up to Base
def roundup(x,base):
    return int(math.ceil(x / base))*base

class NpEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, np.integer):
            return int(obj)
        if isinstance(obj, np.floating):
            return float(obj)
        if isinstance(obj, np.bool_):
            return super().encode(bool(obj))
        if isinstance(obj, np.ndarray):
            return obj.tolist()
        return super(NpEncoder, self).default(obj)

# Search for Value X in Key Y from List of Dict Z
def getDictByValue(x,y,dct):
    result = next(z for z in dct if z[y]==x)
    return result


# Update Request Specs
def UpdateJobSpecs(job_data):
    dct_clean = job_data.copy()
    #print("UpdateJobSpecs Data BEFORE", json.dumps(dct_clean, indent = 2, sort_keys=True),"\n") 
    #dct_clean['target_cluster'] = dct_clean['cluster']
    job_data['start_datetime'] = job_data['submit_datetime']
    job_data['job_comments'] = job_data['issues']
    #''' ,'index'
    pop_list = ['aci_tenant','active','active_status','ad_domain','anp','bridged_domain','client_tag','cluster','comment',
        'cpu_ratio','cpu_sockets','disk_blocksize','disk_type','epg','issues','max_vcpu','max_vram','os_disksize','os_edition',
        'requested_by','request_status','request_valid','sla','submit_datetime','tenant','updated','vm_count']
    try:
        job_data.pop('clustername')
    except:
        pass
    if job_data['job_comments'] != "":
        job_issues = ""
        for item in job_data['job_comments']:
            job_issues = job_issues + str(item) + ";"
        job_data['job_comments'] = job_issues
    print([job_data.pop(key) for key in pop_list]) #,'clustername'
    timestamp = str(datetime.now()).split(".")[0] 
    job_data['updated_datetime'] = timestamp
    job_data['t2storage_disks'] = str(job_data['t2storage_disks'])
    job_data['t3storage_disks'] = str(job_data['t3storage_disks'])
    job_data['t2storage_drives'] = str(job_data['t2storage_drives'])
    job_data['t3storage_drives'] = str(job_data['t3storage_drives'])
    job_data['t2storage_labels'] = str(job_data['t2storage_labels'])
    job_data['t3storage_labels'] = str(job_data['t3storage_labels'])

    dct_clean = job_data.copy()
    #print("UpdateJobSpecs Data AFTER", json.dumps(dct_clean, indent = 2, sort_keys=True),"\n") 
    return dct_clean

# Clean and Submit Request
def PrepRequestSubmit(request_data):
    print("PrepRequestSubmit Data BEFORE", json.dumps(request_data, indent = 2, sort_keys=True),"\n") 
    
    pop_list = ['cluster','ip_cidr','os_disk','t2storage_drives','t3storage_drives','t2storage_disks','t3storage_disks','t2storage_labels','t3storage_labels','t2total','t3total']
    print([request_data.pop(key) for key in pop_list])
    today = str(datetime.now()).split(".")[0]
    duedate = str(datetime.now() + timedelta(days=10)).split(".")[0]
    request_data['request_phase'] = "DEPLOYMENT"
    request_data['competency'] = str(request_data['competency']).upper()
    #request_data['request_status'] = "READY"

    request_data['complete_datetime'] = duedate
    request_data['updated_datetime'] = today
    request_data['hosting_location'] = ""
    request_data['request_comments'] = ""
    request_data['manual_actions'] = ""
    request_data['vm_partner'] = ""
    #request_data['vm_placement'] = ""
    request_data['ref_subid'] = str(request_data["ref_subid"]).upper()
    request_data['override_standards'] = False

    
    if request_data['request_comments'] != []:
        request_issues = ""
        for item in request_data['request_comments']:
            request_issues = request_issues + str(item) + ";"
        request_data['request_comments'] = request_issues
        request_data['request_status'] = "REMEDIATE"
    else:
        request_data['request_status'] = "READY"
        request_data['request_comments'] = ""

    submit_data = request_data.copy()

    return submit_data

# Update Request Specs
def UpdateRequestSpecs(request_data, hosting_data, osconfig_data):
    request_data['reference'] = str(request_data["reference"]).upper()
    if str(request_data["app_type"]).lower() in ['appliance','shell']:
        osdisksize = 0
        windows_osdisksize = 0
    else:
        osdisksize = int(osconfig_data['os_configs'][0]['os_disksize'])
        windows_osdisksize = 120
        
    request_data['t2drives'] = str(request_data["t2drives"]).upper()
    request_data['t3drives'] = str(request_data["t3drives"]).upper()

    request_data['ip_cidr'] = str(hosting_data['hosting_configs'][0]['ip_cidr'])
    ## Update OS Disk if Windows
    if "windows" in str(request_data["os_version"]).lower():
        request_data['os_type'] = "Windows"
        request_data['t2labels'] = str(request_data["t2labels"]).upper()
        request_data['t3labels'] = str(request_data["t3labels"]).upper()
        if request_data['vram'] > mem_baseline:
            updated_osdisksize = osdisksize + roundup(int(request_data['vram']*memswap_ratio),10)
            request_data['os_disk'] = updated_osdisksize
        else:
            updated_osdisksize = windows_osdisksize
            request_data['os_disk'] = updated_osdisksize
    else:
        updated_osdisksize = osdisksize
        request_data['os_disk'] = updated_osdisksize
        if "linux" in str(request_data["os_version"]).lower():
            request_data['t2labels'] = str(request_data["t2labels"]).lower()
            request_data['t3labels'] = str(request_data["t3labels"]).lower()
            request_data['os_type'] = "Linux"
        else:
            request_data['os_type'] = "Other"

    # Add OS Disk to preferred Tier Total
    t2_total = 0
    t2_disks = []
    t2_drives = []
    t2_labels = []

    #if int(request_data['t2storage']) != 0 or request_data['t2storage'] != "":
    if str(request_data['t2storage']) not in ['0','']:
      
        try:
            t2_list = str(request_data['t2storage']).split(',')            
            t2_total = t2_total + sum([int(x) for x in t2_list])
            t2_disks  = list(map(int,t2_list))
            #request_data['t2storage_disks'] = list(map(int,t2_list))
            t2_drives = str(request_data['t2drives']).upper().split(',')
            t2_labels = str(request_data['t2labels']).upper().split(',')

        except ValueError:
            t2_disks.append(int(request_data['t2storage']))
            t2_total = t2_total + int(request_data['t2storage'])
            t2_drives.append(request_data['t2drives'])
            t2_labels.append(request_data['t2labels'])
        
        request_data['t2storage_drives'] = t2_drives
        request_data['t2storage_disks'] = t2_disks
        request_data['t2storage_labels'] = t2_labels

        #request_data['t2total'] = int(request_data['t2total']) + updated_osdisksize
    else:
        request_data['t2total'] = t2_total
        request_data['t2storage_drives'] = t2_drives
        request_data['t2storage_disks'] = t2_disks
        request_data['t2storage_labels'] = t2_labels
        request_data['t2drives'] = ""
        #request_data['t2storage_total'] = ''

    #if int(request_data['t3storage']) != 0 or request_data['t3storage'] != '':
    t3_total = 0
    t3_disks = []
    t3_drives = []
    t3_labels = []

    if str(request_data['t3storage']) not in ['0','']:
      
        try:
            t3_list = str(request_data['t3storage']).split(',')            
            t3_total = t3_total + sum([int(x) for x in t3_list])
            t3_disks = list(map(int,t3_list))
            #request_data['t3storage_disks'] = list(map(int,t3_list))
            t3_drives = str(request_data['t3drives']).upper().split(',')
            t3_labels = str(request_data['t3labels']).upper().split(',')
            
        except ValueError:
            t3_disks.append(int(request_data['t3storage']))
            t3_total = t3_total + int(request_data['t3storage'])
            t3_drives.append(str(request_data['t3drives']).upper())
            t3_labels.append(request_data['t3labels'])

        request_data['t3storage_drives'] = t3_drives      
        request_data['t3storage_disks'] = t3_disks  
        request_data['t3storage_labels'] = t3_labels
        #request_data['t3total'] = int(request_data['t3total']) + updated_osdisksize
    else:
        #request_data['t3total'] = t3_total
        request_data['t3storage_drives'] = t3_drives
        request_data['t3storage_disks'] = t3_disks
        request_data['t3storage_labels'] = t3_labels
        request_data['t3drives'] = ""


    # If no additional storage requested, add os_disksize to storage total based on App Tier
    if str(request_data['app_tier']).upper() not in ['ATV','NONE']:
        #if str(request_data['t2storage']) not in ['0','']:
        #    request_data['t2total'] = t2_total + updated_osdisksize
        #    request_data['t3total'] = t3_total
        #else:
        if str(request_data['app_type']).upper() in ['VDI']:
            # Set default of 100GB for VDI Win10 OS Disk
            request_data['t2total'] = t2_total + winvdi_osdisksize 
            request_data['t3total'] = t3_total            
        else:
            if t2_total != 0:
                request_data['t2total'] = t2_total + updated_osdisksize
                request_data['t3total'] = t3_total
            else:
                request_data['t3total'] = t3_total + updated_osdisksize
                request_data['t2total'] = t2_total
                #request_data['t3total'] = t3_total

    else:
        # Allocate T3 for non-prod OS
        request_data['t3total'] = t3_total + updated_osdisksize
        request_data['t2total'] = t2_total

    # Get Target Cluster(s) from Hosting and update request
    all_clusters = []
    for cluster in hosting_data['hosting_configs']:
        #print(cluster['cluster'])
        all_clusters.append(cluster['cluster'])
    clusters = list(set(all_clusters))
    print("CLUSTERS",clusters,"\n")
    request_data['cluster'] = clusters
    # Add OS Config
    #request_data.update(osconfig_data['os_configs'][0])

    return request_data

# Check Capacity and Reserve
def getJobData(jobrequest):
    jobrequest['reference'] = str(jobrequest["reference"]).upper()
    jobs = []
    #response = {}
    jobconf = jobrequest.copy()
    jobconf['job_active'] = False
    jobconf['job_stage'] = "AD_OBJECT"
    jobconf['job_state'] = "NORMAL"
    jobconf['job_status'] = "PENDING"

    ## Update OS Disk if Windows
    if "windows" in str(jobconf["os_version"]).lower():
        jobconf['os_type'] = "Windows"
    elif "linux" in str(jobconf["os_version"]).lower():
        jobconf['os_type'] = "Linux"
        # Also Skip to VM_DEPLOY stage if Linux job
        jobconf['job_stage'] = "VM_DEPLOY"
    else:
        jobconf['os_type'] = "Other"
        # Also Skip to VM_DEPLOY stage if Other OS job
        jobconf['job_stage'] = "VM_DEPLOY"

    #print("JobConfig Data", json.dumps(jobconf, indent = 2),"\n") 
    #if str(jobrequest["app_tier"]).upper() in ["ATI","ATII"]:
    if str(jobrequest["app_tier"]).upper() in ["ATIII"] or str(jobrequest["app_type"]).upper() in ["VDI"]:
        # Generate jobspecs based on best capacity
        for x in range(0, int(jobrequest["vm_count"])):
            jobid = str(jobrequest["reference"]).upper() + "_" + str(x+1)
            jobconf['request_jobid'] = jobid
            jobconf['ref_subid'] = str(jobrequest["ref_subid"]).upper() 
            jobconf['vm_count'] = 1
            #jobcluster = []
            #jobcluster.append(jobrequest["cluster"])
            #obconf['cluster'] = jobcluster
            # Append Job to Build List
            dict_requested = jobconf.copy()
            jobs.append(dict_requested)  

    else:
        sitenum = 0
        # Generate jobspecs alternating between data center sites.
        for x in range(0, int(jobrequest["vm_count"])):
            
            jobid = str(jobrequest["reference"]).upper() + "_" + str(x+1)
            jobconf['request_jobid'] = jobid
            jobconf['ref_subid'] = str(jobrequest["ref_subid"]).upper()
            jobconf['vm_count'] = 1
            jobcluster = []
            jobcluster.append(jobrequest["cluster"][sitenum])
            jobconf['cluster'] = jobcluster #jobrequest["cluster"][sitenum]
            #print(jobconf['cluster'])
            # Append Job to Build List
            dict_requested = jobconf.copy()
            jobs.append(dict_requested)
            # Switch to other hosting location reference data
            if int(len(jobrequest["cluster"])) > 1:
                if sitenum == 0:
                    sitenum = 1
                else:
                    sitenum = 0 
                
    #response["jobdata"] = jobs
    return jobs

# Extract or Generate VM Names
def getVmName(name_request):
    print("NAME REQUEST","\n",name_request,"\n")

    vm_names = []
    # Check if VM Name was supplied, split into list object else set to blank.
    if key_exists(name_request,"vm_name"):
        try:
            vm_names = list(filter(None,str(name_request["vm_name"]).upper().strip().split(',')))
            # Check VM Names supplied matches vm count required, else make blank.
            if len(vm_names) != int(name_request["vm_count"]):
                name_request["vm_name"] = ""                    
        except ValueError as e:
            name_request["vm_name"] = ""
            print("VM Name error ", e, "\n")
    else:
        name_request["vm_name"] = ""

    if name_request["vm_name"] == "":
        print("Generating VM Name...\n")
        match=['POC','Proof-Of-Concept','Concept']
        #if "Proof-Of-Concept" in str(name_request['environment']).upper():
        if any(c in str(name_request['environment']) for c in match):
            app_type = "POC"
            #app_type = name_request['environment']
        else:
            app_type = name_request['app_type']

        name_spec = {
            "business_unit": name_request['business_unit'],
            "app_type": app_type,
            "environment": name_request['environment'],
            "os_version": name_request['os_version'],
            "request_jobid": name_request['reference'],
            "vm_count": 1,
            "is_virtual": 1,
            "for_testing": for_testing,
            "vm_description": name_request['app_type'],
            "requested_by": name_request['reference']
        }
        for x in range(0, int(name_request["vm_count"])):
            vm_name = getNextName(name_spec)
            print(f"NAME {x} ISSUED: {vm_name}\n")

            vm_names.append(vm_name['name_issued'])
    else:
        print("VM Names List", vm_names)

    return vm_names

# Get IP Address from IPAM
def getIpAddress(ip_request):
    print(f"\nIPAM ADDRESS REQUEST: {ip_request['ip_cidr']}\n",ip_request,"\n")
    ip_result = {}
    issues = []
    issue_count = 0

    # Set dhcp ip address request result to ""
    if str(ip_request['ip_cidr']).lower() == "dhcp":
        ip_result['has_ipaddress'] = True
        ip_result['ip_address'] = "dhcp"         
    else:  
        ipdata = NextFreeIP(ip_request,"set")
        #jobconf.update(ipdata)
        
        if ipdata['success']:
            ip_result['has_ipaddress'] = True
            ip_result['ip_address'] = ipdata['data']
            
        else:
            ip_result['has_ipaddress'] = False
            ip_result['ip_address'] = ""
            issues.append("No IP Address allocated.")
            issue_count += 1
    ip_result.update({ "issues": issues, "issue_count":issue_count})
    print(f"\nIPAM RESULT:\n{ip_result}\n") 
    
    return ip_result

def AddJobLog(issue_type,data_dict):
    today = str(datetime.now()).split(".")[0]
    #print("Issue Data", json.dumps(data_dict, indent = 2),"\n") 
    #for item in data_dict['issues']:
    for x in range(len(data_dict['issues'])):
        logdata = {
            "log_added" : today,
            "request_jobid" : data_dict['request_jobid'],
            "vm_name" : data_dict['vm_name'],
            "job_stage" : data_dict['job_stage'],
            "log_type" : "ISSUE",
            "job_comments" : str(data_dict['issues'][x]),
            "log_status" : "OPEN",
            "log_updated" : today,
            "log_update" : str(today) + " New issue added",
            "issue_type": str(issue_type).upper(),
            "issue_ref" : "",
            "automatic" : False
        }
        dbAddJobLog(logdata)
    return

# Process Deployment Request
def DeployRequest(request_data):
    # Validate Request
    response = {}
    issues = []
    issue_count = 0
    remediation = False
    timestamp = str(datetime.now()).split(".")[0] 
    request_data['submit_datetime'] = timestamp
    response['timestamp'] = timestamp


    validation = ValidateRequest(request_data)
    response['validation'] = validation
    request_data['request_valid'] = validation['is_valid']

    if not validation['is_valid']:        
        response['success'] = False
        issues.append(validation['validation_issues'])
        request_data['request_status'] = "INVALID"
        #return response
    else:
        request_data['request_status'] = "NEW"
        hosting_data = GetHostingConfig(request_data)
        #print("Hosting Data", json.dumps(hosting_data, indent = 2),"\n") 
        #response['hosting'] = hosting_data
        updated_request = {}
        updated_request['request_comments'] = ""
        
        if not hosting_data['has_hosting']:
            response['success'] = False
            remediation = True
            issues.append("No Hosting Configs found!")
            updated_request['success'] = False
            #return response
        else:
            osconfig_data = GetOSConfig(request_data)
            #print("GetOSConfig Data", json.dumps(osconfig_data, indent = 2),"\n") 
            if not osconfig_data['has_osconfig']:
                response['success'] = False
                remediation = True
                issues.append("No OS Configs found!")
                updated_request['success'] = False
                #return response
            else:
                updated_request = UpdateRequestSpecs(request_data, hosting_data, osconfig_data)
                response['updated_request'] = updated_request

                job_data = getJobData(updated_request)
                #response['job_data'] = job_data
                # Extract or Generate VM Names
                vm_names = getVmName(updated_request)
                jobs = []
                job_issues = 0
                sitenum = 0
                for x in range(len(job_data)):
                    #print("JOB_ID", job['request_jobid'])
                    job = job_data[x].copy()
                    print("JOB_ID", job['request_jobid']," NAME:", vm_names[x])
                    job.update({"vm_name":vm_names[x]})
                    #'''
                    # Reserve IP Address on IPAM
                    ip_request = getIpAddress(job)
                    #ip_address = ip_request['ip_address']
                    print("JOB IP_ADDRESS", ip_request)
                    job.update({"ip_address": ip_request['ip_address']})
                    #'''   
                    job_resources = GetResources(job)
                    print("JOB_RESOURCES","\n", json.dumps(job_resources, indent = 2, sort_keys=True), "\n")
                    if job_resources['issue_count'] == 0:
                        job.update(job_resources['resources'])
                    job.update({"issues":job_resources['issues']})
                    #print("JOB_ISSUES","\n", job_resources, "\n") #['issues']
                    #print("HOSTING OPTIONS","\n", hosting_data['hosting_configs'], "\n")

                    # Extract Hosting config for resources cluster
                    #job.update(hosting_data['hosting_configs'][sitenum])
                    job.update(getDictByValue(job['cluster'][0],"cluster",hosting_data['hosting_configs']))
                    # Add OS Config.
                    job.update(osconfig_data['os_configs'][0])
                    
                    job.update({"target_cluster":job_resources['resources']['clustername']})
                    job.update({"app_tier":updated_request['app_tier']})
                    job.update({"app_type":updated_request['app_type']})
                    #'''
                    #print("CHECKPOINT JOB RESOURCES:","\n",json.dumps(job, indent = 2, sort_keys=True),"\n")
                    #if int(job_resources['issue_count']) > 0:
                    # Check if list of issues is empty
                    if job['issues']:
                        AddJobLog('Resources',job)
                        remediation = True
                        job_issues += 1
                        issues = issues + job['issues']
                        job.update({"job_state": "REMEDIATE","has_compute": job_resources['has_compute'], "has_storage":job_resources['has_storage']})
                        if not job_resources['has_compute']:                            
                            remediate_job = job.copy()
                            print("COMPUTE ISSUES: Requesting Remediation Config","\n", job, "\n")
                            remediate_job['cluster'] = [job['remediation_cluster']]
                            # Compensate for T2 only environments (e.g. VDI)
                            if remediate_job['app_type'] not in ['VDI']:
                                remediate_job['t2total'] = 0
                                remediate_job['t3total'] = job['os_disk']
                                remediate_job['t3drives'] = ""
                            else:
                                remediate_job['t2total'] = job['os_disk']
                                remediate_job['t3total'] = 0
                                remediate_job['t3drives'] = ""
                            remediation_resources = GetResources(remediate_job)
                            print("COMPUTE REMEDIATION RESOURCES:","\n",remediation_resources['resources'],"\n")
                            job.update({"preferred_host": remediation_resources['resources']['preferred_host']})
                            job.update({"t3_datastore": remediation_resources['resources']['t3_datastore']})
                            job.update({"t2_datastore": remediation_resources['resources']['t2_datastore']})
                        #'''
                        elif not job_resources['has_storage']:
                            remediate_job = job.copy()
                            #print("STORAGE ISSUES: Requesting Storage Remediation Config","\n", job, "\n")
                            remediate_job['cluster'] = [job_resources['resources']['clustername']]
                            # Compensate for T2 only environments (e.g. VDI)
                            if remediate_job['app_type'] not in ['VDI']:
                                remediate_job['t2total'] = 0
                                remediate_job['t3total'] = job['os_disk']
                                remediate_job['t3drives'] = ""
                            else:
                                remediate_job['t2total'] = job['os_disk']
                                remediate_job['t3total'] = 0
                                remediate_job['t3drives'] = ""
                            remediation_resources = GetResources(remediate_job)
                            print("STORAGE REMEDIATION RESOURCES:","\n",json.dumps(remediation_resources, indent = 2, sort_keys=True),"\n") #['resources']
                            job.update({"preferred_host": remediation_resources['resources']['preferred_host']})
                            job.update({"t3_datastore": remediation_resources['resources']['t3_datastore']})
                            job.update({"t2_datastore": remediation_resources['resources']['t2_datastore']})
                        #'''
                        else:
                            print("RESOURCES ISSUES: None! All Is Good!","\n")
                            job.update({"job_state": "NORMAL"})
                    else:
                        job.update({"job_state": "NORMAL"})
                        remediation = False
                    job_updated = UpdateJobSpecs(job)
                    jobs.append(job_updated)
                    # Switch to other hosting location reference data
                    if sitenum == 0:
                        sitenum = 1
                    else:
                        sitenum = 0

                response['jobspecs'] = jobs
                updated_request['success'] = True
        
        updated_request['request_comments'] = issues
        if updated_request['success']:
            response['success'] = True
            newrequest = PrepRequestSubmit(updated_request)
            newrequest.pop('success')

            #print("NEW REQUEST","\n", json.dumps(newrequest,indent=2,sort_keys=True),"\n")
            dbAddRequest(newrequest)
            for item in jobs:
                print("JOB",item['request_jobid'], "\n")

                dbAddJob(item)
        else:
            response['success'] = False
        
    print("DONE!","\n")

    
    return response


# Get Request and Jobs Status
def GetRequestStatus(req_query):
    #dct_builddata = {}
    print("GET REQUEST and JOBs STATUS","\n", json.dumps(req_query, indent=2, sort_keys=True),"\n")
    #print("RESULT: REQUEST and JOBs STATUS","\n", json.dumps(result, indent=2, sort_keys=True),"\n")

    jobconf, dct_req, dct_jobs = {},{},{}
    select_request = "reference = '" +str(req_query["reference"]).upper()+ "';"
    df_requests = dbSelectQuery('requests', select_request)
    print("DATAFRAME QUERY REQUEST and JOBS","\n", df_requests)
    if not df_requests.empty:
        df_requests['updated_datetime'] = df_requests['updated_datetime'].astype(str)
        dct_req_out = df_requests[['reference','request_status','vm_count','updated_datetime','request_comments']].copy()
        dct_req = dct_req_out.to_dict(orient='records')
        jobconf["request_status"] = dct_req
        #qry_request = session.query(Requests) \
        #    .filter(Requests.reference == str(data_dict['reference']).upper()) \
        #    .first()
        select_jobs = "reference = '" +str(req_query["reference"]).upper()+ "';"
        df_jobs = dbSelectQuery('buildjobs', select_jobs)
        if not df_jobs.empty:
            df_jobs['updated_datetime'] = df_jobs['updated_datetime'].astype(str)
            df_jobs_out = df_jobs[['request_jobid','reference','vm_name','ip_address','job_status','job_state','job_stage','datacenter','updated_datetime','job_comments']].copy()
            dct_jobs = df_jobs_out.to_dict(orient='records')
            jobconf["success"] = True
            jobconf["job_status"] = dct_jobs
        else:
            jobconf["success"] = False
    else:
        jobconf["success"] = False
    print("RESULTS: QUERY REQUEST and JOBS","\n", jobconf,"\n")

    return jobconf

# Get Build Specs
def getBuildSpecs(query):
    print("GET BUILDSPECS\n", json.dumps(query, indent=2, sort_keys=True),"\n")
    resp, dct_jobs = {},{}
    if key_exists(query,"request_jobid"):
        select_jobs = "request_jobid = '" +str(query["request_jobid"]).upper()+ "';"
        valid = True
    elif key_exists(query,"servername"):
        select_jobs = "vm_name = '" +str(query["servername"]).upper()+ "';"
        valid = True
    elif key_exists(query,"reference"):
        select_jobs = "reference = '" +str(query["reference"]).upper()+ "';"
        valid = True
    else:
        valid = False

    if valid:
        df_jobs = dbSelectQuery('buildjobs', select_jobs)
        if not df_jobs.empty:
            df_jobs['updated_datetime'] = df_jobs['updated_datetime'].astype(str)
            # df_jobs_out = df_jobs[['request_jobid','reference','vm_name','ip_address','job_status','job_state','job_stage','datacenter','updated_datetime','job_comments']].copy()
            # dct_jobs = df_jobs_out.to_dict(orient='records')
            dct_jobs = df_jobs.to_dict(orient='records')
            resp["success"] = True
            resp["data"] = dct_jobs
            resp["message"] = f"{len(df_jobs)} record(s) found..."
        else:
            resp["success"] = False
            resp["message"] = "No records found..."
            resp["data"] = []
    else:
        resp["success"] = False
        resp["message"] = "Invalid query..."
        resp["data"] = []

    print("RESULTS: BUILDSPECS\n", resp,"\n")

    return resp
