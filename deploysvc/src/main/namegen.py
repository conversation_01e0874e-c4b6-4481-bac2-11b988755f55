import sys, json
from datetime import datetime
from rich import print
# Import Database module
from src.main.dbmgmt import dbGetNextName,dbQuery, df2sql
# Import Config Reference Data
from src.main.confmgmt2 import GetNameOSConfig
import config
# Assign config data to local variable
config = config.conf()
#from config import AppConfig

jobconf = {}

for_testing = config['SERVER']['FOR_TESTING']
environment = config['SERVER']['ENVIRONMENT']['POC']
name_range = config['SERVER']['NAME_RANGE']['SEED']
server_start = config['SERVER']['NAME_RANGE']['SERVER_START']
server_end = config['SERVER']['NAME_RANGE']['SERVER_END']
name_places = 6
pad_char = 0

# Generate Next Free Name to use
# Requires json with JOB and "GET or SET" action.
def GetName(job):           #,action):
    print("NAME SPECS:",json.dumps(job, indent=2),"\n")
    
    apptype = str(config['NAMEGEN']['APP_TYPE'][job['app_type'].upper()]) #+ chassis
    #testing = True
    if job['is_virtual']:
        chassis = str(config['NAMEGEN']['CHASSIS']['VIRTUAL'])
    else:
        chassis = str(config['NAMEGEN']['CHASSIS']['PHYSICAL'])

    #print("APPTYPE", str(config['NAMEGEN']['APP_TYPE'][job['app_type'].upper()]) ,"\n")
    print("APPTYPE", apptype,"\n")

    if apptype not in ["CLS","POC","VDST"] :
        base_name = apptype + chassis + name_range.upper()  # + seed_range #
    else:
        base_name = apptype + name_range.upper()            #+ seed_range # + chassis 

    test_range = str(config['NAMEGEN']['NAME_RANGE']['TESTING']).upper()
    if job['for_testing']:
        seed_range = test_range
        #base_name = base_name + str(seed_range)[0]
    else:
        seed_range = str(config['NAMEGEN']['NAME_RANGE']['PRODUCTION']).upper()

    print("\n","BASENAME:", base_name, "\n")        
    job['seed_range'] = seed_range
    job['base_name'] = base_name
    job['test_range'] = test_range
    #job.pop('for_testing')

    vm_names = ""
    for x in range(0, int(job['vm_count'])):
        name_request = dbGetNextName(job)
        print("JOB_",x,"\n")
        if x == int(job['vm_count'])-1:
            vm_names = vm_names + name_request['name_issued']
        else:
            vm_names = vm_names + name_request['name_issued'] +","
        print("NAMES", vm_names, "\n")

    job.pop('vm_count')
    
    job['vm_name'] = vm_names.upper()
    
    name_request.update(job)
    name_request.pop('name_issued')
    #name_request.pop('range_split')

    return name_request

def IssueNextName(job):           #,action):
    print("NAME SPECS:",json.dumps(job, indent=2),"\n")
    osconfig_spec = {}
    name_request = {}
    name_list = []
    name_issued = {}
    
    apptype = str(config['NAMEGEN']['APP_TYPE'][job['app_type'].upper()]) #+ chassis
    #testing = True
    if job['is_virtual']:
        chassis = str(config['NAMEGEN']['CHASSIS']['VIRTUAL'])
    else:
        chassis = str(config['NAMEGEN']['CHASSIS']['PHYSICAL'])

    #print("APPTYPE", str(config['NAMEGEN']['APP_TYPE'][job['app_type'].upper()]) ,"\n")
    print("APPTYPE", apptype,"\n")

    if apptype not in ["CLS","POC","VDST"] :
        base_name = apptype + chassis + name_range.upper()  # + seed_range #
    else:
        base_name = apptype + name_range.upper()            #+ seed_range # + chassis 

    test_range = str(config['NAMEGEN']['NAME_RANGE']['TESTING']).upper()
    if job['for_testing']:
        seed_range = test_range
        #base_name = base_name + str(seed_range)[0]
    else:
        seed_range = str(config['NAMEGEN']['NAME_RANGE']['PRODUCTION']).upper()

    print("\n","BASENAME:", base_name, "\n")        
    job['seed_range'] = seed_range
    job['base_name'] = base_name
    job['test_range'] = test_range
    #job.pop('for_testing')

    temp = {}
    for x in range(0, int(job['vm_count'])):
        name_request = dbGetNextName(job)
        if apptype not in ["CLS"] :
            osconfig = GetNameOSConfig(job)
            if not osconfig.empty:
                osconfig.drop(['sla','app_tier','app_type','vm_template','os_edition','os_disksize','os_compliance_scan','vulnerability_scan'], axis=1, inplace=True)
                print("AD_OBJECT",type(osconfig),"\n",osconfig.info(),"\n")


                osconfig['vm_name'] = name_request['name_issued']
                temp['vm_description'] = name_request['vm_description']
                print("AD_OBJECT",type(osconfig),"\n",osconfig.head(),"\n")
                osconfig_spec = osconfig.to_dict(orient='records')
                #pop_list = []
                #pop_list = ['sla','app_tier','app_type','vm_template','os_edition','os_disksize','os_compliance_scan','vulnerability_scan']
                #print([osconfig_spec.pop(key) for key in pop_list])
                
                print("TYPE",type(osconfig_spec),"\n")
                print("osconfig_spec",osconfig_spec,"\n")
                temp = osconfig_spec.pop()
                temp['success'] = True
                temp['vm_description'] = name_request['vm_description']
            else:
                temp['error'] = "No AD Object Config Found!"
                temp['success'] = False
        else:
            temp['vm_name'] = name_request['name_issued']
            temp['vm_description'] = name_request['vm_description']
            temp['success'] = True
        name_list.append(temp)
        temp = {}
    
    print("NAMES", name_list, "\n")
    #name_issued['name_issued'] = name_list

    #job.pop('vm_count')
    
    #job['vm_name'] = vm_names.upper()
    
    name_issued['name_data'] = name_list
    #name_request.pop('name_issued')
    #name_request.pop('range_split')

    return name_issued

def getNextName(job):           #,action):

    #print("NAME REQUEST:",json.dumps(job, indent=2),"\n")

    name_request = {}
    
    apptype = str(config['SERVER']['NAME_TYPE'][job['app_type'].upper()]) #+ chassis
    #testing = True
    if job['is_virtual']:
        chassis = str(config['SERVER']['CHASSIS']['VIRTUAL'])
    else:
        chassis = str(config['SERVER']['CHASSIS']['PHYSICAL'])

    #print("APPTYPE", apptype,"\n")

    if apptype not in ["CLS","POC","VDST"] :
        base_name = apptype + chassis + name_range.upper()  # + seed_range #
    else:
        base_name = apptype + name_range.upper()            #+ seed_range # + chassis 

    test_range = str(config['SERVER']['NAME_RANGE']['TESTING']).upper()
    if job['for_testing']:
        seed_range = test_range
    else:
        seed_range = str(config['SERVER']['NAME_RANGE']['PRODUCTION']).upper()

    #print("\n","BASENAME:", base_name, "\n")        
    job['seed_range'] = seed_range
    job['base_name'] = base_name
    job['test_range'] = test_range

    name_request = dbGetNextName(job)
    
    print("NAME ISSUED:", name_request['name_issued'], "\n")

    return name_request

def getName(input):
    print("NAME REQUEST:\n",json.dumps(input, indent=2),"\n")  
    name_group = input['name_group']
    grplen = len(name_group)
    qry_getname = f" \
        SELECT \
            cast(substring(max(name_issued) from {grplen+1}) as unsigned)+1 as counter, \
            concat(left(max(name_issued),{grplen}), lpad((cast((cast(substring(max(name_issued) from {grplen+1}) as unsigned)+1) as char)),6,0)) as name_issued \
        FROM namestable \
        WHERE name_issued LIKE '{name_group}%' \
        ORDER BY name_issued desc; \
    "
    dfNextName = dbQuery(qry_getname)
    
    if dfNextName['name_issued'][0] != None:
        dfNextName.loc[0,'request_jobid'] = str(input['reference']).upper()
        print("Results\n",dfNextName.head())
        print("LENGTH: ",dfNextName.shape[0])
        # Convert response to dictionary
        dctNextName = dfNextName.to_dict('records')[0]
        # Add next_name to database
        #dfNextName.reset_index(drop=True, inplace=True)
        #df2sql(dfNextName,'namestable','append')
    else:
        # No current entries found, generate a start name for this name group
        print(f"Starting NEW Name Group for {name_group}")
        next_name = name_group.upper() + f"{server_start:06}" #:name_places}" #+ str(server_start).ljust(name_places,str(pad_char))
        dctNextName = {
            'reference' : str(input['reference']).upper(),
            'counter' : server_start,
            'name_issued' : next_name
        }
        dfNextName.loc[0,'request_jobid'] = str(input['reference']).upper()
        dfNextName.loc[0,'counter'] = server_start
        dfNextName.loc[0,'name_issued'] = str(next_name).upper()
    # Write new name to database
    dfNextName.reset_index(drop=True, inplace=True)
    df2sql(dfNextName,'namestable','append')

    return dctNextName