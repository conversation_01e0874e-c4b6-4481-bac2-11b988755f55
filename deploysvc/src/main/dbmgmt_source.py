from ast import Add
import sys, json, time, requests
from datetime import date, time, datetime
import logging

import config
# Assign config data to local variable
config = config.conf()

from config import AppConfig
# Import SqlAlchemy DB instance
from src import db

#import MariaDB connection
from src.mdbsql import mdbconnect, init_db, Base, mdbengine, mdbsession, df2conn, text
from src.mdbsql import Table, Column, Integer, String, Text, Numeric, Date, DateTime, Boolean, Numeric
from src.mdbsql import MutableList, PickleType, object_as_dict, query_to_list, query_to_dict #, df2sql, DropTable #, Date, ForeignKey, orm,

# Import JIRA API components
from src.main.jiramgmt import AddJiraComment, ChangeJiraStatus

#from src.utils import get_xlsx2df, get_xlsx2df_auth
#import xlrd
# Import pandas
import pandas as pd
#import numpy as np

# Read config variables
user = AppConfig.DB_USER
pwd = AppConfig.DB_PWD
dbs = config['MARIADB']['MARIADB_CPSDB']
host = config['MARIADB']['MARIADB_HOST']
port = config['MARIADB']['MARIADB_PORT']


df_confdata = pd.DataFrame()
df_pending = pd.DataFrame()
df_capdata = pd.DataFrame()
df_capcompute = pd.DataFrame()
df_capstorage = pd.DataFrame()

reqconf = {}
capdata = {}
jobconf = {}
req_status = []
req_status = ["READY","DECLINED","IN_PROGRESS","REMEDIATION","COMPLETED","CANCELLED"]
job_keys_remove = ['vm_count','compute_status','aci_tenant','disk_type','disk_blocksize','t2storage_status','t3storage_status','active','ad_domain','anp','bridged_domain','client_tag','active_status','comment','compute_ok','cluster','tenant','request_id','cpu_sockets','cpu_ratio','epg','index','ip_ok','max_vcpu','max_vram','os_edition','os_disksize','requested_by','sla','storage_ok','storage_status','t2storage_ok','t3storage_ok']

# Initialize Database Connection
engine = mdbengine(user, pwd, dbs, host, port)
#session = mdbsession(engine)

# Users Data Table
class Users(db.Model):    
    __tablename__ = "users"
    id = Column(db.Integer, autoincrement = True, primary_key=True)
    username = Column(db.String(50), unique=True, nullable=False)
    password = Column(db.String(250), nullable=False)
    email = Column(db.String(50), unique=True, nullable=True)
    fullname = Column(db.String(50), nullable=True)
    description = Column(db.String(250), nullable= True)
    active = Column(db.Boolean(), default=False)

# Requests Data Table
class Requests(db.Model):    
    __tablename__ = "requests"
    reference = Column(db.String(20), primary_key=True)
    ref_subid = Column(db.String(20), nullable=True)
    requested_by = Column(db.String(50), nullable=True)
    submit_datetime = Column(db.DateTime(), nullable=True)
    updated_datetime = Column(db.DateTime(), nullable=True)
    complete_datetime = Column(db.DateTime(), nullable=True)
    request_phase = Column(db.String(50), nullable=True)
    request_status = Column(db.String(50), nullable=True)
    request_valid = Column(db.Boolean(), nullable=True)
    business_unit = Column(db.String(50), nullable=True)
    app_type = Column(db.String(50), nullable=True)
    app_tier = Column(db.String(50), nullable=True)
    environment = Column(db.String(50), nullable=True)
    vm_count = Column(db.Integer, nullable=True)
    vm_name = Column(db.String(100), nullable=True)    
    vm_description = Column(db.String(100), nullable=True)  
    os_version = Column(db.String(50), nullable=True)
    os_type = Column(db.String(50), nullable=True)  
    vcpus = Column(db.String(50), nullable=True)
    vram = Column(db.String(50), nullable=True)
    t2storage = Column(db.String(50), nullable=True)
    t2drives = Column(db.String(50), nullable=True)
    t3storage = Column(db.String(50), nullable=True)
    t3drives = Column(db.String(50), nullable=True)
    vm_placement = Column(db.String(50), nullable=True)
    hosting_location = Column(db.String(50), nullable=True)
    competency = Column(db.String(50), nullable=True)
    dr_solution = Column(db.String(50), nullable=True)
    vm_partner = Column(db.String(50), nullable=True)
    override_standards= Column(db.Boolean(), nullable=True)
    manual_actions = Column(db.String(200), nullable=True)
    request_comments = Column(db.String(250), nullable=True)

#'''
# Jobs Data Table
class BuildJobs(db.Model):
    __tablename__ = "buildjobs"
    request_jobid = Column(db.String(20), primary_key=True)
    reference = Column(db.String(20), nullable=True)
    start_datetime = Column(db.DateTime(), nullable=True)
    updated_datetime = Column(db.DateTime(), nullable=True)
    complete_datetime = Column(db.DateTime(), nullable=True)
    business_unit = Column(db.String(50), nullable=True)
    app_type = Column(db.String(50), nullable=True)
    app_tier = Column(db.String(50), nullable=True)
    environment = Column(db.String(50), nullable=True)

    job_active = Column(db.Boolean(), nullable=True)
    job_stage = Column(db.String(50), nullable=True)
    job_state = Column(db.String(50), nullable=True)
    job_status = Column(db.String(50), nullable=True)
    job_comments = Column(db.Text, nullable=True)

    vm_name = Column(db.String(30), nullable=True)    
    vm_description = Column(db.String(100), nullable=True)   
    os_version = Column(db.String(50), nullable=True)
    os_type = Column(db.String(50), nullable=True)  
    vm_template = Column(db.String(50), nullable=True)
    vcpus = Column(db.Integer, nullable=True)
    vram = Column(db.Integer, nullable=True)

    ad_orgunit = Column(db.String(200), nullable=True)
    ad_suffix = Column(db.String(50), nullable=True)
    scan_os_baseline = Column(db.String(50), nullable=True)
    scan_os_vulnerability = Column(db.String(50), nullable=True)
    build_report = Column(db.String(50), nullable=True)

    ip_address = Column(db.String(15), nullable=True)
    ip_cidr = Column(db.String(15), nullable=True)  
    ip_gateway = Column(db.String(15), nullable=True)
    ip_mask = Column(db.String(15), nullable=True)
    ip_maskbits = Column(db.Integer, nullable=True)
    dns = Column(db.String(15), nullable=True)
    dns_suffix = Column(db.String(50), nullable=True)
    epg_string = Column(db.String(50), nullable=True)
    vlan_tag_bare_metal = Column(db.Integer, nullable=True) 

    has_compute = Column(db.Boolean(), nullable=True)
    has_storage = Column(db.Boolean(), nullable=True)
    os_disk = Column(db.Integer, nullable=True)
    t2storage = Column(db.String(50), nullable=True)
    t2drives = Column(db.String(20), nullable=True)
    t2_datastore = Column(db.String(50), nullable=True)
    t2storage_disks = Column(db.String(50), nullable=True)
    t2storage_drives = Column(db.String(50), nullable=True)
    t2total = Column(db.Integer, nullable=True) 

    t3storage = Column(db.String(50), nullable=True)
    t3drives = Column(db.String(20), nullable=True)
    t3_datastore = Column(db.String(50), nullable=True)
    t3storage_disks = Column(db.String(50), nullable=True)
    t3storage_drives = Column(db.String(50), nullable=True)
    t3total = Column(db.Integer, nullable=True) 

    datacenter = Column(db.String(50), nullable=True)
    hosting_platform = Column(db.String(50), nullable=True)
    vc_node = Column(db.String(50), nullable=True) 
    vmfolder = Column(db.String(50), nullable=True)
    vm_placement = Column(db.String(50), nullable=True)    
    hosting_location = Column(db.String(50), nullable=True)  
    target_cluster = Column(db.String(50), nullable=True)
    preferred_host = Column(db.String(50), nullable=True)

    remediation_cluster = Column(db.String(50), nullable=True)
    remediation_host = Column(db.String(50), nullable=True)
    competency = Column(db.String(50), nullable=True)
    dr_solution = Column(db.String(50), nullable=True)

#'''

# Joblogs Table
class JobLogs(db.Model):
    __tablename__ = "joblogs"
    id = Column(db.Integer, autoincrement = True, primary_key=True)
    log_added = Column(db.DateTime(), nullable=False)
    user_ref = Column(db.String(20), nullable=True) 
    request_jobid = Column(db.String(20), nullable=True)    
    vm_name = Column(db.String(20), nullable=True)
    issue_ref = Column(db.String(50), nullable=False)
    issue_type = Column(db.String(50), nullable=False)
    job_stage = Column(db.String(50), nullable=True)
    log_type = Column(db.String(50), nullable=False)
    job_comments = Column(db.String(250), nullable=True)
    log_status = Column(db.String(50), nullable=True)
    log_updated = Column(db.DateTime(), nullable=True)
    log_update = Column(db.String(250), nullable=True)
    automatic = Column(db.Boolean(), default=False)

# Resources Data Table
class Resources(db.Model):
    __tablename__ = "resources"
    __table_args__ = {'extend_existing': True}
    
    index = Column(db.Integer, autoincrement = True, primary_key=True)
    clustername = Column(db.String(50), nullable=True)
    vcenter = Column(db.String(50), nullable=True)
    totalcpusincluster = Column(db.Integer, nullable=True)
    cpuall = Column(db.Numeric, nullable=True)
    cpuratio = Column(db.Numeric, nullable=True)    
    availablecpu = Column(db.Integer, nullable=True)
    totalmemincls = Column(db.Numeric, nullable=True)
    memall = Column(db.Numeric, nullable=True)
    mempercall = Column(db.Numeric, nullable=True)
    availablemem = Column(db.Numeric, nullable=True)
    preferred_host = Column(db.String(50), nullable=True)
    datastore_name = Column(db.String(150), nullable=True)
    mirrored = Column(db.String(15), nullable=True)
    tier = Column(db.Integer, nullable=True)
    freespacegb = Column(db.Numeric, nullable=True)
    capacitygb = Column(db.Numeric, nullable=True)
    availablestrg = Column(db.Numeric, nullable=True)
    updated = Column(db.DateTime(), nullable=True)

# Names Generator Table
class NamesTable(db.Model):
    __tablename__ = "namestable"
    id = Column(db.Integer, primary_key=True)
    request_jobid = Column(db.String(20), nullable=True)
    date_issued = Column(db.DateTime(), nullable=True)    
    name_issued = Column(db.String(20), unique=True, nullable=False)
    os_version = Column(db.String(50), nullable=True)
    counter = Column(db.Integer, nullable=False)
    business_unit = Column(db.String(40), nullable=True)
    app_type = Column(db.String(20), nullable=True)
    app_tier = Column(db.String(50), nullable=True)
    environment = Column(db.String(40), nullable=True)
    is_virtual = Column(db.Boolean(), nullable=True)
    vm_description = Column(db.String(40), nullable=True)

# Capacity Data Table
class Capacity(db.Model):
    __tablename__ = "capacity"
    __table_args__ = {'extend_existing': True}

    index = Column(db.Integer, autoincrement = True, primary_key=True) #, autoincrement = True, nullable=False)
    clustername = Column(db.String(50), nullable=True)
    vcenter = Column(db.String(50), nullable=True)
    totalcpusincluster = Column(db.Integer, nullable=True)
    cpuallocation = Column(db.Numeric, nullable=True)
    cpuratio = Column(db.Numeric, nullable=True)    
    availablecpu = Column(db.Integer, nullable=True)
    totalmemincluster = Column(db.Numeric, nullable=True)
    memallocation = Column(db.Numeric, nullable=True)
    mempercallocated = Column(db.Numeric, nullable=True)
    availablemem = Column(db.Numeric, nullable=True)
    preferred_host = Column(db.String(50), nullable=True)
    datastore_name = Column(db.String(150), nullable=True)
    mirrored = Column(db.String(15), nullable=True)
    tier = Column(db.Integer, nullable=True)
    freespacegb = Column(db.Numeric, nullable=True)
    capacitygb = Column(db.Numeric, nullable=True)
    availablestorage = Column(db.Numeric, nullable=True)
    updated = Column(db.DateTime(), nullable=True)
#'''
# Decomm Requests Table
class DecommRequests(db.Model):    
    __tablename__ = "decommrequests"
    id = Column(db.Integer, autoincrement = True, primary_key=True)
    reference = Column(db.String(20), nullable=False)
    changeref = Column(db.String(20), nullable=False)
    requested_by = Column(db.String(50), nullable=True)
    requested_on = Column(db.DateTime(), nullable=True)
    hostname = Column(db.String(100), nullable=False)
    instance = Column(db.String(100), nullable=False)
    is_virtual = Column(db.Boolean(), nullable=False, default=True)
    cooldown = Column(db.Boolean(), nullable=False, default=True)
    shutdown = Column(db.Boolean(), nullable=False, default=False)
    retain_ip = Column(db.Boolean(), nullable=False, default=False)
    start_date = Column(db.Date(), nullable=False)
    decomm_stage = Column(db.String(100), nullable=False)
    decomm_status = Column(db.String(100), nullable=False)
    completed_datetime = Column(db.DateTime(), nullable=True)


#'''
#'''
# Decomm Jobs Table
class DecommJobs(db.Model):
    __tablename__ = "decommjobs"
    id = Column(db.Integer, autoincrement = True, primary_key=True)
    decomm_jobid = Column(db.String(20), nullable=False)
    reference = Column(db.String(20), nullable=False)
    changeref = Column(db.String(20), nullable=True)
    requested_by = Column(db.String(50), nullable=True)
    requested_on = Column(db.DateTime(), nullable=True)
    hostname = Column(db.String(100), nullable=False)
    instance = Column(db.String(100), nullable=False)
    is_virtual = Column(db.Boolean(), nullable=False, default=True)
    competency = Column(db.String(50), nullable=True)
    cooldown = Column(db.Boolean(), nullable=False, default=True)
    cooldown_days = Column(db.Integer, nullable=True)
    shutdown = Column(db.Boolean(), nullable=False, default=False)
    retain_ip = Column(db.Boolean(), nullable=False, default=False)
    start_date = Column(db.DateTime(), nullable=True)
    final_date = Column(db.DateTime(), nullable=True)
    decomm_stage = Column(db.String(100), nullable=False)
    decomm_status = Column(db.String(100), nullable=False)
    updated_datetime = Column(db.DateTime(), nullable=True)
    completed_datetime = Column(db.DateTime(), nullable=True)
    decomm_comments = Column(db.String(250), nullable=True)


#'''




#init_db(meta,engine)
Base.metadata.create_all(bind=engine)


def df2sql(data,table,action):
    # Receive data and upload to database
    print("Received data...")
    #print(data)
    #conn, meta, engine, session = mdbconnect(user, pwd, dbs, host, port)
    engine = mdbengine(user, pwd, dbs, host, port)
    #session = mdbsession(engine)
    conn = engine.raw_connection()
    
    cursor = conn.cursor()
    data.to_sql(name=table, con=engine, if_exists = action, index=True)
    #print(action," to database complete!","\n")
    cursor.close() 
    conn.close()

    return

# Add New Request to database
def dbAddRequest(data_dict):
    print("DATA", data_dict)
    #data_dict.pop('requested')
    req = Requests(**data_dict)
    #with session.begin():
    engine = mdbengine(user, pwd, dbs, host, port)
    session = mdbsession(engine)
    session.add(req)
    session.commit()
    print("New Request Added!","\n")
    session.close()
    # read

    return

# Add New Request to database
def dbAddJob(data_dict):

    print("DATA", data_dict)
    req = BuildJobs(**data_dict)
    #with session.begin():
    engine = mdbengine(user, pwd, dbs, host, port)
    session = mdbsession(engine)
    session.add(req)
    session.commit()
    session.close()
    print("New Build Job Added!","\n")
    # read

    return

# Add New Request to database
def dbUpdateRequest(data_dict):
    print("DATA", data_dict)
    reqconf["reference"] = str(data_dict["reference"]).upper()

    if str(data_dict["request_status"]).upper() in req_status:
        #with session.begin():
        engine = mdbengine(user, pwd, dbs, host, port)
        session = mdbsession(engine)
        session.query(Requests).\
            filter(Requests.reference == str(data_dict["reference"]).upper()).\
            update({Requests.request_status : str(data_dict["request_status"]).upper() })

        session.commit()
        session.close()
        print("Request Updated!","\n")
        # read
        reqconf["update_result"] = "successful"
        reqconf["update_ok"] = True
    else:
        print("Request Updated Failed!","\n")
        reqconf["update_result"] = "failed"
        reqconf["update_ok"] = False

    print(reqconf)

    return reqconf

def AddJobLog(data_dict):
    today = str(datetime.now()).split(".")[0]
    #with session.begin():
    engine = mdbengine(user, pwd, dbs, host, port)
    session = mdbsession(engine)
    job = session.query(BuildJobs)                                          \
        .filter(                                                                    \
            BuildJobs.request_jobid == str(data_dict["request_jobid"]).upper()      \
            )                                                                       \
        .first()
    session.close() 
    if str(data_dict['log_type']).upper() == "INFO":
        log_status = "RESOLVED"
    else:
        log_status = "OPEN"            

    logdata = {
        "log_added" : today,
        "request_jobid" : job.request_jobid,
        "user_ref" : str(data_dict['requested_by']),
        "vm_name" : job.vm_name,
        "job_stage" : data_dict['job_stage'],
        "log_type" : str(data_dict['log_type']).upper(),
        "job_comments" : data_dict['job_comment'],
        "log_status" : log_status,
        "log_updated" : today,
        "log_update" : str(today) + " New entry added.",
        "issue_type": str(data_dict['issue_type']).upper(),
        "issue_ref" : str(data_dict['issue_ref']),
        "automatic" : False
    }
    dbAddJobLog(logdata)
    return


# Get Next PENDING Build Job
def dbGetNextJob(data_dict):
    print("DATA", data_dict)
    dct_nextjob = {}
    #with session.begin():
    engine = mdbengine(user, pwd, dbs, host, port)
    session = mdbsession(engine)
    if str(data_dict["request_jobid"]).upper() == "":
        # Filter for Next Job(s)
        if str(data_dict["job_stage"]).upper() == "VM_DEPLOY":    
            nextjob = session.query(BuildJobs)                                      \
                .filter(                                                            \
                    BuildJobs.job_stage == "VM_DEPLOY",                             \
                    BuildJobs.hosting_platform == data_dict["hosting_platform"],    \
                    BuildJobs.job_status == "PENDING"                               \
                    )                                                               \
                .first()
        else:
            nextjob = session.query(BuildJobs)                                      \
                .filter(                                                            \
                    BuildJobs.job_stage == str(data_dict["job_stage"]).upper(),     \
                    BuildJobs.job_status == str(data_dict["job_status"]).upper()    \
                    )                                                               \
                .first()
    else:
        nextjob = session.query(BuildJobs)                                          \
        .filter(                                                                    \
            BuildJobs.request_jobid == str(data_dict["request_jobid"]).upper()      \
            )                                                                       \
        .first()       

    ### ADD TRE ot IF for Empty Response Condition!
    #dct_nextjob = object_as_dict(nextjob)
    if nextjob:
        request_jobid = nextjob.request_jobid
        print("NEXT JOB:", dct_nextjob, "\n")
        reference = nextjob.reference    
        updated = str(datetime.now()).split(".")[0]

        session.query(BuildJobs)                                                \
            .filter(BuildJobs.request_jobid == request_jobid)                   \
            .update({                                                           \
                "job_status":"ALLOCATED",                                       \
                "job_stage": str(data_dict["job_stage"]).upper(),               \
                "job_active":True, "updated_datetime":updated,                    \
                BuildJobs.job_comments : ""                                     \
                })
        session.commit()

        session.query(Requests).\
            filter(Requests.reference == reference).\
            update({Requests.request_phase : "DEPLOYMENT", Requests.request_status : "IN_PROGRESS",  })
        session.commit()
        #AddJiraComment(reference,"DEPLOYMENT in PROGRESS")

        #print("Added DateTime:",	datetime.fromisoformat(added.split(".")[0]), "\n") #datetime.fromisoformat(date_string)

        nextjob = session.query(BuildJobs)                                      \
            .filter( BuildJobs.request_jobid == request_jobid)                  \
            .first()
        
        session.close()

        jobconf = object_as_dict(nextjob)
        jobconf['start_datetime'] = str(jobconf['start_datetime'])
        jobconf['updated_datetime'] = str(jobconf['updated_datetime'])
        print("NEXT JOB","\n", jobconf, "\n")
        jobconf["success"] = True

        return jobconf
    else:
        jobconf = {"success": False}
        return jobconf

# Add New Request to database
def dbUpdateJob(data_dict):

    print("DATA", data_dict)
    reqconf = {}
    #reqconf["request_jobid"] = str(data_dict["request_jobid"]).upper()
    updated = str(datetime.now()).split(".")[0]
    print("UPDATED", updated, "\n")
    #if data_dict["success"]:
    #    reqconf["update_ok"] = True
    #with session.begin():
    engine = mdbengine(user, pwd, dbs, host, port)
    session = mdbsession(engine)
    updatejob = session.query(BuildJobs)                                          \
        .filter(                                                                    \
            BuildJobs.request_jobid == str(data_dict["request_jobid"]).upper()      \
            )                                                                       \
        .first() 
    reference = updatejob.reference
    updatereq = session.query(Requests)\
        .filter(Requests.reference == reference)\
        .first()

    if str(updatereq.requested_by).upper() == "JIRA":
        jira_req = True
        ca_req = False
        cron_req = False
        print("JIRA Request","\n")
    elif str(updatereq.requested_by).upper() == "CA":
        ca_req = True
        jira_req = False
        cron_req = False
        print("CA Request","\n")
    elif str(updatereq.requested_by).upper() == "CRON":
        cron_req = True
        jira_req = False
        ca_req = False
        print("CRONICLE Request","\n")
    else:
        cron_req = False
        ca_req = False
        jira_req = False
        print("Unknown Request","\n")

    if str(data_dict["job_stage"]).upper() == "AD_OBJECT":

        if str(data_dict["job_status"]).upper() == "IN_PROGRESS":
            session.query(BuildJobs)\
                .filter(BuildJobs.request_jobid == str(data_dict["request_jobid"]).upper()) \
                .update({ BuildJobs.job_status : "IN_PROGRESS", BuildJobs.updated_datetime : updated})
            #session.commit()
            reqconf["update_result"] = "AD_OBJECT Updated to IN_PROGRESS!"
            
        # Reset JOB STATUS to PENDING
        if str(data_dict["job_status"]).upper() == "RESET":
            session.query(BuildJobs)\
                .filter(BuildJobs.request_jobid == str(data_dict["request_jobid"]).upper()) \
                .update({ BuildJobs.job_status : "PENDING", BuildJobs.job_comments : updatejob.job_comments, BuildJobs.updated_datetime : updated})
            #session.commit()    
            reqconf["update_result"] = "AD_OBJECT Updated to PENDING!"

        if str(data_dict["job_status"]).upper() == "COMPLETED":
            if data_dict["success"]: 
                session.query(BuildJobs)\
                    .filter(BuildJobs.request_jobid == str(data_dict["request_jobid"]).upper()) \
                    .update({BuildJobs.job_stage : "VM_DEPLOY", BuildJobs.job_status : "PENDING", BuildJobs.updated_datetime : updated })
                #session.commit()
                reqconf["update_result"] = "AD_OBJECT Updated to VM_DEPLOY!"
            else:
                session.query(BuildJobs)\
                    .filter(BuildJobs.request_jobid == str(data_dict["request_jobid"]).upper()) \
                    .update({ BuildJobs.updated_datetime : updated, BuildJobs.job_comments : updatejob.job_comments +";"+str(data_dict["job_comment"])})
                #session.commit()              
                reqconf["update_result"] = "AD_OBJECT requires MANUAL Intervention!"

    if str(data_dict["job_stage"]).upper() == "VM_DEPLOY":

        if str(data_dict["job_status"]).upper() == "IN_PROGRESS":
            session.query(BuildJobs)\
                .filter(BuildJobs.request_jobid == str(data_dict["request_jobid"]).upper()) \
                .update({ BuildJobs.job_status : "IN_PROGRESS", BuildJobs.updated_datetime : updated, BuildJobs.job_comments : str(data_dict["job_comment"])})
            #session.commit()    
            # Update REQUEST status and completion timestamp
            session.query(Requests).\
                filter(Requests.reference == reference).\
                update({Requests.request_phase : "DEPLOYMENT", Requests.request_status : "IN_PROGRESS", Requests.updated_datetime : updated})
                
            reqconf["update_result"] = "VM_DEPLOY Updated to IN_PROGRESS!"
            update_comment = str(updatejob.vm_name) +" build IN_PROGRESS."
            if jira_req:
                AddJiraComment(reference,update_comment)

        # Reset JOB STATUS to PENDING
        if str(data_dict["job_status"]).upper() == "RESET":
            session.query(BuildJobs)\
                .filter(BuildJobs.request_jobid == str(data_dict["request_jobid"]).upper()) \
                .update({ BuildJobs.job_status : "PENDING", BuildJobs.updated_datetime : updated, BuildJobs.job_comments : str(data_dict["job_comment"])})
            #session.commit()    
            reqconf["update_result"] = "VM_DEPLOY Updated to PENDING!"

        if str(data_dict["job_status"]).upper() == "COMPLETED":      
            if data_dict["success"]:         
                if str(updatejob.app_type).upper() == "VDI":
                    session.query(BuildJobs)\
                        .filter(BuildJobs.request_jobid == str(data_dict["request_jobid"]).upper()) \
                        .update({BuildJobs.job_stage : "QUALITY", BuildJobs.job_state : "NORMAL", BuildJobs.job_status : "PENDING", BuildJobs.job_comments : "", BuildJobs.updated_datetime : updated })
                    #session.commit()
                    reqconf["update_result"] = "VDI Updated to QUALITY!"        
                    #data_dict['job_stage'] = "QUALITY"         
                else:                 
                    session.query(BuildJobs)\
                        .filter(BuildJobs.request_jobid == str(data_dict["request_jobid"]).upper()) \
                        .update({BuildJobs.job_stage : "OS_CONFIG", BuildJobs.job_state : "NORMAL", BuildJobs.job_status : "PENDING", BuildJobs.job_comments : "", BuildJobs.updated_datetime : updated})
                    #session.commit()              
                    reqconf["update_result"] = "VM_DEPLOY Updated to OS_CONFIG!"
            else:
                if str(updatejob.app_type).upper() == "VDI":
                    # Update REQUEST status and completion timestamp
                    session.query(Requests).\
                        filter(Requests.reference == reference).\
                        update({Requests.request_phase : "DEPLOYMENT", Requests.request_status : "ON_HOLD", Requests.request_comments : updatereq.request_comments + str(data_dict["job_comment"])+";", Requests.updated_datetime : updated})
                    #session.commit()                    
                # Update JOB status 
                session.query(BuildJobs)\
                    .filter(BuildJobs.request_jobid == str(data_dict["request_jobid"]).upper()) \
                    .update({BuildJobs.job_state : "MANUAL", BuildJobs.updated_datetime : updated, BuildJobs.job_comments : str(data_dict["job_comment"])})
                #session.commit()              
                reqconf["update_result"] = "VM_DEPLOY requires MANUAL Intervention!"

    if str(data_dict["job_stage"]).upper() == "VM_REDEPLOY":
        # Reset JOB STATUS to PENDING
        if str(data_dict["job_status"]).upper() == "RESET":
            session.query(BuildJobs)\
                .filter(BuildJobs.request_jobid == str(data_dict["request_jobid"]).upper()) \
                .update({ BuildJobs.job_stage : "VM_DEPLOY", BuildJobs.job_status : "PENDING", BuildJobs.updated_datetime : updated, BuildJobs.job_comments : str(data_dict["job_comment"])})
            #session.commit()    
            reqconf["update_result"] = "Reset to VM_DEPLOY and PENDING!"

    if str(data_dict["job_stage"]).upper() == "OS_CONFIG":

        if str(data_dict["job_status"]).upper() == "IN_PROGRESS":
            session.query(BuildJobs)\
                .filter(BuildJobs.request_jobid == str(data_dict["request_jobid"]).upper()) \
                .update({ BuildJobs.job_status : "IN_PROGRESS", BuildJobs.updated_datetime : updated, BuildJobs.job_comments : str(data_dict["job_comment"])})
            #session.commit()
            reqconf["update_result"] = "OS_CONFIG Updated to IN_PROGRESS!"
            
        # Reset JOB STATUS to PENDING
        if str(data_dict["job_status"]).upper() == "RESET":
            session.query(BuildJobs)\
                .filter(BuildJobs.request_jobid == str(data_dict["request_jobid"]).upper()) \
                .update({ BuildJobs.job_status : "PENDING", BuildJobs.job_state : "NORMAL", BuildJobs.updated_datetime : updated, BuildJobs.job_comments : str(data_dict["job_comment"])})
            #session.commit()    
            reqconf["update_result"] = "OS_CONFIG Updated to PENDING!"

        if str(data_dict["job_status"]).upper() == "COMPLETED":
            if data_dict["success"]: 

                session.query(BuildJobs)\
                    .filter(BuildJobs.request_jobid == str(data_dict["request_jobid"]).upper()) \
                    .update({BuildJobs.job_stage : "COMPLIANCE", BuildJobs.job_state : "NORMAL", BuildJobs.job_status : "PENDING", BuildJobs.job_comments : "", BuildJobs.updated_datetime : updated })
                #session.commit()
                reqconf["update_result"] = "OS_CONFIG Updated to COMPLIANCE!"
            else:
                session.query(BuildJobs)\
                    .filter(BuildJobs.request_jobid == str(data_dict["request_jobid"]).upper()) \
                    .update({BuildJobs.job_state : "MANUAL", BuildJobs.updated_datetime : updated, BuildJobs.job_comments : str(data_dict["job_comment"])})
                #session.commit()              
                reqconf["update_result"] = "OS_CONFIG requires MANUAL Action."

    if str(data_dict["job_stage"]).upper() == "COMPLIANCE":

        if str(data_dict["job_status"]).upper() == "IN_PROGRESS":
            session.query(BuildJobs)\
                .filter(BuildJobs.request_jobid == str(data_dict["request_jobid"]).upper()) \
                .update({ BuildJobs.job_status : "IN_PROGRESS", BuildJobs.start_datetime : updated, BuildJobs.job_comments : str(data_dict["job_comment"])})
            #session.commit()    
            reqconf["update_result"] = "COMPLIANCE Updated to IN_PROGRESS!"
            
        # Reset JOB STATUS to PENDING
        if str(data_dict["job_status"]).upper() == "RESET":
            session.query(BuildJobs)\
                .filter(BuildJobs.request_jobid == str(data_dict["request_jobid"]).upper()) \
                .update({ BuildJobs.job_status : "PENDING", BuildJobs.job_state : "NORMAL", BuildJobs.updated_datetime : updated, BuildJobs.job_comments : str(data_dict["job_comment"])})
            #session.commit()    
            reqconf["update_result"] = "COMPLIANCE Updated to PENDING!"

        if str(data_dict["job_status"]).upper() == "COMPLETED":      
            if data_dict["success"]:          
                session.query(BuildJobs)\
                    .filter(BuildJobs.request_jobid == str(data_dict["request_jobid"]).upper()) \
                    .update({BuildJobs.job_stage : "QUALITY", BuildJobs.job_state : "NORMAL", BuildJobs.job_status : "PENDING", BuildJobs.job_comments : "", BuildJobs.updated_datetime : updated})
                #session.commit()              
                reqconf["update_result"] = "COMPLIANCE Updated to QUALITY!"
            else:
                session.query(BuildJobs)\
                    .filter(BuildJobs.request_jobid == str(data_dict["request_jobid"]).upper()) \
                    .update({BuildJobs.job_state : "MANUAL", BuildJobs.updated_datetime : updated, BuildJobs.job_comments : str(data_dict["job_comment"])})
                #session.commit()              
                reqconf["update_result"] = "COMPLIANCE requires MANUAL Intervention!"

    if str(data_dict["job_stage"]).upper() == "QUALITY":

        if str(data_dict["job_status"]).upper() == "IN_PROGRESS":
            session.query(BuildJobs)\
                .filter(BuildJobs.request_jobid == str(data_dict["request_jobid"]).upper()) \
                .update({ BuildJobs.job_status : "IN_PROGRESS", BuildJobs.updated_datetime : updated, BuildJobs.job_comments : str(data_dict["job_comment"])})
            #session.commit()    
            reqconf["update_result"] = "QUALITY Updated to IN_PROGRESS!"
            
        # Reset JOB STATUS to PENDING
        if str(data_dict["job_status"]).upper() == "RESET":
            session.query(BuildJobs)\
                .filter(BuildJobs.request_jobid == str(data_dict["request_jobid"]).upper()) \
                .update({ BuildJobs.job_status : "PENDING", BuildJobs.job_state : "NORMAL", BuildJobs.updated_datetime : updated, BuildJobs.job_comments : str(data_dict["job_comment"])})
            #session.commit()    
            reqconf["update_result"] = "QUALITY Updated to PENDING!"

        if str(data_dict["job_status"]).upper() == "COMPLETED":      
            if data_dict["success"]:  
                session.query(BuildJobs)\
                    .filter(BuildJobs.request_jobid == str(data_dict["request_jobid"]).upper()) \
                    .update({BuildJobs.job_stage : "HANDOVER", BuildJobs.job_state : "NORMAL", BuildJobs.job_status : "PENDING", BuildJobs.job_comments : "", BuildJobs.complete_datetime : updated })
                #session.commit()
                reqconf["update_result"] = "Job Updated to HANDOVER PENDING!" 
                update_comment = str(updatejob.vm_name) +" build COMPLETED."
                if jira_req:
                    AddJiraComment(reference,update_comment)

                req_openjobs = session.query(BuildJobs)                                     \
                    .filter(                                                                \
                        BuildJobs.reference == reference,                                   \
                        BuildJobs.job_stage !=  "HANDOVER"                                  \
                        )                                                                   \
                    .all() 
                if len(req_openjobs) == 0:

                    # Update REQUEST status and completion timestamp
                    session.query(Requests).\
                        filter(Requests.reference == reference).\
                        update({Requests.request_phase : "HANDOVER", Requests.request_status : "PENDING", Requests.updated_datetime : updated})
                        
                    if jira_req:
                        AddJiraComment(reference, "All build(s) completed.")
                        #ChangeJiraStatus(reference,"Review")
                else:
                    print(str(len(req_openjobs))+" build(s) remaining.")
                    if jira_req:
                        AddJiraComment(reference, str(len(req_openjobs))+" build(s) remaining.")

            else:
                session.query(BuildJobs)\
                    .filter(BuildJobs.request_jobid == str(data_dict["request_jobid"]).upper()) \
                    .update({BuildJobs.job_state : "MANUAL", BuildJobs.updated_datetime : updated, BuildJobs.job_comments : str(data_dict["job_comment"])})
                #session.commit()              
                reqconf["update_result"] = "QUALITY requires MANUAL Intervention!"

    if str(data_dict["job_stage"]).upper() == "HANDOVER":

        if str(data_dict["job_status"]).upper() == "IN_PROGRESS":
            session.query(BuildJobs)\
                .filter(BuildJobs.request_jobid == str(data_dict["request_jobid"]).upper()) \
                .update({ BuildJobs.job_status : "IN_PROGRESS", BuildJobs.updated_datetime : updated, BuildJobs.job_comments : str(data_dict["job_comment"])})
            #session.commit()    
            reqconf["update_result"] = "HANDOVER Updated to IN_PROGRESS!"

        # Reset JOB STATUS to PENDING
        if str(data_dict["job_status"]).upper() == "RESET":
            session.query(BuildJobs)\
                .filter(BuildJobs.request_jobid == str(data_dict["request_jobid"]).upper()) \
                .update({ BuildJobs.job_status : "PENDING", BuildJobs.job_state : "NORMAL", BuildJobs.updated_datetime : updated, BuildJobs.job_comments : str(data_dict["job_comment"])})
            #session.commit()    
            reqconf["update_result"] = "HANDOVER Updated to PENDING!"

        if str(data_dict["job_status"]).upper() == "COMPLETED":      
            if data_dict["success"]:  
                session.query(BuildJobs)\
                    .filter(BuildJobs.request_jobid == str(data_dict["request_jobid"]).upper()) \
                    .update({BuildJobs.job_stage : "HANDOVER", BuildJobs.job_status : "COMPLETED", BuildJobs.job_state : "NORMAL", BuildJobs.job_comments : "", BuildJobs.complete_datetime : updated })
                session.commit()
                reqconf["update_result"] = "HANDOVER Updated to COMPLETED!" 

                # Update REQUEST status and completion timestamp
                session.query(Requests).\
                    filter(Requests.reference == reference).\
                    update({Requests.request_phase : "HANDOVER", Requests.request_status : "COMPLETED", Requests.complete_datetime : updated})
                #session.commit()
                if jira_req:
                    AddJiraComment(reference, "Handover completed.")
                    ChangeJiraStatus(reference,"Review")
            else:
                session.query(BuildJobs)\
                    .filter(BuildJobs.request_jobid == str(data_dict["request_jobid"]).upper()) \
                    .update({BuildJobs.job_state : "MANUAL", BuildJobs.updated_datetime : updated, BuildJobs.job_comments : str(data_dict["job_comment"])})
                #session.commit()              
                reqconf["update_result"] = "HANDOVER requires MANUAL Intervention!"
    try:
        session.commit() 
    except:
        session.rollback()
    finally:
        session.close()
    AddJobLog(data_dict)
    
    print(reqconf)
    return reqconf

# Get Next Allocated Capacities for Build Job
def dbGetAllJobs2(data_dict):
    #print("DATA", data_dict)
    engine = mdbengine(user, pwd, dbs, host, port)
    session = mdbsession(engine)
    # Filter for ALL Job(s) by STAGE and STATUS
    if data_dict['job_state'] == "":
        if data_dict['hosting_platform'] == "":
            if data_dict['job_status'] == "":
                pending = session.query(BuildJobs)                                          \
                    .filter(                                                                \
                        BuildJobs.job_stage == str(data_dict['job_stage']).upper()          \
                        )                                                                   \
                    .all()                       
            else:
                pending = session.query(BuildJobs)                                          \
                    .filter(                                                                \
                        BuildJobs.job_stage == str(data_dict['job_stage']).upper(),         \
                        BuildJobs.job_status == str(data_dict["job_status"]).upper()        \
                        )                                                                   \
                    .all()                                                   
        else:
            if data_dict['job_status'] == "":
                if data_dict['job_status'] == "":
                    pending = session.query(BuildJobs)                                          \
                        .filter(                                                                \
                            BuildJobs.hosting_platform == str(data_dict['hosting_platform'])   \
                            )                                                                   \
                        .all()    
                else:
                    pending = session.query(BuildJobs)                                          \
                        .filter(                                                                \
                            BuildJobs.hosting_platform == str(data_dict['hosting_platform']),   \
                            BuildJobs.job_stage == str(data_dict['job_stage']).upper()          \
                            )                                                                   \
                        .all()                          
            else:          
                pending = session.query(BuildJobs)                                          \
                    .filter(                                                                \
                        BuildJobs.hosting_platform == str(data_dict['hosting_platform']),   \
                        BuildJobs.job_stage == str(data_dict['job_stage']).upper(),         \
                        BuildJobs.job_status == str(data_dict["job_status"]).upper()       \
                        )                                                                   \
                    .all()               
    else:
        if data_dict['job_status'] == "" and data_dict['job_stage'] != "":
            pending = session.query(BuildJobs)                                          \
                .filter(                                                                \
                    BuildJobs.job_stage == str(data_dict['job_stage']).upper(),         \
                    BuildJobs.job_state == str(data_dict['job_state']).upper()          \
                    )                                                                   \
                .all()  
        elif data_dict['job_stage'] == "":
            pending = session.query(BuildJobs)                                          \
                .filter(                                                                \
                    BuildJobs.job_status == str(data_dict["job_status"]).upper(),       \
                    BuildJobs.job_state == str(data_dict['job_state']).upper()          \
                    )                                                                   \
                .all()                  
        else:
            pending = session.query(BuildJobs)                                          \
                .filter(                                                                \
                    BuildJobs.job_stage == str(data_dict['job_stage']).upper(),         \
                    BuildJobs.job_status == str(data_dict["job_status"]).upper(),       \
                    BuildJobs.job_state == str(data_dict['job_state']).upper()          \
                    )                                                                   \
                .all()
    df_pending = pd.DataFrame( \
        [(d.request_jobid,d.job_stage,d.job_status,d.job_state,d.target_cluster,d.vcpus,d.vram,d.t2_datastore,d.t2total,d.t3_datastore,d.t3total) for d in pending], \
        columns=['request_jobid','job_stage','job_status','job_state','target_cluster','vcpus','vram','t2_datastore','t2total','t3_datastore','t3total'] \
        )
        
    print("\n", df_pending.info(), "\n")
    print("\n", df_pending.head(10), "\n")
    session.close()
    return df_pending


# Get All Build Jobs Logs as per given filters
def dbGetJobLogs(data_dict):
    #with session.begin():
    engine = mdbengine(user, pwd, dbs, host, port)
    session = mdbsession(engine)
    if str(data_dict['request_jobid']).upper() != "" or str(data_dict['vm_name']).upper() != "":
        if str(data_dict['request_jobid']).upper() != "":
            joblogs = session.query(JobLogs)                                          \
                .filter(JobLogs.request_jobid == str(data_dict['request_jobid']).upper()) \
                .all()
        elif str(data_dict['vm_name']).upper() != "":
            joblogs = session.query(JobLogs)                                          \
                .filter(JobLogs.vm_name == str(data_dict['vm_name']).upper()) \
                .all()
    else:
        joblogs = session.query(JobLogs)    \
            .all()

    df_pending = pd.DataFrame( \
        [(d.id,d.request_jobid,d.vm_name,d.log_type,d.log_added,d.job_stage,d.log_status,d.job_comments,d.log_updated,d.log_update,d.automatic) for d in joblogs], \
        columns=['id','request_jobid','vm_name','log_type','log_added','job_stage','log_status','job_comments','log_updated','log_update','automatic'] \
        )
    session.close()
    #df_pending.reset_index(inplace= True)
    #df_pending.set_index(['id','request_jobid','vm_name'], inplace=True)
    print("\n", df_pending.info(), "\n")
    print("\n", df_pending.head(10), "\n")

    return df_pending

# Get All Build Jobs as per given filters
def dbGetAllJobs(data_dict):
    #with session.begin():
    engine = mdbengine(user, pwd, dbs, host, port)
    session = mdbsession(engine)
    pending = session.query(BuildJobs) \
        .all()
    df_pending = pd.DataFrame( \
        [(d.reference,d.request_jobid,d.vm_name,d.ip_address,d.app_type,d.hosting_platform,d.job_stage,d.job_status,d.job_state,d.job_comments,d.updated_datetime,d.target_cluster,d.vcpus,d.vram,d.t2_datastore,d.t2total,d.t3_datastore,d.t3total) for d in pending], \
        columns=['reference','request_jobid','vm_name','ip_address','app_type','hosting_platform','job_stage','job_status','job_state','job_comments','updated_datetime','target_cluster','vcpus','vram','t2_datastore','t2total','t3_datastore','t3total'] \
        )
    session.close()
    df_pending = df_pending.loc[
        (df_pending['hosting_platform'].str.contains(str(data_dict['hosting_platform']))) & \
        (df_pending['job_stage'].str.contains(str(data_dict['job_stage'])))               & \
        (df_pending['job_status'].str.contains(str(data_dict['job_status'])))             & \
        (df_pending['job_state'].str.contains(str(data_dict['job_state'])))               & \
        (df_pending['request_jobid'].str.contains(str(data_dict['request_jobid'])))               
        ]
    #df_pending.reset_index(inplace= True)
    #df_pending.set_index(['reference','request_jobid','vm_name'], inplace=True)
    print("\n", df_pending.info(), "\n")
    print("\n", df_pending.head(10), "\n")

    return df_pending

# Get Next Allocated Capacities for Build Job
def dbRefreshCapacity(df):
    # Replace current Capacity data in database
    df2sql(df,"capacity","replace")
    return

# Get Next Allocated Capacities for Build Job
def dbRefreshResources(df):
    # Replace current Capacity data in database
    engine = mdbengine(user, pwd, dbs, host, port)
    #df2sql(df,"resources","replace")
    df2conn(engine,df,"resources","replace")
    return

# Get Next Allocated Capacities for Build Job
def dbUpdateInfraview(df):
    # Replace current Capacity data in database
    #df2sql(df,"infraview","replace")
    engine = mdbengine(user, pwd, dbs, host, port)
    df2conn(engine,df,"infraview","replace")
    return

# Get Next Allocated Capacities for Build Job ---- str(data_dict["cluster"]).upper()       \
def dbGetCapacity():
    #print("DATA", data_dict)
    #with session.begin():
    engine = mdbengine(user, pwd, dbs, host, port)
    session = mdbsession(engine)
    capacity = session.query(Capacity).all()
    session.close()

    print("\n", "CAPACITY RESULTS:", len(capacity), "\n")
    if len(capacity) > 0:
        df_capdata = pd.DataFrame(query_to_dict(capacity))

    else:
        df_capdata = pd.DataFrame()

    return df_capdata

# Allocate Resources 
def dbAllocateResources(data_dict):
    print("RESOURCES REQUIRED","\n", data_dict, "\n")
    #with session.begin():
    engine = mdbengine(user, pwd, dbs, host, port)
    session = mdbsession(engine)
    updated = str(datetime.now()).split(".")[0]

    # Update Compute Allocated
    session.query(Resources)                                                \
        .filter( Resources.clustername == str(data_dict["clustername"]).upper() )  \
        .update({
            Resources.availablecpu : Resources.availablecpu - int(data_dict["vcpus"]), \
            Resources.availablemem : Resources.availablemem - int(data_dict["vram"]), \
            Resources.updated : updated \
        })
    # Update T2 Storage Allocated    
    if str(data_dict["t2datastore"]) != "":
        session.query(Resources)                                                \
            .filter( Resources.datastore_name == str(data_dict["t2datastore"]).upper() )  \
            .update({
                Resources.availablestrg : Resources.availablestrg - int(data_dict["t2total"])
            })   
    # Update T3 Storage Allocated    
    if str(data_dict["t3datastore"]) != "":
        session.query(Resources)                                                \
            .filter( Resources.datastore_name == str(data_dict["t3datastore"]).upper() )  \
            .update({
                Resources.availablestrg : Resources.availablestrg - int(data_dict["t3total"])
            })              
    #session.commit()
    session.close()

    return

# Get Next PENDING Build Job
def dbUpdateCapacity(data_dict):
    print("DATA","\n", data_dict, "\n")
    #with session.begin():
    engine = mdbengine(user, pwd, dbs, host, port)
    session = mdbsession(engine)
    current = session.query(Capacity)                                                         \
        .filter((Capacity.clustername == str(data_dict["target_cluster"]).upper()) &      \
                ((Capacity.datastore_name == str(data_dict["t2datastore"]).upper()) |  \
                (Capacity.datastore_name == str(data_dict["t3datastore"]).upper()))
            )  \
        .all()
    print("BEFORE","\n")
    for col in current:
        print(col.clustername, col.availablecpu, col.availablemem, col.datastore_name, col.availablestorage, col.updated)
    # Set Request Submit DateTime    ---- filter(BuildJobs.request_jobid == str(jobconf["request_jobid"]).upper()).\
    print("\n")
    updated = str(datetime.now()).split(".")[0]
    #'''
    # Update Compute Allocated
    session.query(Capacity)                                                \
        .filter( Capacity.clustername == str(data_dict["clustername"]).upper() )  \
        .update({
            Capacity.availablecpu : Capacity.availablecpu - int(data_dict["vcpus"]), \
            Capacity.availablemem : Capacity.availablemem - int(data_dict["vram"]), \
            Capacity.updated : updated \
        })
    # Update T2 Storage Allocated    
    session.query(Capacity)                                                \
        .filter( Capacity.datastore_name == str(data_dict["t2datastore"]).upper() )  \
        .update({
            Capacity.availablestorage : Capacity.availablestorage - int(data_dict["t2total"]), \
            Capacity.updated : updated \
        })   
    # Update T3 Storage Allocated    
    session.query(Capacity)                                                \
        .filter( Capacity.datastore_name == str(data_dict["t3datastore"]).upper() )  \
        .update({
            Capacity.availablestorage : Capacity.availablestorage - int(data_dict["t3total"]), \
            Capacity.updated : updated \
        })              
    session.commit()
    session.close()

    print("AFTER","\n")
    for col in current:
        print(col.clustername, col.availablecpu, col.availablemem, col.datastore_name, col.availablestorage, col.updated)
    # Set Request Submit DateTime    ---- filter(BuildJobs.request_jobid == str(jobconf["request_jobid"]).upper()).\
    print("\n")    
    #'''
    return

# Add New Request to database
def dbGetNextName(data_dict):
    #print("DATA", json.dumps(data_dict,indent=2))

    reqconf = {}
    reqconf.update(data_dict)
    reqconf.pop('vm_count')
    
    basename = "%{}%".format(data_dict['base_name'])
    #else:
    #    basename = "%{}%".format(data_dict['base_name'])
    #with session.begin():
    engine = mdbengine(user, pwd, dbs, host, port)
    session = mdbsession(engine)
    if data_dict['for_testing']:
        current = session.query(NamesTable)                          \
            .filter( \
                NamesTable.name_issued.like(basename),         \
                NamesTable.counter >= int(data_dict['test_range'])         \
            )  \
            .order_by(NamesTable.name_issued.desc())    \
            .all()
    else:
        current = session.query(NamesTable)                          \
            .filter( \
                NamesTable.name_issued.like(basename),         \
                NamesTable.counter < int(data_dict['test_range'])         \
            )  \
            .order_by(NamesTable.name_issued.desc())    \
            .all()     

    print("\n", "NAMES RESULTS:", len(current), "\n")
    updated = str(datetime.now()).split(".")[0]
    reqconf.pop('for_testing') 
    reqconf.pop('test_range') 
    reqconf.pop('requested_by') 

    if len(current) > 0:
        #for col in current:
        #    print(col.name_issued)
        last_issued = str(current[0].name_issued)
        next_free_num = int(last_issued.replace(data_dict['base_name'],"")) + 1
        nextname = data_dict['base_name'] + str(next_free_num).rjust(3,'0')

        reqconf["name_issued"] = nextname.upper()
        reqconf["date_issued"] = updated        
        reqconf["counter"] = next_free_num
        reqconf.pop('base_name')
        reqconf.pop('seed_range')   

        addName = NamesTable(**reqconf)
        session.add(addName)
        reqconf["update_result"] = "Last Issued:" + last_issued + ", Next Free: " + nextname
        reqconf["success"] = True
    else:
        #df_names = pd.DataFrame()
        seed_name = str(data_dict['base_name']).upper() + str(data_dict['seed_range'])
        reqconf["name_issued"] = seed_name
        reqconf["date_issued"] = updated
        reqconf["counter"] = int(data_dict['seed_range'])
        reqconf.pop('base_name')
        reqconf.pop('seed_range')

        addName = NamesTable(**reqconf)
        session.add(addName)
        reqconf["result"] = str(seed_name).upper() + " issued, no existing names found"
        reqconf["success"] = False     
    session.commit()
    session.close()
    #print(reqconf)

    return reqconf

# Add New Request to database
def dbAddUsers(data_dict):

    print("DATA", data_dict)

    req = Users(**data_dict)
    #with session.begin():
    engine = mdbengine(user, pwd, dbs, host, port)
    session = mdbsession(engine)
    session.add(req)
    session.commit()
    session.close()
    print("New User Added!","\n")
    # read

    return

# Add New Request to database
def dbAddJobLog(data_dict):

    print("DATA", data_dict)

    req = JobLogs(**data_dict)
    #with session.begin():
    engine = mdbengine(user, pwd, dbs, host, port)
    session = mdbsession(engine)
    session.add(req)
    session.commit()
    session.close()
    print("New Job Entry Logged!","\n")
    # read

    return

# Add New Request to database
def dbUpdateJobLog(data_dict):
    print("DATA", data_dict)
    #updated = str(datetime.now()).split(".")[0]
    #with session.begin():
    engine = mdbengine(user, pwd, dbs, host, port)
    session = mdbsession(engine)
    joblog = session.query(JobLogs)                                          \
        .filter(                                                                    \
            JobLogs.id == int(data_dict["joblogid"])      \
            )                                                                       \
        .first() 
    joblogtext = joblog.log_update
    if joblogtext != "" and str(data_dict["log_update"]) != "":
        joblogtext = str(data_dict["log_update"]) +";"+ joblogtext

        session.query(JobLogs)\
            .filter(JobLogs.id == int(data_dict["joblogid"])) \
            .update({ JobLogs.log_status : str(data_dict["log_status"]), JobLogs.log_updated : data_dict['log_updated'], JobLogs.log_update : joblogtext}) 

    elif joblogtext == "" and str(data_dict["log_status"]) != "":
        session.query(JobLogs)\
            .filter(JobLogs.id == int(data_dict["joblogid"])) \
            .update({ JobLogs.log_status : str(data_dict["log_status"]), JobLogs.log_updated : data_dict['log_updated'], JobLogs.log_update : str(data_dict["log_update"]) +";"}) 
    elif str(data_dict["log_update"]) == "":
        session.query(JobLogs)\
            .filter(JobLogs.id == int(data_dict["joblogid"])) \
            .update({ JobLogs.log_status : str(data_dict["log_status"]), JobLogs.log_updated : data_dict['log_updated']}) 

    session.commit()
    session.close()
    return

# Update Compliance
def dbUpdateCompliance(data_dict):
    print("COMPLIANCE DATA","\n", data_dict, "\n")
    #with session.begin():
    engine = mdbengine(user, pwd, dbs, host, port)
    session = mdbsession(engine)
    updated = str(datetime.now()).split(".")[0]
    #'''
    # Update Compliance Data Files
    session.query(BuildJobs)\
        .filter(BuildJobs.request_jobid == str(data_dict["request_jobid"]).upper()) \
        .update({ BuildJobs.scan_os_baseline : str(data_dict["scan_os_baseline"]),BuildJobs.scan_os_vulnerability : str(data_dict["scan_os_vulnerability"]), BuildJobs.updated_datetime : updated})
    session.commit()
    session.close()
    return

# Update Compliance
def dbUpdateQuality(data_dict):
    print("QUALITY DATA","\n", data_dict, "\n")
    #with session.begin():
    engine = mdbengine(user, pwd, dbs, host, port)
    session = mdbsession(engine)
    updated = str(datetime.now()).split(".")[0]
    #'''
    # Update Compliance Data Files
    session.query(BuildJobs)\
        .filter(BuildJobs.request_jobid == str(data_dict["request_jobid"]).upper()) \
        .update({ BuildJobs.build_report : str(data_dict["build_report"]), BuildJobs.updated_datetime : updated})
    session.commit()
    session.close()
    return

# Update Decomm
def dbUpdateDecommJob(data_dict):
    print("DECOMM DATA","\n", data_dict, "\n")
    #with session.begin():
    engine = mdbengine(user, pwd, dbs, host, port)
    session = mdbsession(engine)
    updated = str(datetime.now()).split(".")[0]
    #'''
    # Update Decomm Job Status
    session.query(DecommJobs)\
        .filter(DecommJobs.decomm_jobid == str(data_dict["decomm_jobid"]).upper()) \
        .update({ DecommJobs.decomm_status : str(data_dict["decomm_status"]),DecommJobs.updated_datetime : updated})
    session.commit()
    session.close()
    # Update Decomm Job Status
    select = "decomm_jobid = '" +str(data_dict["decomm_jobid"]).upper()+ "';"
    df_results = dbSelectQuery('decommjobs', select)

    return df_results

# Execute Query
def dbSelectQuery(table, target):
    query = "SELECT * FROM {} WHERE {}".format(table,target)
    print("QUERY:","\n",query,"\n")
    engine = mdbengine(user, pwd, dbs, host, port)
    #session = mdbsession(engine)
    conn = engine.connect()

    # Use text() to prevent pandas from treating % as format specifiers
    df_results = pd.read_sql(text(query), con = conn)

    #print(df_results.info())
    #print(df_results.head(10))

    conn.close()

    return df_results

# Execute Query
def dbUpdateQuery(table, column, target):
    query = "UPDATE {} SET {} WHERE {};".format(table,column,target)
    print("QUERY:","\n",query,"\n")
    engine = mdbengine(user, pwd, dbs, host, port)
    #session = mdbsession(engine)
    conn = engine.raw_connection()
    with conn.cursor() as cursor:
        cursor.execute(query)
        conn.commit()
    conn.close()

    return


# Get Next PENDING Build Job
def dbAllocateResources(data_dict):
    print("DATA","\n", data_dict, "\n")
    #with session.begin():
    engine = mdbengine(user, pwd, dbs, host, port)
    session = mdbsession(engine)
    before = session.query(Resources)  \
        .filter((Resources.clustername == str(data_dict["clustername"]).upper()))  \
        .all()
    print("BEFORE","\n")
    for col in before:
        print(col.clustername, col.availablecpu, col.availablemem, col.datastore_name, col.availablestrg, col.updated)
    # Set Request Submit DateTime    ---- filter(BuildJobs.request_jobid == str(jobconf["request_jobid"]).upper()).\
    print("\n")
    updated = str(datetime.now()).split(".")[0]
    #'''
    # Update Compute Allocated
    session.query(Resources) \
        .filter( Resources.clustername == str(data_dict["clustername"]).upper() )  \
        .update({
            Resources.availablecpu : Resources.availablecpu - int(data_dict["vcpus"]), \
            Resources.availablemem : Resources.availablemem - int(data_dict["vram"]), \
            Resources.updated : updated \
        })

    # Update T2 Storage Allocated
    if data_dict['t2total'] != 0:
        session.query(Resources)                                                \
            .filter( Resources.datastore_name == str(data_dict["t2_datastore"]).upper() )  \
            .update({
                Resources.availablestrg : Resources.availablestrg - int(data_dict["t2total"]), \
                Resources.updated : updated \
            })   
    # Update T3 Storage Allocated    
    if data_dict['t3total'] != 0:
        session.query(Resources)                                                \
            .filter( Resources.datastore_name == str(data_dict["t3_datastore"]).upper() )  \
            .update({
                Resources.availablestrg : Resources.availablestrg - int(data_dict["t3total"]), \
                Resources.updated : updated \
            })              
    session.commit()
    session.close()
    after = session.query(Resources)                                                         \
        .filter((Resources.clustername == str(data_dict["clustername"]).upper()))  \
        .all()
    print("\n","AFTER","\n")
    for col in after:
        print(col.clustername, col.availablecpu, col.availablemem, col.datastore_name, col.availablestrg, col.updated)
    # Set Request Submit DateTime    ---- filter(BuildJobs.request_jobid == str(jobconf["request_jobid"]).upper()).\
    print("\n")    
    #'''
    return

# Add New Request to database
def dbAddDecommRequest(data_dict):
    print("DATA", data_dict)
    #data_dict.pop('requested')
    req = DecommRequests(**data_dict)
    #with session.begin():
    engine = mdbengine(user, pwd, dbs, host, port)
    session = mdbsession(engine)
    session.add(req)
    session.commit()
    print("New Decomm Request Added!","\n")
    session.close()
    # read

    return

# Add New Request to database
def dbAddDecommJob(data_dict):

    print("DATA", data_dict)
    req = DecommJobs(**data_dict)
    #with session.begin():
    engine = mdbengine(user, pwd, dbs, host, port)
    session = mdbsession(engine)
    session.add(req)
    session.commit()
    session.close()
    print("New Decomm Job Added!","\n")
    # read

    return