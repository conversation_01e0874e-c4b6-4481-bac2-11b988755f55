import sys, json, time, requests
from rich import print
from datetime import date, time, datetime
from requests_ntlm import HttpNtlmAuth
from io import BytesIO

# Import pandas
import pandas as pd
#import numpy as np

from src.utils import get_xlsx2df, get_xlsx2df_auth
# Import Database module
from src.main.dbmgmt import dbGetAllJobs, dbRefreshCapacity, dbGetCapacity, dbRefreshResources, dbUpdateCapacity
# Import Database module
from src.main.dbmgmt import df2sql, dbSelectQuery, dbAllocateResources, dbUpdateInfraview

import config
# Assign config data to local variable
config = config.conf()
from config import AppConfig

cps_user = AppConfig.CPS_USR
cps_pwrd = AppConfig.CPS_PWD

df_confdata = pd.DataFrame()
df_cpctmgmt = pd.DataFrame()
df_compute = pd.DataFrame()
df_compcpct = pd.DataFrame()
df_hosting = pd.DataFrame()
df_storage = pd.DataFrame()
df_computeconf = pd.DataFrame()
df_osconf = pd.DataFrame()
df_t2storage = pd.DataFrame()
df_t3storage = pd.DataFrame()

df_capdata = pd.DataFrame
df_available_cmpt = pd.DataFrame()
df_computecap = pd.DataFrame()
df_available_strg = pd.DataFrame()
df_available_t2strg = pd.DataFrame()
df_available_t3strg = pd.DataFrame()
df_available = pd.DataFrame()   
df_allocated = pd.DataFrame() 
df_pending = pd.DataFrame() 

job_hostconf = {}
job_osconf = {}
jobconf = {}
capacity_dict = {}


t2_tags = ["DIA","SAP"]
t3_tags = ["AMB","QUA","RUB","EME","VSAN","C:"]
#mirror_tags = ["NML","NMP"]
mirror_tags = ["MSP"]
notmirrored = ["ATV"]
excl_tags = ["-0","DNU","PDF","MAP","REPL"]
dedicated = 3584

capacity_data = config['CPS']['SVC_CAPACITY']
infraview_data = config['CPS']['CPS_INFRAVIEW']
##print(capacity_data)

# Get Config Management Data
def UpdateInfraview():
    # capacity Configs
    try:
        #df_infravw = get_xlsx2df(infraview_data)
        print("No AUTH")
        df_infravw = pd.read_excel(infraview_data,sheet_name="Sheet1")
    except:
        #df_infravw = get_xlsx2df_auth(infraview_data,cps_user,cps_pwrd)
        print("With AUTH")
        r = requests.get(infraview_data,auth=HttpNtlmAuth(cps_user,cps_pwrd), verify=False)
        df_infravw = pd.read_excel(BytesIO(r.content),engine='openpyxl',sheet_name="Sheet1")
    print("\n","INFRAVIEW","\n", df_infravw.info(),"\n")
    # Update database table
    dbUpdateInfraview(df_infravw)
    status = {'success':True}
    return status


# Get Config Management Data
def UpdateResources():
    # capacity Configs
    try:
        df_cpctmgmt = get_xlsx2df(capacity_data)
        print("No AUTH")
    except:
        df_cpctmgmt = get_xlsx2df_auth(capacity_data,cps_user,cps_pwrd)
        print("With AUTH")

    df_resources = df_cpctmgmt['Combined']                                         # Extract capacity Configs from Config Mgmt Data
    #print(df_resources.info())
    df_resources.columns = map(str.lower, df_resources.columns)                     # Convert column names to lowercase
    df_resources.rename(columns={'preffered_host':'preferred_host'}, inplace=True)
    df_resources['clustername'] = df_resources['clustername'].str.replace(".mud.internal.co.za","", regex=False)	        # Strip dns suffix from clustername
    df_resources['clustername'] = df_resources['clustername'].str.upper()
    df_resources = df_resources.fillna(0)
    updated = str(datetime.now()).split(".")[0]
    df_resources['updated'] = updated
    df_resources.reset_index(inplace=True)

    query_pending = {
        "hosting_platform"  : "",
        "request_jobid"     : "",
        "job_stage"         : "VM_DEPLOY",
        "job_status"        : "PENDING",
        "job_state"         : ""
    } 
    df_vmdeploy = dbGetAllJobs(query_pending)
    query_pending = {
        "hosting_platform"  : "",
        "request_jobid"     : "",
        "job_stage"         : "AD_OBJECT",
        "job_status"        : "PENDING",
        "job_state"         : ""
    } 
    df_adobject = dbGetAllJobs(query_pending)
    df_pending = df_adobject.append(df_vmdeploy)
    print("\n","PENDING JOBs","\n")
    print(df_pending[['job_stage','job_status','target_cluster','vcpus','vram','t2_datastore','t2total','t3_datastore','t3total']],"\n")

    # Update database table
    dbRefreshResources(df_resources)
    status = {
        'success' : True,
        'message' : "Available Resources updated!"
    }

    return status

# Filter clusters
def SelectCluster(data):
    print("\n","SELECT CLUSTER DATA:", data,"\n")
    issues = 0
    comments = []
    response = {}
    resources_dict = {}
    resources_dict['vcpus'] = data['vcpus']
    resources_dict['vram'] = data['vram']
    print("\n","CLUSTER OPTIONS:", data['cluster'],"\n")

    #clslist = ",".join(map("'{0}'".format, data['cluster']))
    #clslist = clslist.strip("'[")
    #print("CLSLIST:",clslist,"\n")

    if len(data['cluster']) == 1:
        clslist = str(data['cluster'][0])
        print("1 Cluster:",clslist,"\n")
    else:
        # Convert LIST into comma separated STRING with single quotes
        clslist = ",".join(map("'{0}'".format, data['cluster']))
        clslist = clslist.strip("'[")
        print(">1 Cluster:",clslist,"\n")

    # Build SQL SELECT WHERE string
    select = "clustername IN ('" + clslist + "');"
    df_clusters = dbSelectQuery('resources',select)
    if not df_clusters.empty:
        # Check AvailMem > ReqMem, optional (AvailCpu > ReqCpu) # ,'availablecpu'
        df_clusters.sort_values(by=['availablemem'], ascending=False, inplace = True)
        target_cluster = str(df_clusters.iloc[0]['clustername']).upper()
        print("TARGET CLUSTER:",target_cluster,"\n")
        # Filter to TARGET Cluster
        df_clusters = df_clusters.loc[(df_clusters['clustername'] == target_cluster)]
        print("COMPUTE","\n", df_clusters.iloc[0][['index','clustername','availablecpu','availablemem','preferred_host']], "\n")
        df_compute = df_clusters[0:1].copy()
        dct_compute = df_compute.to_dict(orient='records')

        df_tmp = df_clusters[0:1][['clustername','preferred_host']].copy()
        resources_dict = df_tmp.to_dict(orient='records')

        print("Compute Resources","\n", resources_dict, "\n")
        response.update({ "has_compute":True, "resources":resources_dict, "compute_issues": comments, "issue_count":issues}) #, "capacity": dct_compute})
    else:
        print("COMPUTE: Compute Resources Not Found!","\n")
        comments.append("Compute capacity required in "+str(clslist))              
        issues += 1
        response.update({ "has_compute":False, "resources":resources_dict, "compute_issues": comments, "issue_count":issues, "capacity": ""})
    return df_clusters, response

# Filter clusters
def SelectStorage(job, df_cluster):
    df_datastores = pd.DataFrame()
    issues = 0
    comments = []
    response = {}

    df_cluster = df_cluster[~df_cluster['datastore_name'].str.contains('|'.join(excl_tags),na=False)] 

    # Check and Select T2 Storage
    if int(job['t2total']) != 0:
        if job['business_unit'].upper() == "SANTAM":
            if job['app_tier'].upper() in notmirrored:
                if job['app_type'].upper() == "MSSQL":
                    mirror_tag = "NotMirrored"
                else:
                    mirror_tag = "NotMirrored"
            else:
                mirror_tag = "Mirrored"            
        else:
            if job['app_tier'].upper() == "ATIII":
                mirror_tag = "Mirrored"
            else:
                mirror_tag = "NotMirrored"

        df_t2storage = df_cluster.loc[
            (df_cluster['tier'] == 2) &          \
            (df_cluster['mirrored'] == mirror_tag) &          \
            (df_cluster['availablestrg'] >= int(job['t2total']))           \
            ].copy()
        if not df_t2storage.empty:
            df_t2storage.sort_values(by=['availablestrg'], ascending=True, inplace = True)

            #print(df_t2storage.info())
            df_t2capacity = df_t2storage[0:1].copy()
            dct_t2capacity = df_t2capacity.to_dict(orient='records')
            df_t2capacity.rename(columns={'datastore_name': 't2_datastore'}, inplace=True)
            print("T2_STORAGE","\n", df_t2capacity[['index','clustername','tier','t2_datastore','availablestrg']], "\n")            
            print(dct_t2capacity,"\n")
            df_datastores = df_datastores.append(df_t2capacity)
            df_tmp = df_t2capacity[0:1][['clustername','t2_datastore']].copy()
            t2resources_dict = df_tmp.to_dict(orient='records')

            print("T2Storage Resources","\n", t2resources_dict, "\n")
            response.update({ "has_t2storage": True, "resources":t2resources_dict, "storage_issues": comments, "issue_count":issues})
        else:
            if int(job['t2total']) > dedicated:
                print("T2_STORAGE: Tier2 DEDICATED Storage Resources Not Found!","\n")
                comments.append(mirror_tag+" "+str(job['t2total'])+"GB Tier2 DEDICATED Storage capacity required in "+str(df_cluster.iloc[0]['clustername']))              
                issues += 1
            else:
                print("T2_STORAGE: Tier2 Storage Resources Not Found!","\n")
                comments.append(mirror_tag+" "+str(job['t2total'])+"GB Tier2 Storage capacity required in "+str(df_cluster.iloc[0]['clustername']))              
                issues += 1

            df_datastores["t2_datastore"] = ""
            response.update({ "has_t2storage":False })
    else:
        df_datastores["t2_datastore"] = ""
        response.update({ "has_t2storage":True})

    # Check T3 Capacity    
    if int(job['t3total']) != 0:
        
        if job['business_unit'].upper() == "SANTAM":
            if job['app_tier'].upper() in notmirrored:
                if job['app_type'].upper() == "MSSQL":
                    mirror_tag = "NotMirrored"
                else:
                    mirror_tag = "NotMirrored"     
            else:
                mirror_tag = "Mirrored"                         
        else:
            if job['app_tier'].upper() == "ATIII":
                mirror_tag = "Mirrored"
            else:
                mirror_tag = "NotMirrored"

        df_t3storage = df_cluster.loc[
            (df_cluster['tier'] == 3) &          \
            (df_cluster['mirrored'] == mirror_tag) &          \
            (df_cluster['availablestrg'] >= int(job['t3total']))           \
            ].copy()
        if not df_t3storage.empty:            
            df_t3storage.sort_values(by=['availablestrg'], ascending=True, inplace = True)
            df_t3capacity = df_t3storage[0:1].copy()
            dct_t3capacity = df_t3capacity.to_dict(orient='records')
            df_t3capacity.rename(columns={'datastore_name': 't3_datastore'}, inplace=True)
            print("T3_STORAGE","\n", df_t3capacity[['index','clustername','tier','t3_datastore','availablestrg']], "\n")            
            print(dct_t3capacity,"\n")
            df_datastores = df_datastores.append(df_t3capacity)
            df_tmp = df_t3capacity[0:1][['clustername','t3_datastore']].copy()
            t3resources_dict = df_tmp.to_dict(orient='records')

            print("T3Storage Resources","\n", t3resources_dict, "\n")

            response.update({ "has_t3storage":True, "resources":t3resources_dict, "storage_issues": comments, "issue_count":issues})
        else:
            if int(job['t3total']) > dedicated:
                print("T3_STORAGE: Tier3 DEDICATED Storage Resources Not Found!","\n")
                comments.append(mirror_tag+" "+str(job['t3total'])+"GB Tier3 DEDICATED Storage capacity required in "+str(df_cluster.iloc[0]['clustername']))              
                issues += 1
                
            else:
                print("T3_STORAGE: Tier3 Storage Resources Not Found!","\n")
                comments.append(mirror_tag+" "+str(job['t3total'])+"GB Tier3 Storage capacity required in "+str(df_cluster.iloc[0]['clustername']))              
                issues += 1

            df_datastores["t3_datastore"] = ""
            response.update({ "has_t3storage": False })
    else:
        df_datastores["t3_datastore"] = ""
        response.update({ "has_t3storage": True})

    if response['has_t2storage'] and response['has_t3storage']:
        response.update({ "has_storage": True})
    else:
        response.update({ "has_storage": False})

    #response.update({ "issues": comments, "issue_count":issues})
        
    # Return results if not empty
    if not df_datastores.empty:
        
        lst_resources = ['clustername']
        lst_dstiers = ['t2_datastore','t3_datastore']

        df_datastores = df_datastores.fillna("")
        dct_capacity = df_datastores.to_dict(orient='records')
        print("\n","Cluster Capacity:","\n", df_datastores.head(),"\n",dct_capacity,"\n")

        df_tmp = df_datastores[['clustername','preferred_host','t2_datastore','t3_datastore']].copy()
        df_resources = df_tmp.groupby(lst_resources)[lst_dstiers].sum().reset_index()
        resources_dict = df_resources.to_dict(orient='records')
        
        #print("\n","Cluster Resources:","\n", df_resources.head(),"\n",resources_dict,"\n")
        response.update({ "resources":resources_dict, "storage_issues": comments, "issue_count":issues}) #,"capacity": dct_capacity})
        #print("\n","Storage Resources Response = True:","\n", response,"\n")
        return response
    else:
        response.update({ "resources":"", "storage_issues": comments, "issue_count":issues, "capacity": ""})
        #print("\n","Storage Resources Response = False:","\n", response,"\n")
        return response

# AllocateResources
def AllocateResources(resources_data):
    print()
    print("ALLOCATING RESOURCES","\n",resources_data,"\n")
    #resources_data.pop("preferred_host")
    dbAllocateResources(resources_data)

    print("RESOURCES ALLOCATED","\n",resources_data,"\n")
    return


# Perform Resources Check
def GetResources(data):
    print("\n","GET RESOURCES:","\n", data)
    #timestamp = str(datetime.now()).split(".")[0]     
    issues = 0
    comments = []
    if data['business_unit'] != "":
        response = {}
        resources = {}
        # Populate requested resources
        resources['vcpus'] = data['vcpus']
        resources['vram'] = data['vram']
        resources['t2total'] = data['t2total']
        resources['t3total'] = data['t3total']

        df_cluster, dct_compute = SelectCluster(data)
        response.update({"compute":dct_compute }) #, "resources": resources})
        resources['has_compute'] = dct_compute['has_compute']
        response['has_compute'] = dct_compute['has_compute']

        print("AVAILABLE COMPUTE RESOURCES","\n",dct_compute,"\n")
        if dct_compute['has_compute']:
            resources.update(dct_compute['resources'][0])
            #resources['preferred_host'] = dct_compute['resources'][0]['preferred_host']
            #response.update({"resources": resources})
            # Select Storage
            dct_storage = SelectStorage(data, df_cluster)
            print("AVAILABLE STORAGE RESOURCES","\n",dct_storage,"\n")
            response.update({"storage":dct_storage })
            resources['has_storage'] = dct_storage['has_storage']
            response['has_storage'] = dct_storage['has_storage']
            #resources.update({"target_cluster" : resources['clustername']})
            #resources.update({"preferred_host" : dct_compute['resources'][0]['preferred_host']})
            #resources.pop('clustername')

            if dct_storage['has_storage']:
                resources['t2_datastore'] = dct_storage['resources'][0]['t2_datastore']
                resources['t3_datastore'] = dct_storage['resources'][0]['t3_datastore']
                comments = comments + dct_storage['storage_issues']
                issues += int(dct_storage['issue_count'])                
                resources.update(dct_storage['resources'][0])
                resources.update({'preferred_host' : dct_compute['resources'][0]['preferred_host']})
                response.update({ "issues": comments, "issue_count":issues}) # ,"resources": resources , "storage":dct_storage, , "capacity":dct_storage 
                AllocateResources(resources)

            else:
                #resources['t2_datastore'] = 0
                #resources['t3_datastore'] = 0
                resources.update({'preferred_host' : dct_compute['resources'][0]['preferred_host']})
                comments = comments + dct_storage['storage_issues']
                issues += int(dct_storage['issue_count'])
                response.update({ "issues": comments, "issue_count":issues})

            response.update({"resources": resources})
        else:
            comments = comments + dct_compute['compute_issues']     
            issues += int(dct_compute['issue_count'])
            comments.append("Manual storage allocation required.")
            issues += 1
            response.update({"has_storage": False, "issues": comments, "issue_count":issues, "capacity": ""})
    else:
        comments.append("No Business Unit Resources found.")
        issues += 1
        response.update({ "has_compute": False,"has_storage": False,"issues": comments, "issue_count":issues, "capacity": ""})

    return response