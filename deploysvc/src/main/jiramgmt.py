import sys, json, time, requests
from datetime import date, time, datetime
from src.utils import key_exists
# library modules
from jira import JIRA

import config
# Assign config data to local variable
config = config.conf()
from config import AppConfig

jobconf = {}
jirausr = AppConfig.JIRA_USR
jirapwd = AppConfig.JIRA_TKN

jira_enabled = config['JIRA']['ENABLED']
baseurl = config['JIRA']['JIRA_API_URL']
t2_tags = ["GOLD","DIAMOND","SAPHIRE"]
t3_tags = ["BRONZE","AMBER","QUARTZ","RUBY","EMERALD","VSAN","C:"]

headers = JIRA.DEFAULT_OPTIONS["headers"].copy()
headers["Authorization"] = f"Bearer {jirapwd}"

# Add Comment to ticket
def AddJiraComment(reference, comment):
    if jira_enabled:
        print(reference,"JIRA ADD COMMENT","\n")
        jira=JIRA(server=baseurl, options={"headers": headers})

        issue = jira.issue(reference)
        jira.add_comment(issue, comment)

    return

# Add Comment to ticket
def ChangeJiraStatus(reference, status):
    if jira_enabled:
        print(reference,"JIRA STATUS CHANGE")
        jira=JIRA(server=baseurl, options={"headers": headers})
        issue = jira.issue(reference)
        print(reference,"CURRENT:",issue.fields.status)
        transition_value = {}
        transitions = jira.transitions(issue, expand="transitions.fields")

        for value in transitions:
            transition_value[value['name']] = value['id']

        jira.transition_issue(issue, transition=transition_value[status])
        issue = jira.issue(reference)
        print(reference,"NEW:",issue.fields.status, "\n")
        
    return

# Add Comment to ticket
def AddJiraAttachment(reference, file):
    if jira_enabled:
        print(reference,"JIRA ADD ATTACHMENT")
        jira=JIRA(server=baseurl, options={"headers": headers})

        issue = jira.issue(reference)
        jira.add_attachment(issue, file)     
        print(reference,"ATTACHMENT:",file, "\n")
        response = { "success":True, "message": "Jira attachment uploaded"}
    else:
        response = { "success":False, "message": "Jira not enabled"}
    print(response['message'],"\n")
    return response

# Query JIRA UI Options
def JiraUI(data):

    print(data)
    with open('jiraui_v1.json', 'r') as f:
        jiraui_json = json.load(f)
    #jiraui_json
    asset_owner = str(data['asset_owner']).upper()
    dr_tier = str(data['dr_tier']).upper()
    clients = jiraui_json[asset_owner]['CLIENTS']
    competency = jiraui_json[asset_owner]['COMPETENCY']
    os_versions = jiraui_json[asset_owner]['OS_VERSIONS']
    response = jiraui_json[asset_owner][dr_tier]
    response['ASSET_OWNER'] = asset_owner
    response['CLIENTS'] = clients
    response['OS_VERSIONS'] = os_versions
    response['COMPETENCY'] = competency
    response['success'] = True
    response['status'] = "JIRA UI Selections Available"

    return response        

# Query JIRA UI Options
def JiraDeploy(data):
    #timestamp = str(datetime.now()).split(".")[0]    
    mapped = JiraRequestMap(data)
    #print("MAPPED","\n", json.dumps(mapped, indent = 2), "\n") 

    return mapped

# Map JIRA to API
def JiraRequestMap(data):
    print(json.dumps(data, indent=2),"\n")

    data['business_unit'] = data['client']
    if data['requested_by'] == "":
        data['requested_by'] = "JIRA"

    t2storage   = ""
    t2drives    = ""
    t2labels    = ""
    t3storage   = ""
    t3drives    = ""
    t3labels    = ""

    for x in range(0,8):
        diskx = "disk"+str(x+1)
        if key_exists(data,diskx):
            #print(diskx, data[diskx])
            print("Disk",diskx,":",data[diskx]['tier'], data[diskx]['size'], data[diskx]['drive'], data[diskx]['label'])
            if str(data[diskx]['tier']) != "":
                if str(data[diskx]['tier']).upper() == "GOLD":
                    t2storage   = t2storage + str(data[diskx]['size']) + ","
                    t2drives    = t2drives + str(data[diskx]['drive']) + ","
                    t2labels    = t2labels + str(data[diskx]['label']) + ","
                elif str(data[diskx]['tier']).upper() == "BRONZE":
                    t3storage   = t3storage + str(data[diskx]['size']) + ","
                    t3drives    = t3drives + str(data[diskx]['drive']) + ","
                    t3labels    = t3labels + str(data[diskx]['label']) + ","
            print(data.pop(diskx))

    t2storage   = t2storage.rstrip(',')
    t2drives    = t2drives.rstrip(',')
    t2labels    = t2labels.rstrip(',')
    t3storage   = t3storage.rstrip(',')
    t3drives    = t3drives.rstrip(',')
    t3labels    = t3labels.rstrip(',')

    print(t2storage, t3storage)
    print(t2drives, t3drives)
    print(t2labels, t3labels)

    data['t2storage'] = t2storage
    data['t2drives'] = t2drives
    data['t2labels'] = t2labels
    data['t3storage'] = t3storage
    data['t3drives'] = t3drives
    data['t3labels'] = t3labels

    poplist = ['asset_owner','client','nics']
    [data.pop(key) for key in poplist]
    print("\n",json.dumps(data, indent=2))
    return data

# Map JIRA to API
def JiraRequestMap2(data):
    print(json.dumps(data, indent=2),"\n")

    #data['business_unit'] = data['client']
    if data['requested_by'] == "":
        data['requested_by'] = "JIRA"

    t2storage   = ""
    t2drives    = ""
    t2labels    = ""
    t3storage   = ""
    t3drives    = ""
    t3labels    = ""

    for x in range(0,8):
        diskx = "disk"+str(x+1)
        if key_exists(data,diskx):
            #print(diskx, data[diskx])
            print("Disk",diskx,":",data[diskx]['tier'], data[diskx]['size'], data[diskx]['drive'], data[diskx]['label'])
            if str(data[diskx]['tier']) != "":
                if str(data[diskx]['tier']).upper() in t2_tags: # "GOLD":
                    t2storage   = t2storage + str(data[diskx]['size']) + ","
                    t2drives    = t2drives + str(data[diskx]['drive']) + ","
                    t2labels    = t2labels + str(data[diskx]['label']) + ","
                elif str(data[diskx]['tier']).upper() in t3_tags: # "BRONZE":
                    t3storage   = t3storage + str(data[diskx]['size']) + ","
                    t3drives    = t3drives + str(data[diskx]['drive']) + ","
                    t3labels    = t3labels + str(data[diskx]['label']) + ","
            print(data.pop(diskx))

    t2storage   = t2storage.rstrip(',')
    t2drives    = t2drives.rstrip(',')
    t2labels    = t2labels.rstrip(',')
    t3storage   = t3storage.rstrip(',')
    t3drives    = t3drives.rstrip(',')
    t3labels    = t3labels.rstrip(',')

    print(t2storage, t3storage)
    print(t2drives, t3drives)
    print(t2labels, t3labels)

    data['t2storage'] = t2storage
    data['t2drives'] = t2drives
    data['t2labels'] = t2labels
    data['t3storage'] = t3storage
    data['t3drives'] = t3drives
    data['t3labels'] = t3labels

    #poplist = ['client','nics']
    #[data.pop(key) for key in poplist]
    print("\n",json.dumps(data, indent=2))
    return data
