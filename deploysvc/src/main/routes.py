from flask import request, Blueprint
from flask import Flask, jsonify, abort, make_response
import json, os, base64
from rich import print

import numpy as np
from datetime import date, time, datetime, timedelta

from flask_jwt_extended import create_access_token,jwt_required, get_jwt_identity

from src.main.deploymgmt import DeployRequest, GetRequestStatus, getBuildSpecs
from src.main.namegen import getNextName, getName, IssueNextName
#from src.main.ipam2 import SubnetInfo, NextFreeIP
from src.main.configsref import UpdateConfigData, GetHostingConfig
from src.main.resources import UpdateResources, GetResources, UpdateInfraview
#from src.main.confmgmt import GetConfigReference, ValidateRequest
#from src.main.confmgmt2 import HostingConfig2 # ValidateRequest2, GetConfigReference2
from src.main.capacity2 import RefreshCapacityData, CheckCapacity #, GetResourcesData # CapacityCheck, UpdateResources, 
from src.main.jobspecs import GetJobLogs, ProcessRequest , ValidateRequest3, UpdateRequest, GetNextJob, GetAllJobs, UpdateJob, UpdateJobLog, UpdateCompliance, UpdateQuality
from src.main.dbmgmt import dbAddUsers, Users
from src.main.decommmgmt import AddDecommRequest, GetAllDecommJobs, UpdateDecommJob, ManageDecommJobs
from src.main.jiramgmt import ChangeJiraStatus, JiraUI, JiraDeploy, AddJiraComment, AddJiraAttachment, JiraRequestMap2
from src.main.dcsquote import DcsQuote, UpdateRates
#import src.main.dbmgmt 
import config
# Assign config data to local variable
config = config.conf()

from src import bcrypt
basedir = os.path.abspath(os.path.dirname(__file__))
restarted = datetime.now()
up_since = str(restarted).split(".")[0]
jira_enabled = config['JIRA']['ENABLED']
quotes_enabled = bool(config['AUTODEPLOY']['QUOTES_ENABLED'])

main_bp = Blueprint('main',__name__)

class NpEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, np.integer):
            return int(obj)
        if isinstance(obj, np.floating):
            return float(obj)
        if isinstance(obj, np.bool_):
            return super().encode(bool(obj))
        if isinstance(obj, np.ndarray):
            return obj.tolist()
        if isinstance(obj, np.datetime_data):
            return str(obj)
        return super(NpEncoder, self).default(obj)

# Add service user
@main_bp.route("/user/add", methods=["POST"])
@jwt_required()
def add_user():
    data = request.json
    #username = request.json.get("username", None)
    #data['password'] = bcrypt.generate_password_hash(data['password'])
    data['password'] = bcrypt.generate_password_hash(str(data['password']).encode('utf-8')) #, bcrypt.gensalt())
    data['active'] = True

    #print(json.dumps(data, indent=2),"\n")
    print(data,"\n")
    dbAddUsers(data)

    status = {
        'success' : True,
        'message' : "New user added"
    }
    print(status,"\n")
    return make_response(jsonify(status),201)


# Issue token
@main_bp.route("/token", methods=["POST"])
def create_token():
    data = request.json
    username = data['username']
    #password = bcrypt.check_password(data['password'])
    # Query your database for username and password
    user = Users.query.filter_by(username=username).first()
    if user: 
        #if bcrypt.checkpw(user.password.encode('utf-8'), bcrypt.gensalt()):
        if bcrypt.check_password_hash(user.password.encode('utf-8'), data['password']):
            expires = timedelta(days=int(config['DEPLOYSVC']['TOKEN_EXPIRE_DAYS']))
            # create a new token with the user id inside
            access_token = create_access_token(identity=user.id, expires_delta=expires)
            return jsonify({ "token": access_token, "user_id": user.id })
        else:
            return jsonify({"msg": "Password failed!"}), 401  
    else:
        # the user was not found on the database
        return jsonify({"msg": "Authentication failed!"}), 401

# Root end point - just for service status
@main_bp.route("/", methods=['GET','POST'])
def main():
    data = request.json
    print(data)
    uptime = str(datetime.now() - restarted).split(".")[0]
    status = {
        'status': "Online", 
        'online' : True,
        'uptime' : uptime,
        'up_since': up_since, 
        'message' : "Deployment API Service is online."
    }
    print("\n","API SERVICE STATUS:")
    print(json.dumps(status, indent=2),"\n")   
    return make_response(jsonify(status),201)

# Root end point - just for service status
@main_bp.route("/protected", methods=['POST'])
@jwt_required()
def protected():
    data = request.json
    print(data)
    uptime = str(datetime.now() - restarted).split(".")[0]
    status = {
        'status': "Online", 
        'online' : True,
        'uptime' : uptime,
        'up_since': up_since, 
        'message' : "Protected: Deployment API Service is online."
    }
    
    return make_response(jsonify(status),201)

# Process New Deployment Request
@main_bp.route("/newrequest", methods=['GET','POST'])
def Test():
    jobrequest = request.json
    print("\n")
    print("JOB REQUEST")    
    print(jobrequest,"\n")  
      
    request_data = ProcessRequest(jobrequest)    
    # Validate Request
    #jobrequest_status = ValidateRequest3(jobrequest)
    print("REQUEST_RESPONSE","\n",json.dumps(request_data, sort_keys=True),"\n")
    if request_data['request_valid']:
        # Process Valid Request
        #request_status = ProcessRequest(jobrequest)
        #return make_response(jsonify({'request_data': request_data}),201)  
        return make_response(json.dumps(request_data, sort_keys=True,cls=NpEncoder),201)
    else:  
        # Reject Invalid Request
        return make_response(json.dumps(request_data, sort_keys=True, cls=NpEncoder),202)      


# Validate Deployment Request
@main_bp.route("/validate", methods=['POST'])
def VerifyRequest():
    job = request.json
    print("REQUEST","\n",job, "\n")    
    request_status = ValidateRequest3(job)
    print("VALIDATION","\n",request_status, "\n")    
    return make_response(jsonify({'request_status': request_status}),201)    

# Root end point - just for service status
@main_bp.route("/updaterequest", methods=['POST'])
def updaterequest():
    data = request.json
    #print(data)
    response = UpdateRequest(data)
    
    return make_response(jsonify(response),201)

# Root end point - just for service status
@main_bp.route("/getname", methods=['POST'])
def getname():
    data = request.json
    response = getName(data)
    #response = IssueNextName(data)
    print("NAME_RESULT:","\n",json.dumps(response, indent=2),"\n")    
    return make_response(json.dumps(response,sort_keys=True,cls=NpEncoder),201)

# Request next free name
@main_bp.route("/namerequest", methods=['POST'])
def namerequest():
    data = request.json

    response = getNextName(data)
    print("NAME_RESULT:","\n",json.dumps(data, indent=2),"\n")    
    return make_response(json.dumps(response['name_data'], sort_keys=True,cls=NpEncoder),201)


# Root end point - just for service status
@main_bp.route("/updatejob", methods=['POST'])
def updatejob():
    data = request.json
    #print(data)
    response = UpdateJob(data)    
    return make_response(jsonify(response),201)

# Root end point - just for service status
@main_bp.route("/updatejoblog", methods=['POST'])
def updatejoblog():
    data = request.json
    #print(data)
    response = UpdateJobLog(data)    
    return make_response(jsonify(response),201)

# Root end point - just for service status
@main_bp.route("/getnextjob", methods=['POST'])
def getnextjob():
    data = request.json
    #print(data)
    response = GetNextJob(data)   
    return make_response(jsonify(response),201)

# Root end point - just for service status
@main_bp.route("/getalljobs", methods=['POST','GET'])
#@jwt_required()
def getalljobs():
    data = request.json
    print(data)
    response = GetAllJobs(data)   
    return make_response(jsonify(response),201)

# Root end point - just for service status
@main_bp.route("/getjoblogs", methods=['POST'])
#@jwt_required()
def getjoblogs():
    data = request.json
    print(data)
    response = GetJobLogs(data)   
    return make_response(jsonify(response),201)


# Obtain capacity info
@main_bp.route("/capacity", methods=['GET'])
def capacity():
    data = request.json
    print("CAPACITY CHECK")

    #capacitydata = CapacityCheck(CheckCapacity) 

    capacitydata = CheckCapacity(data) 
    print("CAPACITY RESPONSE","\n", capacitydata, "\n") 

    return make_response(capacitydata,201)
     

# Update capacity info
@main_bp.route("/refreshcapacity", methods=['GET','POST'])
def updatecapacity():
    data = request.json
    print(data) 
    #print("CAPACITY CHECK")
    capacitydata = RefreshCapacityData() 
    capacitydata["reference"] = data["reference"]
    capacitydata["updated_by"] = data["requested_by"]

    print("CAPACITY RESPONSE","\n", json.dumps(capacitydata, indent = 2), "\n")    
    return make_response(jsonify(capacitydata),201)

#'''  
# Update capacity info
@main_bp.route("/updateconfigs", methods=['POST'])
def updateconfigs():
    #data = request.json
    #print(data) 
    print("UPDATE CONFIGs:","\n")
    status = UpdateConfigData()

    print("RESOURCES RESPONSE","\n", json.dumps(status, indent = 2), "\n")    
    return make_response(jsonify(status),201)
#'''
#'''  
# Update capacity info
@main_bp.route("/updateinfraview", methods=['GET','POST'])
#@jwt_required()
def updateinfraview():
    #data = request.json
    #print(data) 
    print("\n","UPDATING INFRAVIEW:","\n")
    status = UpdateInfraview()

    print("UPDATE INFRAVIEW RESPONSE","\n", json.dumps(status, indent = 2), "\n")    
    return make_response(jsonify(status),201)
#'''
#'''  
# Update capacity info
@main_bp.route("/updateresources", methods=['GET','POST'])
#@jwt_required()
def updateresources():
    #data = request.json
    #print(data) 
    print("\n","UPDATE RESOURCES:","\n")
    status = UpdateResources()

    print("UPDATE RESOURCES RESPONSE","\n", json.dumps(status, indent = 2), "\n")    
    return make_response(jsonify(status),201)
#'''

#'''  
# Update capacity info
@main_bp.route("/updatecompliance", methods=['POST'])
#@jwt_required()
def updatecompliance():
    data = request.json
    print(data) 
    print("\n","UPDATE COMPLIANCE:","\n")
    status = UpdateCompliance(data)

    print("UPDATE COMPLIANCE RESPONSE","\n", json.dumps(status, indent = 2), "\n")    
    return make_response(jsonify(status),201)
#'''

#'''  
# Update capacity info
@main_bp.route("/updatequality", methods=['POST'])
#@jwt_required()
def updatequality():
    data = request.json
    print(data) 
    print("\n","UPDATE QUALITY:","\n")
    status = UpdateQuality(data)

    print("UPDATE QUALITY RESPONSE","\n", json.dumps(status, indent = 2), "\n")    
    return make_response(jsonify(status),201)
#'''

# POST Decomm Request
@main_bp.route("/getdecommjobs", methods=['POST'])
def getdecommjobs():
    data = request.json

    result = GetAllDecommJobs(data)

    print(json.dumps(result, indent=2),"\n")  
    return make_response(jsonify(result),201)

# POST Decomm Request
@main_bp.route("/updatedecommjob", methods=['POST'])
def updatedecommjob():
    data = request.json

    result = UpdateDecommJob(data)

    print(json.dumps(result, indent=2),"\n")  
    return make_response(jsonify(result),201)

# POST Decomm Request
@main_bp.route("/managedecommjobs", methods=['POST'])
def managedecommjobs():

    result = ManageDecommJobs()

    print(json.dumps(result, indent=2),"\n")  
    return make_response(jsonify(result),201)


#'''  
# Update capacity info
@main_bp.route("/gethosting", methods=['POST'])
#@jwt_required()
def gethosting():
    data = request.json
    print("\n","GET JOBSPEC RESOURCES","\n")
    print(data) 
    #print("UPDATE RESOURCES:","\n")
    hosting = GetHostingConfig(data)
    print(hosting) 

    print("\n","HOSTING RESPONSE","\n", json.dumps(hosting, indent = 2), "\n")    
    return make_response(jsonify(hosting),201)
    #else:
    #    return make_response(jsonify(results),401)
#'''

#'''  
# Update capacity info
@main_bp.route("/getresources", methods=['POST'])
#@jwt_required()
def getresources():
    data = request.json
    print("\n","GET JOBSPEC RESOURCES","\n")
    print(data) 
    #print("UPDATE RESOURCES:","\n")
    hosting = GetHostingConfig(data)
    print(hosting) 
    results = GetResources(hosting['response'])
    #print(results) 

    print("\n","HOSTING RESPONSE","\n", json.dumps(results, indent = 2), "\n")    
    return make_response(jsonify(results),201)
    #else:
    #    return make_response(jsonify(results),401)
#'''

# JIRA DEPLOYMENT end point - to populate JIRA Form Options
@main_bp.route("/v1/autodeploy", methods=['POST'])
def autodeploy():
    data = request.json
   
    requested = data.copy()
    
    response = DeployRequest(data)
    response['requested'] = requested
    print("RESPONSE: ",response['success'],"\n")
    #print(json.dumps(response, indent = 2, sort_keys=True), "\n") 
    if response['success']:     
        #print("RESPONSE: SUCCESS","\n", json.dumps(response, indent = 2, sort_keys=True), "\n")    
        return make_response(jsonify(response),201)
    else:
        #print("RESPONSE: FAILED","\n", json.dumps(response, indent = 2, sort_keys=True), "\n")
        return make_response(jsonify(response),400)                 

# JIRA DEPLOYMENT end point - to populate JIRA Form Options
@main_bp.route("/v1/requeststatus", methods=['POST','GET'])
def requeststatus():
    #data = request.json
    try:
        print("API: requeststatus, POST")
        data = request.json
    except:
        print("API: requeststatus, GET")
        data = request.args.to_dict()

    #requested = data.copy()
    response = GetRequestStatus(data)
    #response['requested'] = requested
    print("RESPONSE: ",response['success'],"\n")
    #print(json.dumps(response, indent = 2, sort_keys=True), "\n") 
    if response['success']:     
        #print("RESPONSE: SUCCESS","\n", json.dumps(response, indent = 2, sort_keys=True), "\n")    
        return make_response(jsonify(response),201)
    else:
        #print("RESPONSE: FAILED","\n", json.dumps(response, indent = 2, sort_keys=True), "\n")
        return make_response(jsonify(response),400) 

# JIRAUI end point - to populate JIRA Form Options
@main_bp.route("/v1/buildspecs", methods=['POST','GET'])
def buildspecs():
    
    try:
        print("API: BUILDSPECS, POST")
        data = request.json
        response = getBuildSpecs(data)
    except:
        print("API: BUILDSPECS, GET")
        data = request.args.to_dict()
        response = getBuildSpecs(data)
    # finally:
    #     response = { "success":False, "status":"Query error"}

    return make_response(jsonify(response),201)

# JIRA DEPLOYMENT end point - to populate JIRA Form Options
@main_bp.route("/v1/autodecomm", methods=['POST'])
def autodecomm():
    data = request.json
    response = {}
   
    requested = data.copy()
    response.update(AddDecommRequest(data))
    response['request'] = requested
    #print("RESPONSE","\n")
    
    if response['success']:     
        print("RESPONSE: SUCCESS","\n", json.dumps(response, indent = 2, sort_keys=True), "\n")    
        return make_response(jsonify(response),201)
    else:
        print("RESPONSE: FAILED","\n", json.dumps(response, indent = 2), "\n")
        return make_response(jsonify(response),400)                 


# JIRAUI end point - to populate JIRA Form Options
@main_bp.route("/v1/jiraui", methods=['GET','POST'])
def jiraui_v1():
    #data = request.json
    try:
        data = request.args.to_dict()
        response = JiraUI(data)
    except:
        response = { "success":False, "status":"Query error"}

    return make_response(jsonify(response),201)

# JIRA DEPLOYMENT end point - to populate JIRA Form Options
@main_bp.route("/v1/jiradeploy", methods=['POST'])
def jiradeploy():
    data = request.json
    response = {}
    response['requested'] = data.copy()
    timestamp = str(datetime.now()).split(".")[0]  
    response['timestamp'] = timestamp
    # Map JIRA request to current database schema
    mapped = JiraDeploy(data)

    if jira_enabled:
        result = DeployRequest(mapped)
        response.update({"result":result})
        if quotes_enabled:
                quotedata = {}
                quotemap = JiraRequestMap2(data)
                quotedata['data'] = quotemap
                quote = DcsQuote(quotedata)
                response.update({"quote":quote})

    try:        
        response['mapped'] = mapped
        response['success'] = True
        #print("RESPONSE","\n", json.dumps(response, indent = 2), "\n") 
        return make_response(jsonify(response),201)        
    except:
        response["success"]  = False
        #print("RESPONSE","\n", json.dumps(response, indent = 2), "\n") 
        return make_response(jsonify(response),400)

# JIRA DEPLOYMENT - Add Attachment
@main_bp.route("/v1/jiradeploy/comment", methods=['POST'])
def jira_comment():
    data = request.json
    print(json.dumps(data, indent = 2), "\n")
    print(data['comment'])
    AddJiraComment(str(data['reference']).upper(),str(data['comment']))
    
    response = { "success":True, "status":"Comment for JIRA update received."}

    return make_response(jsonify(response),201)

# JIRA DEPLOYMENT - Add Attachment
@main_bp.route("/v1/jiradeploy/attachment", methods=['POST'])
def jira_attachment():
    data = request.json
    print(json.dumps(data, indent = 2), "\n")
    print(data['file'])
    AddJiraAttachment(str(data['reference']).upper(),str(data['file']))
    
    response = { "success":True, "status":"File data received"}

    return make_response(jsonify(response),201)

# JIRA DEPLOYMENT end point - to populate JIRA Form Options
@main_bp.route("/v2/jira/deploy", methods=['POST'])
def jira_deploy():
    data = request.json
    response = {}
    response['requested'] = data.copy()
    timestamp = str(datetime.now()).split(".")[0]  
    response['timestamp'] = timestamp
    # Map JIRA request to current database schema
    mapped = JiraDeploy(data)
    response['data'] = mapped
    result = DcsQuote(response)
    #result = DeployRequest(mapped)
    #response.update({"result":result})
    if jira_enabled:
        pass

    try:        

        response['success'] = True
        print("RESPONSE","\n", json.dumps(response, indent = 2), "\n") 
        return make_response(jsonify(response),201)        
    except:
        response["success"]  = False
        print("RESPONSE","\n", json.dumps(response, indent = 2), "\n") 
        return make_response(jsonify(response),400)

# Update DCS Rates
@main_bp.route("/updaterates", methods=['GET','POST'])
#@jwt_required()
def updaterates():
    print("\nUPDATE DCS RATES\n")
    status = UpdateRates()
    print("UPDATE RATES RESPONSE","\n", json.dumps(status, indent = 2), "\n")    
    return make_response(jsonify(status),201)

# JIRA DEPLOYMENT end point - to populate JIRA Form Options
@main_bp.route("/v2/jira/dcsquote", methods=['POST'])
def dcsquote():
    data = request.json
    response,quotedata = {},{}
    response['requested'] = data.copy()
    timestamp = str(datetime.now()).split(".")[0]  
    #response['timestamp'] = timestamp
    # Map JIRA request to current database schema
    quotemap = JiraRequestMap2(data)
    quotedata['data'] = quotemap
    quote = DcsQuote(quotedata)
    response.update({"result":quote})
    if jira_enabled:
        pass
    try:        
        response['success'] = True
        #print("RESPONSE","\n", json.dumps(response, indent = 2), "\n") 
        return make_response(jsonify(response),201)        
    except:
        response["success"]  = False
        #print("RESPONSE","\n", json.dumps(response, indent = 2), "\n") 
        return make_response(jsonify(response),400)

