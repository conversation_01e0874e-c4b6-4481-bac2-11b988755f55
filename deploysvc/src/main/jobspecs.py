import sys, json, requests #time,
from datetime import date, time, datetime
import logging
# Import pandas
import pandas as pd
import numpy as np

from src.utils import key_exists
# Import Config Reference Data
from src.main.confmgmt2 import GetConfigReference2
# Import Available Resources Data
from src.main.capacity2 import CapacityData, StorageCapacity, CheckCapacity, GetClusters
# Import NameGen module
from src.main.namegen import GetName
# Import IPAM module
#from src.main.ipam2 import NextFreeIP
# Import Database module
from src.main.dbmgmt import dbGetJobLogs, dbAddJobLog, dbUpdateJobLog, dbAddRequest, dbUpdateRequest, dbAddJob, dbUpdateJob, dbGetNextJob, dbGetAllJobs, dbUpdateCompliance, dbUpdateQuality

import config
# Assign config data to local variable
config = config.conf()
from config import AppConfig

for_testing = config['NAMEGEN']['FOR_TESTING']

df_request = pd.DataFrame()
df_confdata = pd.DataFrame()
df_cpctmgmt = pd.DataFrame()
df_hosting = pd.DataFrame()
df_compcpct = pd.DataFrame()
df_compute = pd.DataFrame()
df_hosting = pd.DataFrame()
df_storage = pd.DataFrame()
df_computeconf = pd.DataFrame()
df_osconf = pd.DataFrame()
df_jobspec = pd.DataFrame()
df_requested = pd.DataFrame()
df_available_cmpt = pd.DataFrame()
df_available_strg = pd.DataFrame()
df_available = pd.DataFrame()
df_capacity = pd.DataFrame()
df_resources = pd.DataFrame()
tgt_compute = pd.DataFrame()
tgt_hosting = pd.DataFrame()
tgt_hostconf = pd.DataFrame()
tgt_osconfig = pd.DataFrame()
tgt_capacity = pd.DataFrame()
dict_requested = {}
job_specs = {}
job_hostconf = {}
job_osconf = {}
job_capacity = {}
job_storage = {}
tgt_hostconfig = {}

updates = {}

clusters = []
job_keys_remove = ['vm_count','compute_status','aci_tenant','disk_type','disk_blocksize','t2storage_status','t3storage_status','active','ad_domain','anp','bridged_domain','client_tag','active_status','comment','compute_ok','cluster','tenant','request_id','cpu_sockets','cpu_ratio','epg','index','ip_ok','max_vcpu','max_vram','os_edition','os_disksize','requested_by','sla','storage_ok','storage_status','t2storage_ok','t3storage_ok']

class NpEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, np.integer):
            return int(obj)
        if isinstance(obj, np.floating):
            return float(obj)
        if isinstance(obj, np.bool_):
            return super().encode(bool(obj))
        if isinstance(obj, np.ndarray):
            return obj.tolist()
        return super(NpEncoder, self).default(obj)
# Validate Deployment Request
def ValidateRequest3(job):
    print("REQUEST STATUS")
    request_status = {}
    issues = 0
    comments = []

    if str(job['reference']) != "":
        job['reference'] = str(job['reference']).upper()

    if "Windows" in str(job["os_version"]):
        is_windows_image = True
        job['os_type'] = "Windows"
        # Drives names to UPPERCASE
        if str(job['t2drives']) != "":
            job['t2drives'] = str(job['t2drives']).upper()
        if str(job['t3drives']) != "":
            job['t3drives'] = str(job['t3drives']).upper()        
    elif "Linux" in str(job["os_version"]):
        is_windows_image = False
        job['os_type']  = "Linux"
    else:
        is_windows_image = False
        job['os_type']  = "Other"


    # Check Non-Production is marked as ATV.        
    if str(job["environment"]).upper() == "PRODUCTION": 
        if str(job["app_tier"]).upper() == "ATV":
            comments.append(str(job["environment"]).upper() + " environment DR Tier must NOT be ATV!")
            issues += 1       
        else:
            issues += 0

    # Check Production is not marked as ATV.
    if str(job["environment"]).upper() != "PRODUCTION":
        if str(job["app_tier"]).upper() != "ATV":
            comments.append(str(job["environment"]).upper() + " environment cannot be DR Tier " + str(job["app_tier"]).upper())              
            issues += 1   
        else:
            issues += 0
 
    # Check user not requesting T2 Storage for ATV.
    if str(job["app_tier"]).upper() == "ATV":
        if str(job["t2storage"]).upper() != "":            
            comments.append("Tier-2 Storage not available for ATV, request separately.")       
            issues += 1   
        else:
            issues += 0

    # Check multiples of 2 instances requested for ATI and ATII.
    if str(job["app_tier"]).upper() == "ATI" or str(job["app_tier"]).upper() == "ATII":
        if int(job["vm_count"]) % 2 != 0:            
            comments.append(str(job["app_tier"]).upper() +" requires vm_count in multiples of 2 vm instances!")       
            issues += 1   
        else:
            issues += 0
            job['vm_placement']  = "DR Balanced"
            #comments.append("Location is based on DR capability.")  
    else:
        issues += 0
        job['vm_placement']  = "Capacity Balanced"   
        #comments.append("Location is based on available capacity.")         

    #vm_names = []
    # Check if VM Name was supplied, split into list object else set to blank.
    if int(job["vm_count"]) > 1:
        try:
            if job["vm_name"] != "":
                vm_names = str(job["vm_name"]).split(',')
                print("VM NAMES",vm_names, "\n")
        except ValueError as e:
            comments.append(str(int(job["vm_count"])) +" vm names required or leave blank to generate.")       
            issues += 1 
    else:
        issues += 0

    # Update response data. 
    if issues == 0:
        request_status["request_valid"] = True
        request_status["request_status"] = "READY"
        request_status["request_phase"] = "DEPLOYMENT"
        comments.append("Deployment request is valid.") 
        request_status["request_comments"] = comments    

        #request_status["job_request"] = job
        request_status.update(job)
    else:
        request_status["request_valid"] = False      
        request_status["request_status"] = "REJECTED"
        request_status["request_phase"] = "VALIDATION"
        comments.append("Deployment request is invalid.")     
        request_status["request_comments"] = comments    

    #request_status["request_issues"] = issues
    
    return request_status

# Retrieve All Available Resources
def AvailableResources(cls,job): #(job, clusters):
    # Load Available Resources data
    global df_available

    print("Available Resources in", cls)
    df_resources = df_available[df_available["clustername"].isin(cls)]
    df_compute = df_resources.copy()

    # dropping ALL duplicate values
    df_compute.drop_duplicates(subset="clustername", inplace = True)
    #print(df_compute.info(),"\n")

    #print("Records:", len(df_resources), "\n")

    if len(df_resources) != 0:
        df_resources.set_index(['clustername','preferred_host'])
        df_resources.sort_values(by=['availablecpu','availablemem','availablestorage'], ascending=False, inplace = True)
        df_compute.set_index(['clustername','preferred_host'])
        df_compute.sort_values(by=['availablecpu','availablemem','availablestorage'], ascending=False, inplace = True)

        print("JOB ","\n", job,"\n")
        #try:
        print("AVAIALABLE COMPUTE","\n")
        print(df_compute[['clustername','availablecpu','availablemem','preferred_host']],"\n")

        print("CHECK FREE vCPUs and vRAM","\n")
        job["job_comments"] = ""  
        #if df_compute.iloc[0][df_compute['availablecpu'] >= int(job['vcpus'])]:
        if df_compute.iloc[0]['availablecpu'] >= int(job['vcpus']) and df_compute.iloc[0]['availablemem'] >= int(job['vram']):
            compute_ok = True
            job["compute_ok"] = compute_ok            
                
            job_cls = str(df_compute.iloc[0]['clustername']).upper()
            job["cluster"] = job_cls
            job["preferred_host"] = str(df_compute.iloc[0]['preferred_host']).lower()

            #job.update(job_capacity)            
            print("INDEX", df_compute.index[0])
            #df_resources.loc[df_resources['clustername']==job_cls,"availablecpu"] -= int(job['vcpus'])
            #df_resources.loc[df_resources['clustername']==job_cls,"availablemem"] -= int(job['vram'])
            df_resources.at[df_compute.index[0],'availablecpu'] -= int(job['vcpus'])
            df_resources.at[df_compute.index[0],'availablemem'] -= int(job['vram'])
        else:
            compute_ok = False           
            job["compute_ok"] = compute_ok                          
            job["compute_status"] = "vCPUs and-or vRAM Capacity Insufficient!"     
            #job["cluster"] = "REMEDIATION"                
        #'''
        if compute_ok:
            job_cls = str(df_resources.iloc[0]['clustername']).upper()

            print("JOB for STORAGE", "\n")
            print(job, "\n")
            
            job_storage = json.loads(StorageCapacity(job, df_resources))
            #print(job_storage, "\n")
            
            
            job.update(job_storage)

            if job_storage['storage_ok']:
                # SUBTRACT REQUESTED STORAGE FROM DATASTORES
                if job_storage['t2storage_ok']:
                    job_storage['t2storage_status'] = ""
                    if job['t2storage'] != "":    
                            print(df_resources.loc[df_resources['datastore_name']==job_storage["t2_datastore"],["datastore_name","availablestorage"]])
                            df_resources.loc[df_resources['datastore_name']==job_storage["t2_datastore"],"availablestorage"] -= int(job_storage['t2storage_total'])
                            print(df_resources.loc[df_resources['datastore_name']==job_storage["t2_datastore"],["datastore_name","availablestorage"]], "\n")
                if job_storage['t3storage_ok']:
                    job_storage['t3storage_status'] = ""
                    if job['t3storage'] != "":
                        print(df_resources.loc[df_resources['datastore_name']==job_storage["t3_datastore"],["datastore_name","availablestorage"]])
                        df_resources.loc[df_resources['datastore_name']==job_storage["t3_datastore"],"availablestorage"] -= int(job_storage['t3storage_total'])
                        print(df_resources.loc[df_resources['datastore_name']==job_storage["t3_datastore"],["datastore_name","availablestorage"]], "\n")
                #print(job_storage)
            else:
                print("STORAGE FAILED",job_storage, "\n")
            #print("TYPE",type(job))
            print(job_storage, "\n")            
            #job.update(job_storage[0])

        #'''

            #print(df_resources[['clustername','availablecpu','availablemem','datastore_name','tier','availablestorage','mirrored','preferred_host']],"\n")
    else:
        job["compute_status"] = "No Hosting Resources Found!"
        job["compute_ok"] = False        

    return job

# Retrieve All Available Resources
def GetResources(cls,req,job): #(job, clusters):
    available_capacity = {}
    available_resources = {}
    print("Available Resources in", cls, "\n")
    print("CAPACITY REQUEST","\n",job)

    job["app_tier"] = req["app_tier"]    
    job["vcpus"] = req["vcpus"]
    job["vram"] = req["vram"]
    # If ATIII-V , get best Cluster based on Capacity
    if req["app_tier"] != "ATI" or req["app_tier"] != "ATII":
        df_hosting = GetClusters(cls)
        job["clustername"] = df_hosting.iloc[0]['clustername']

    t2_total = 0
    t3_total = 0

    if "ATV" in req["app_tier"].upper():
        t3_total = t3_total + int(job['os_disksize'])
    else:
        if req['t2storage'] == "" and req['t3storage'] == "":
            t2_total = t2_total + int(job['os_disksize'])
        elif req['t2storage'] != "":
            t2_total = t2_total + int(job['os_disksize'])
        else:
            t3_total = t3_total + int(job['os_disksize'])     

    # Process Tier 2 Storage Request
    if req['t2storage'] != "":          
        #print("ST2:",job['t2storage'])
        t2_disks = []
        t2_drives = []        
        try:
            t2_list = req['t2storage'].split(",")            
            t2_total = t2_total + sum([int(x) for x in t2_list])
            available_resources["t2storage_disks"] = list(map(int,t2_list))
            t2_drives = req['t2drives'].split(",")            
            #print("T2 TOTAL:",t2_total)                 
        except ValueError:
            t2_disks.append(int(job['t2storage']))
            t2_total = t2_total + int(job['t2storage'])
            #print("T2 TOTAL:",t2_total)
            t2_drives.append(job['t2drives'])
        
        job["t2total"] = t2_total

        available_resources["t2storage_total"] = t2_total
        available_resources["t2storage_drives"] = t2_drives
        available_resources["t2storage_disks"] = str(req['t2storage'])
    else:
        job["t2total"] = t2_total
        job["t2storage_drives"] = ""
        job["t2storage_disks"] = ""
        job["t2storage_total"] = ""

    # Process Tier 3 Storage Request
    if req['t3storage'] != "":          
        #print("ST3:",job['t3storage'])
        t3_disks = []
        t3_drives = []        
        try:
            t3_list = req['t3storage'].split(",")            
            t3_total = t3_total + sum([int(x) for x in t3_list])
            available_resources["t3storage_disks"] = list(map(int,t3_list))
            t3_drives = req['t3drives'].split(",")            
            #print("T3 TOTAL:",t3_total)                 
        except ValueError:
            t3_disks.append(int(job['t3storage']))
            t3_total = t3_total + int(job['t3storage'])
            #print("T3 TOTAL:",t3_total)
            t3_drives.append(job['t3drives'])
        
        job["t3total"] = t3_total

        available_resources["t3storage_total"] = t3_total
        available_resources["t3storage_drives"] = t3_drives
        available_resources["t3storage_disks"] = str(req['t3storage'])
    else:
        job["t3total"] = t3_total
        job["t3storage_drives"] = ""
        job["t3storage_disks"] = ""
        job["t3storage_total"] = ""

    print("\n", "CAPACITY SPECS","\n",job)

    available_capacity = CheckCapacity(job)

    available_capacity.update(available_resources)

    print("RESOURCES", "\n", available_capacity, "\n")

    return available_capacity

def GetNextName(name_request):
    print("NAME REQUEST","\n",name_request,"\n")
    if name_request['environment'] == "POC":
        app_type = name_request['environment']
    else:
        app_type = name_request['app_type']

    name_spec = {
        "business_unit": name_request['business_unit'],
        "app_type": app_type,
        "environment": name_request['environment'],
        "os_version": name_request['os_version'],
        "request_jobid": name_request['reference'],
        "vm_count": 1,
        "is_virtual": 1,
        "for_testing": for_testing,
        "vm_description": name_request['reference'],
        "requested_by": name_request['reference']
    }
    name_result = GetName(name_spec)
    name_issued = name_result['vm_name']

    return name_issued

#
def AddJobLog(data_dict):
    today = str(datetime.now()).split(".")[0]
    logdata = {
        "log_added" : today,
        "request_jobid" : data_dict['request_jobid'],
        "vm_name" : data_dict['vm_name'],
        "job_stage" : data_dict['job_stage'],
        "log_type" : data_dict['log_type'],
        "job_comments" : data_dict['job_comments'],
        "log_status" : "OPEN",
        "log_updated" : today,
        "log_update" : str(today) + " New issue added",
        "automatic" : False
    }
    dbAddJobLog(logdata)
    return

# Update Request Status
def UpdateJobLog(data):
    updated = str(datetime.now()).split(".")[0]
    print("UPDATE_JOBLOG DATA","\n", json.dumps(data, indent=2),"\n")
    logdata = {
        "joblogid" : data['joblogid'],
        "log_type" : data['log_type'],
        "log_status" : str(data['log_status']).upper(),
        "log_updated" : updated,
        "log_update" : updated +" "+ data['log_update'] +"\n"
    }
    updates = dbUpdateJobLog(logdata)
    #updates["update_result"] = "successful"
    #updates["update_ok"] = True

    return updates

# Obtain Configuration Reference Data
def ProcessRequest(jobrequest):

    def AddIssue(buildjob):

        logdata = {
            "log_added" : buildjob['start_datetime'],
            "request_jobid" : buildjob['request_jobid'],
            "vm_name" : buildjob['vm_name'],
            "job_stage" : "VM_DEPLOY",
            "log_type" : "ISSUE",
            "job_comments" : buildjob['job_comments'],
            "log_status" : "OPEN",
            "log_updated" : buildjob['start_datetime'],
            "log_update" : buildjob['start_datetime'] + " New issue added",
            "issue_type": "",
            "issue_ref" : "",
            "automatic" : False
        }
        dbAddJobLog(logdata)
        return

    # VALIDATE REQUEST
    jobrequest_status = ValidateRequest3(jobrequest)
    jobrequest_status["requested"] = jobrequest
    print(jobrequest_status,"\n")
    if not jobrequest_status['request_valid']:
        # Reject Invalid Request
        
        return jobrequest_status     
 
    else:          # Process Valid Request        # Process Valid Request


        # Process Valid Request
        # SET REQUEST STATE = JOBSPECS 
        jobrequest_status["request_status"] = "NEW" 
        #jobrequest_status["request_state"] = "JOBSPECS" 

        #print(type(jobrequest_status))

        # GET HOSTING AND OS CONFIGs, FILTER TO BU+AT+OS+APP
        df_hosting,df_osconf = GetConfigReference2(jobrequest)
        df_hosting.set_index(["business_unit","cluster","ip_cidr"], inplace = True)
        df_hosting.reset_index(inplace = True)
        print("HOSTING")           
        print(df_hosting, "\n")

        df_osconf.set_index(["business_unit"], inplace = True)
        df_osconf.reset_index(inplace = True)
        
        print("OSCONFIG")        
        #print(df_osconf, "\n") 

        # Load Available Resources data
        global df_available
        
        # CHECK COUNT more than 0
        if len(df_hosting) != 0:
  
            #jobrequest_status["request_state"] =  date.today()
                 
            print("REQUEST STATUS = ",jobrequest_status["request_status"],"\n") 

            # GET OS DISKSIZE 
            if len(df_osconf) != 0:
                jobrequest['os_disksize'] = int(df_osconf.iloc[0]['os_disksize']) 

            print("Hosting Configs Found:", len(df_hosting))
            clusters = df_hosting["cluster"].to_list()

            #print("CLUSTERS:",clusters,"\n")
            # Load Available Resources
            df_capacity = CapacityData()
            #print(df_capacity.info())            
            df_available = df_capacity[df_capacity["clustername"].isin(clusters)]
            print("CLUSTERS AVAILABLE:",clusters,"\n")
            jobconf = {}
            jobs = []
            vm_names = []
            # Check if VM Name was supplied, split into list object else set to blank.
            try:
                vm_names = str(jobrequest["vm_name"]).split(',')
                # Check VM Names supplied matches vm count required, else make blank.
                if len(vm_names) != int(jobrequest["vm_count"]):
                    jobrequest["vm_name"] = ""                    
            except ValueError as e:
                jobrequest["vm_name"] = ""
                print("VM Name error ", e, "\n")

            sitenum = 0
            

            if str(jobrequest["app_tier"]).upper() == "ATI" or str(jobrequest["app_tier"]).upper() == "ATII":
                print("BUILDS REQUESTED:", int(jobrequest["vm_count"]), "\n")
                for x in range(0, int(jobrequest["vm_count"])):
                    print("SITE:", sitenum, "\n")
                    clstr = []                    
                    jobid = str(jobrequest["reference"]).upper() + "_" + str(x+1)
                    print("JOBID_#"+str(x+1), " = ", jobid)

                    #dict_requested = jobrequest.copy()
                    clstr.append(str(df_hosting.iloc[sitenum]['cluster']).upper())
                    print("CLSTR", clstr, "\n")

                    #print("JOBSPEC BEFORE", jobconf, "\n") 
                    jobconf.update({'job_comments':  ""})
                    jobconf['request_jobid'] = jobid 
                    jobconf['reference'] = str(jobrequest["reference"]).upper()        
                     
                    # Check if VM Name was supplied, else generate dummy from JobID
                    if jobrequest["vm_name"] != "":
                        jobconf["vm_name"] = str(str(vm_names[x]).strip()).upper()
                    else:
                        jobconf["vm_name"] = jobid 
                    print("\n", "TARGET JOBSPEC","\n",jobconf) 
                    jobconf['vm_description'] = str(jobrequest["vm_description"])

                    #'''
                    #tgt_compute = df_hosting.loc[df_hosting['cluster'] == str(jobconf['cluster']).upper()]
                    tgt_hosting = df_hosting.iloc[sitenum]
                    #tgt_hosting.set_index(["business_unit"], inplace = True)
                    tgt_hosting.at[tgt_hosting.index[0],'business_unit'] = jobrequest['business_unit']
                    #print("TARGET HOSTING","\n",df_hosting.info(),"\n")
                    job_hostconf = tgt_hosting.to_dict()                     
                    jobconf.update(job_hostconf)

                    tgt_osconfig  = df_osconf.iloc[0]
                    #print("TARGET OSCONFIG","\n",df_osconf.info(),"\n")
                    job_osconf = tgt_osconfig.to_dict()                          
                    jobconf.update(job_osconf)
 
                    print("\n", "TARGET JOBSPEC","\n",jobconf) 
                    '''
                    ipdata = NextFreeIP(jobconf,"set")
                    #jobconf.update(ipdata)

                    if ipdata['data'] != "":
                        jobconf['ip_ok'] = True
                        jobconf['ip_address'] = ipdata['data']
                    else:
                        jobconf['ip_ok'] = False
                        jobconf['ip_address'] = "" 
                    '''

                    #job_capacity = AvailableResources(clstr, jobrequest)
                    # Retrieve from Capacity database table
                    job_capacity = GetResources(clstr, jobrequest, jobconf)

                    jobconf.update(job_capacity)

                    # If Compute Capacity OK, ADD ADDITIONAL HOSTING and OSCONFIG SPECs
                    if  job_capacity["compute_ok"]:
                        if job_capacity['storage_ok']:                                   
                            jobconf["job_state"] = "NORMAL"     
                        else:      
                            jobconf["job_state"] = "REMEDIATE"    
                            jobconf['job_comments'] = jobconf['job_comments'] + "; " + job_capacity["capacity_comments"]                                    
                    else:
                        #print("JOBSPEC AFTER","\n", json.dumps(jobconf, indent=2), "\n")   
                        jobconf["job_state"] = "REMEDIATE"
                        jobconf['job_comments'] = jobconf['job_comments'] + "; " + job_capacity["capacity_comments"]
                    
                    jobconf["job_stage"] = "PENDING" 
                    jobconf['ip_maskbits'] = int(jobconf['ip_maskbits'])
                    jobconf['vlan_tag_bare_metal'] = int(jobconf['vlan_tag_bare_metal'])
                    jobconf['os_disksize'] = int(jobconf['os_disksize'])

                    #'''
                    #print("JOB_REQUEST_JOBSPECS","\n", jobconf)
                    jobconf['request_jobid'] = jobid    
                    jobconf['app_tier'] = str(jobrequest['app_tier']).upper()
                    jobconf['app_type'] = str(jobrequest['app_type'])
                    jobconf['request_id'] = str(jobrequest['reference'])
                    jobconf['environment'] = str(jobrequest['environment'])

                    jobconf['t2storage'] = str(jobrequest['t2storage'])
                    jobconf['t2drives'] = str(jobrequest['t2drives'])
                    jobconf['t2storage_disks'] = str(jobconf['t2storage_disks'])
                    jobconf['t2storage_drives'] = str(jobconf['t2storage_drives'])
                    jobconf['t3storage'] = str(jobrequest['t3storage'])
                    jobconf['t3drives'] = str(jobrequest['t3drives'])
                    jobconf['t3storage_disks'] = str(jobconf['t3storage_disks'])
                    jobconf['t3storage_drives'] = str(jobconf['t3storage_drives'])                        

                    #'''
                    jobconf["target_cluster"] = jobconf["clustername"]
                    # Switch to other hosting location reference data
                    if sitenum == 0:
                        sitenum = 1
                    else:
                        sitenum = 0 
                    pop_list = []
                    pop_list = ['clustername','sla','t2storage_total','t3storage_total','cpu_ratio','max_vcpu','max_vram','aci_tenant','anp','bridged_domain','epg','comment','os_edition','capacity_comments']
                    print([jobconf.pop(key) for key in pop_list])

                    # Append Job to Build List
                    dict_requested = jobconf.copy()
                    jobs.append(dict_requested)
                         
            #'''        
            else:
                print("BUILDS REQUESTED:", int(jobrequest["vm_count"]), "\n")
                for x in range(0, int(jobrequest["vm_count"])):
                    #print("SITE:", sitenum, "\n")
                    clstr = []                    
                    jobid = str(jobrequest["reference"]).upper() + "_" + str(x+1)
                    print("JOBID_#"+str(x+1), " = ", jobid)

                    #dict_requested = jobrequest.copy()
                    clstr.append(str(df_hosting.iloc[sitenum]['cluster']).upper())
                    print("CLSTR", clstr, "\n")

                    #print("JOBSPEC BEFORE", jobconf, "\n") 
                    jobconf.update({'job_comments':  ""})
                    jobconf['request_jobid'] = jobid 
                    jobconf['reference'] = str(jobrequest["reference"]).upper()      

                    tgt_osconfig  = df_osconf.iloc[0]
                    #print("TARGET OSCONFIG","\n",df_osconf.info(),"\n")
                    job_osconf = tgt_osconfig.to_dict()                          
                    jobconf.update(job_osconf)

                    #job_capacity = AvailableResources(clstr, jobrequest)
                    # Retrieve from Capacity database table
                    job_capacity = GetResources(clstr, jobrequest, jobconf)
                    jobconf['request_id'] = str(jobrequest["reference"]).upper()                    
                    jobconf['vm_description'] = str(jobrequest["vm_description"])

                    if jobrequest["vm_name"] != "":
                        job_capacity["vm_name"] = str(str(vm_names[x]).strip()).upper()
                    else:                         
                        job_capacity["vm_name"] = GetNextName(jobrequest)       


                    dict_requested = job_capacity.copy()         
                    jobconf.update(dict_requested)

                    #print("ALL HOSTING","\n",tgt_hosting.head(),"\n")

                    print("\n", "TARGET JOBSPEC","\n",jobconf, "\n") 
                    print("BUSINESS UNIT:", jobrequest['business_unit'], "\n")

                    #'''
                    
                    print("TARGET CLUSTER","\n",str(job_capacity['clustername']).upper(), "\n") 
                    tgt_hosting = df_hosting.loc[df_hosting['cluster'] == str(job_capacity['clustername']).upper()]
                    print("TARGET HOSTING","\n",tgt_hosting.head(),"\n")
                    #tgt_hosting = df_hosting.loc[job_capacity["clustername"]]
                    #tgt_hosting.set_index(["business_unit"], inplace = True)
                    
                    tgt_hosting.at[tgt_hosting.index[0],'business_unit'] = jobrequest['business_unit']
                    print("TARGET HOSTING Head","\n",tgt_hosting.head(),"\n")
                    tgt_hostconf =  tgt_hosting.iloc[0]
                    tgt_hostconfig = tgt_hostconf.to_dict()   
                    print("TARGET HOSTING Config","\n",tgt_hostconfig,"\n")                  
                    jobconf.update(tgt_hostconfig)

                    print("\n", "TARGET JOBSPEC Config","\n",jobconf) 

                    # Set dhcp ip address request result to ""
                    if str(jobconf['ip_cidr']).lower() == "dhcp":
                        jobconf['ip_ok'] = True
                        jobconf['ip_address'] = "dhcp"         
                    else:             
                        #tgt_compute = pd.merge(tgt_hosting, tgt_osconfig, how="inner", left_on='business_unit', right_on='business_unit')
                        #'''
                        ipdata = NextFreeIP(jobconf,"set")
                        print("IP DATA","\n",ipdata)
                        #jobconf.update(ipdata)

                        if ipdata['data'] != "":
                            jobconf['ip_ok'] = True
                            jobconf['ip_address'] = ipdata['data']
                        else:
                            jobconf['ip_ok'] = False
                            jobconf['ip_address'] = "" 
                        #'''

                    # If Compute Capacity OK, ADD ADDITIONAL HOSTING and OSCONFIG SPECs
                    if  job_capacity["compute_ok"]:
                        if job_capacity['storage_ok']:                                   
                            jobconf["job_state"] = "NORMAL"     
                        else:      
                            jobconf["job_state"] = "REMEDIATE"    
                            jobconf['job_comments'] = job_capacity["capacity_comments"] + "; " + jobconf['job_comments']
                    else:
                        #print("JOBSPEC AFTER","\n", json.dumps(jobconf, indent=2), "\n")   
                        jobconf["job_state"] = "REMEDIATE"
                        jobconf['job_comments'] = job_capacity["capacity_comments"] + "; " + jobconf['job_comments']
                    
                    jobconf["job_stage"] = "PENDING" 

                    #print("JOB_REQUEST_JOBSPECS","\n", jobconf)
                    jobconf['request_jobid'] = jobid    
                    jobconf['app_tier'] = str(jobrequest['app_tier']).upper()
                    jobconf['app_type'] = str(jobrequest['app_type'])
                    jobconf['request_id'] = str(jobrequest['reference'])
                    jobconf['environment'] = str(jobrequest['environment'])
                    jobconf['ip_maskbits'] = int(jobconf['ip_maskbits'])
                    jobconf['vlan_tag_bare_metal'] = int(jobconf['vlan_tag_bare_metal'])
                    jobconf['os_disksize'] = int(jobconf['os_disksize'])

                    jobconf['t2storage'] = str(jobrequest['t2storage'])
                    jobconf['t2drives'] = str(jobrequest['t2drives'])
                    jobconf['t2storage_disks'] = str(jobconf['t2storage_disks'])
                    jobconf['t2storage_drives'] = str(jobconf['t2storage_drives'])
                    jobconf['t3storage'] = str(jobrequest['t3storage'])
                    jobconf['t3drives'] = str(jobrequest['t3drives'])
                    jobconf['t3storage_disks'] = str(jobconf['t3storage_disks'])
                    jobconf['t3storage_drives'] = str(jobconf['t3storage_drives'])        

                    jobconf["target_cluster"] = jobconf["clustername"]               

                    #'''
                    pop_list = []
                    pop_list = ['clustername','sla','t2storage_total','t3storage_total','cpu_ratio','max_vcpu','max_vram','aci_tenant','anp','bridged_domain','epg','comment','os_edition','capacity_comments']
                    print([jobconf.pop(key) for key in pop_list])

                    # Append Job to Build List
                    dict_requested = jobconf.copy()
                    jobs.append(dict_requested)

            #'''
            # Set Request Submit DateTime
            today = str(datetime.now()).split(".")[0]
            print("\n","Submit DateTime:",	datetime.fromisoformat(today.split(".")[0]), "\n") #datetime.fromisoformat(date_string)
            #'''
            print("\n","=====================","\n")
            if str(jobrequest["app_type"]).upper() == "VDI":
                print("KEY EXISTS:",key_exists(jobrequest,"submit_datetime"),"\n")
                if key_exists(jobrequest,"submit_datetime"):
                    jobrequest_status.update({ "submit_datetime" :  str(jobrequest["submit_datetime"]) })
                else:
                    jobrequest_status.update({ "submit_datetime" :  today }) 
            else:
                jobrequest_status.update({ "submit_datetime" :  today }) 

            remediations = len(list(filter(lambda job_state: job_state['job_state'] == 'REMEDIATE', jobs)))
            
            print("REMEDIATION COUNT:", remediations)
            if remediations == 0:
                jobrequest_status["request_status"] = "READY" 
            else:
                jobrequest_status["request_status"] = "REMEDIATE" 
            jobrequest_status["request_comments"] = str(jobrequest_status["request_comments"])
            print(jobrequest_status,"\n")
            print("=====================","\n")
            # Add Request to Database
            jobrequest_status.pop('requested')
            dbAddRequest(jobrequest_status)
            # Add Build Jobs to Database
            for job in jobs:
                print(job, "\n")
                if key_exists(jobrequest_status,"submit_datetime"):
                    job.update({ "start_datetime" :  str(jobrequest_status["submit_datetime"]) }) 
                else:
                    jobrequest_status.update({ "start_datetime" :  today }) 
                
                job.update({ "updated_datetime" :  today }) 
                job.update({ "job_active" :  False }) 
                job.update({ "job_stage" :  "AD_OBJECT" }) 
                job.update({ "job_status" :  "PENDING" })    

                for key in job_keys_remove:
                    try:
                        del job[key]
                    except:
                        pass    

                dbAddJob(job)
                # Add VM_DEPLOY  Remediation Issues to JobLog. 
                if job['job_state'] == "REMEDIATE":
                    AddIssue(job)
            #'''
            jobrequest_status["jobspecs"] = jobs
        else:
            #logging.error("Failed to Commit because of {error}. Doing Rollback".format(error=e))
            jobrequest_status["request_comments"] = "No hosting configurations found!"
            jobrequest_status["request_status"] = "DECLINED"
            jobrequest_status["request_valid"] = False

        #print("REQUEST RESULT","\n", json.dumps(jobrequest_status, indent=2),"\n")

        return jobrequest_status

# Update Request Status
def UpdateRequest(req_update):

    print("UPDATE_REQUEST DATA","\n", json.dumps(req_update, indent=2),"\n")

    updates = dbUpdateRequest(req_update)
    #updates["update_result"] = "successful"
    #updates["update_ok"] = True

    return updates

# Update Request Status
def UpdateJob(data):

    print("UPDATE_JOB DATA","\n", json.dumps(data, indent=2),"\n")

    updates = dbUpdateJob(data)
    #updates["update_result"] = "successful"
    #updates["update_ok"] = True

    return updates

# Update Request Status
def GetNextJob(req_update):

    print("GET_NEXT_JOB DATA","\n", json.dumps(req_update, indent=2, sort_keys=True),"\n")

    updates = dbGetNextJob(req_update)

    return updates

# GetAllJobs in Queue
def GetAllJobs(jobsquery):
    dct_builddata = {}
    print("GET_ALL_JOBs DATA","\n", json.dumps(jobsquery, indent=2, sort_keys=True),"\n")

    df_buildjobs = dbGetAllJobs(jobsquery)
    num_rows = df_buildjobs.shape[0]
    if num_rows > 0:
        #df_buildjobs['updated_datetime'] = pd.to_datetime(df_buildjobs['updated_datetime'], errors='coerce').fillna(0)
        df_buildjobs['updated_datetime'] = df_buildjobs['updated_datetime'].astype(str)
    dct_buildjobs = df_buildjobs.to_dict(orient='records')
    dct_builddata.update({"count" : num_rows})
    dct_builddata.update({"jobs" : dct_buildjobs})
    dct_builddata.update({"query" : jobsquery})
    #print("REQUEST RESULT","\n", json.dumps(dct_builddata, indent=2),"\n")
    
    return dct_builddata

# GetAllJobs in Queue
def GetJobLogs(jobsquery):
    dct_builddata = {}
    print("GET_JOB_LOGs DATA","\n", json.dumps(jobsquery, indent=2, sort_keys=True),"\n")

    df_buildjobs = dbGetJobLogs(jobsquery)
    num_rows = df_buildjobs.shape[0]
    if num_rows > 0:
        #df_buildjobs['updated_datetime'] = pd.to_datetime(df_buildjobs['updated_datetime'], errors='coerce').fillna(0)
        df_buildjobs['log_added'] = df_buildjobs['log_added'].astype(str)
        df_buildjobs['log_updated'] = df_buildjobs['log_updated'].astype(str)
    dct_buildjobs = df_buildjobs.to_dict(orient='records')
    dct_builddata.update({"count" : num_rows})
    dct_builddata.update({"joblogs" : dct_buildjobs})
    dct_builddata.update({"query" : jobsquery})
    print("REQUEST RESULT","\n", json.dumps(dct_builddata, indent=2),"\n")
    
    return dct_builddata

# Update Request Status
def UpdateCompliance(req_update):

    print("UPDATE_COMPLIANCE DATA","\n", json.dumps(req_update, indent=2),"\n")
    dbUpdateCompliance(req_update)
    updates["update_result"] = "successful"
    updates["update_ok"] = True

    return updates

# Update Request Status
def UpdateQuality(req_update):

    print("UPDATE_QUALITY DATA","\n", json.dumps(req_update, indent=2),"\n")
    dbUpdateQuality(req_update)
    updates["update_result"] = "successful"
    updates["update_ok"] = True

    return updates