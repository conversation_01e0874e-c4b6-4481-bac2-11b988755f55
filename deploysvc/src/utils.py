import os, requests, json
from rich import print
import subprocess
from subprocess import call
#import urllib3
from requests_ntlm import HttpNtlmAuth
from io import BytesIO

import pandas as pd
import numpy as np
import xlrd
# Import openpyxl
from openpyxl import Workbook
from openpyxl import load_workbook
from openpyxl.utils.dataframe import dataframe_to_rows

basedir = os.path.abspath(os.path.dirname(__file__))
datapath = os.path.join(basedir,"data")

# Check if URL is downloadable
def is_downloadable(url):
    h = requests.head(url, allow_redirects=True)
    header = h.headers
    content_type = header.get('content-type')
    return True

# Check if Key exists in Dictionary
def key_exists(dict_obj,key):
    if key in dict_obj:
        return True
    else:
        return False

# Download the file contents in binary format
def urlrequest(url):
    if is_downloadable(url):
        # download the file contents in binary format
        resp = requests.get(url)
        return resp

# Get JSON
def get_jsondata(url):
    response = urlrequest(url)
    jsondata = json.loads(response.content)
    return jsondata

# Load csv to dataframes
def loadcsv(filename,sep):
    # read csv into dataframe named 'addm'
    df = pd.read_csv(filename, engine="python", sep=sep, encoding='latin1', quotechar='"', error_bad_lines=False)
    
    return df

# Load and append worksheets to dataframes
def load_xlsx(url):

    print("Loading", url)
    wb = load_workbook(filename=BytesIO(get_data_from_url(url)))
    return wb

# Import data from CSV file 
def csv2df(csvfile,rskip, sep):
    """ read csv data from a local file """
    print('Opening csv data source...')   
    # Read the file
    csvdata = pd.read_csv(csvfile,skiprows=rskip, sep=sep, escapechar='\\', low_memory=False, error_bad_lines=False)
    print('CSV source read...')
    # Header clean up -  in case there are multiple white spaces
    print(csvdata.info())
    return csvdata

# Import data from xlsx
def get_xlsx2df(url):
    print("Downloading Excel Data File.")
    xlsxdata = pd.read_excel(url,engine='openpyxl',sheet_name=None)
    #print(xlsxdata)
    return xlsxdata	

# Import data from xlsx with Auth
def get_xlsx2df_auth(url,username,password):
    print("Downloading Excel Data File.")
    print("Source:", url)
    #xlsxdata = pd.read_excel(url,engine='openpyxl',sheet_name=None)
    r = requests.get(url,auth=HttpNtlmAuth(username,password), verify=False)
    xlsxdata = pd.read_excel(BytesIO(r.content),engine='openpyxl',sheet_name=None)
    #print(xlsxdata.info())
    return xlsxdata
	
# Download data from url
def get_data_from_url(url):

    h = requests.head(url, allow_redirects=True)
    print("Reading data from source.")
    resp = requests.get(url, stream=True)
    data = resp.text

    return data

# Build query string
def BuildAndQuery(data):
    print(data)
    qry = ""
    for k,v in data.items():
        if v != "":
            qry = qry + "lower("+ str(k).lower() + ") like '%" + str(v).lower() + "%' and "
    qry = qry.rstrip(" and ") + ";"

    return qry

# printing result 
def BuildUpdateQuery(data):
    qry = ""
    for k,v in data.items():
        if v != "":
            string = str(k)+"='"+str(v)+"',"
            #print(string)
            #print(k,"='"+v+"',")
            qry = qry + string
    qry = qry.rstrip(',')

    print(qry)
    return qry

# Recast data types for json
def recast_json(df):
    for column, dt in zip(df.columns, df.dtypes):
        if dt.type not in [
            np.int64, np.float_, np.bool_,
        ]:
            df.loc[:, column] = df[column].astype(str)
    return df

# Convert docx to pdf
def Docx2Pdf(infile):
    #infile = fname+".docx"
    print(f"CONVERT TO PDF\n")    
    print(f"DOC IN: {infile}")
    outdir = os.path.split(infile)[0] #+ r"/"
    print(f"DIR OUT: {outdir}")
    outfile = str(infile).rsplit(".")[0] +".pdf"
    print(f"PDF OUT: {outfile}")
    #call(f"soffice --convert-to pdf {infile}", shell=True)
    #call(f"soffice --convert-to pdf {infile}", shell=True)
    convert_to_pdf = rf"soffice --headless --convert-to pdf {infile} --outdir {outdir}"
    subprocess.run(convert_to_pdf, shell=True)
    return outfile
